{"config": {"indexing": "full", "lang": ["en"], "min_search_length": 3, "prebuild_index": false, "separator": "[\\s\\-]+"}, "docs": [{"location": "", "text": "Super up Intro Super up is a full social chat app clone from whatsapp cupertino ui support ( Android , ios , windows , web , macOS ) Current the admin panel under build you will receive it as updates for free in this project but you can install the current version of it technologies Flutter for clint side last version or v 3.13.7 Nodejs v17.9.1 or later in the backend side Nestjs socket-io for real time management mongodb for data storage v v 5.* or v 6.* only Some backend tech Bearer token JWT ,ORM mongoose , agora-io for video and voice calls All media saved on the server on your vps server Google maps for share chat location mailer for send reset password OTP firebase-admin for push notifications geoip-lite and request-ip for detect user country :::tip Firebase This project not user firebase to save any data it only use the free firebase FCM for push notifications only! No for firebase high cost! ::: requirements flutter last-sdk installed in your local machine nodejs , nestjs , mongodb installed in your vps server vps server should be at lest 2 cpu and 2GB ram and enough ssd storage at lest 20 GB you can check out this companies digitalocean , hostens make sure you have bandwidth more than 1TB Domain name it can be from namecheap firebase account for handle push notifications Optional agora-account if you intent to enable voice and video calls Optional onesignal account if you intent to push notifications over onesignal instead of firebase Optional google-api-key this key used for search and let users able to send locations in chat Optional google-ads-banner keys get banner id for ios and android if you want to enable it Optional For run ios you need paid apple developer account (99$) per year after purchasing (codecanyou) You will get all files for flutter project (android,ios,web,macos,windows) You will get the flutter admin panel project Postman collection for all apis Full nestjs code which its backend code Full support for future updates Free support for bug fixes only (there is paid support) You can use this project to modify and edit it serve your Customers ! :::tip VCHAT SDK This project use v-chat-sdk under the hood to serve the chat part This will not require you to purchase the v-chat-sdk it already impeded inside the source code if you are looking to implement a chat system into your exists or new flutter app then you should purchase v-chat-sdk ::: :::tip Contact me Iam offer paid full server side setup ::: Flutter project structure in split packages To run the project first activate melos dart pub global activate melos Then run this commend in the root of the project melos bs this code will run pub get for you in all packages we have the following apps ( super_up_admin ) this is the admin panel apps ( super_up_app ) this app you should run it packages all needed packages the most important packages is the super_up_core the s_constants.dart in the lib/src Lest explain packages s_translate contains all arb files for super_up_app , super_up_admin projects super_up_core contains all logic for super up app itself like shared http requests,utils,widgets (DONT UPDATE) v_chat_firebase contains the firebase FCM only logic (DONT UPDATE) v_chat_input_ui this contains the ui and logic for the message input (recorder,file selector,location picker) v_chat_media_editor this contains the ui of media editor v_chat_message_page this contains the ui & logic of message page v_chat_room_page this contains the ui & logic of chat page v_chat_sdk_core contains all logic for v_chat app itself like shared http requests, socket,notifications (DONT UPDATE) Change app name,package,logo each app must have unique package name so you must change it! to be accepted in the stores this step should be done first before connect firebase we will use this package rename install by flutter pub global activate rename to change package name flutter pub global run rename --bundleId com.vchatsdk.vnotestarter Example => pub global run rename --bundleId com.XXXXXXX.XXXXXX to rename the app use flutter pub global run rename --appname \"YOUR APP NAME\" to update logo just update the logo.png in assets folder the logo must be .png then run the code in the super_up_app flutter pub run flutter_launcher_icons:main then this command dart run flutter_native_splash:create Connect firebase Recommend to user firebase flutter CLI which its new tool to make the firebase base connect straightforward android ios You can connect it easily be firebase follow up this video CLI tool SConstants This file inside packages in [super_up_core] in lib/src/s_constants.dart This file contains the configuration of the app abstract class SConstants { ///your super up base domain url ///like this (example.com) not start https// or any sub domains example [superupdev.com] or server ip with port like [12.xxx.xxx:80] /// case of you use server ip just put the server ip connected to the port [http://ip:port] static const _productionBaseUrl = \"superupdev.online\"; ///your app name static const appName = \"Superup\"; ///android and ios admob ids [https://developers.google.com/admob/flutter/quick-start] [https://developers.google.com/ad-manager/mobile-ads-sdk/flutter/quick-start] static const androidAdUnitId = \"ca-app-pub-3940256099942544/6300978111\"; static const iosAdUnitId = \"ca-app-pub-3940256099942544/2934735716\"; ///setup video and voice calls [https://agora.io] static const agoraAppId = \"------------------------\"; ///change this to your google maps api key to enable google maps location picker static const googleMapsApiKey = \"AIzaSyAP---------------------\"; ///update this url to share the app for others static const googlePlayUrl = \"https://play.google.com/store/apps/details?id=com.app.superup\"; static const appleStoreUrl = \"https://testflight.apple.com/join/F4tAbW5J\"; ///get the onesignal id for push notifications [https://onesignal.com] static const oneSignalAppId = \"********-****-****-****-**************\"; ///don't update update only if you use server ip just return your server ip with port [12.xxx.xxx:80/] static String get baseMediaUrl { ///if you dont setup domain yet you can return the server ip like this [return Uri.parse(\"http://ip:port/\");] return \"https://api.$_productionBaseUrl/\"; } ///don't update update only if you use server ip just return your server ip with port [12.xxx.xxx:80/api/v1] static Uri get sApiBaseUrl { ///if you dont setup domain yet you can return the server ip like this [return Uri.parse(\"http://ip:port/api/v1\");] return Uri.parse(\"https://api.$_productionBaseUrl/api/v1\"); } } Add more language You can add new language by just open the [s_translation] package And inside lib/i18n create new file its name should follow the Standard of intl_short language code.arb .arb Inside this file copy the intl_en.arb and translate only the values and dont touch the key of the map Then, while you run the app, the new language will be added, and it will appears in the selections Ads get the ids from these urls get banner id for ios and android android and ios admob ids quick-start mobile-ads-sdk dont forget to update the android android/app/src/main/AndroidManifest.xml APPLICATION_ID not unit id be careful <meta-data android:name=\"com.google.android.gms.ads.APPLICATION_ID\" android:value=\"ca-app-pub-YOUR KEY HERE!\"/> put your android appId for ads here android:value= for Ios update the ios/Runner/Info.plist <key>GADApplicationIdentifier</key> <string>ca-app-pub-YOUR KEY HERE!</string> Agora.io Create agora app and enable it copy the appId and enable Setup video and voice calls agora Static const agoraAppId = \"------------------------\"; enable the Secondary Certificate Google api key Get google api key and enable all options like search and GEO locations for android and ios Don't forget to update the android android/app/src/main/AndroidManifest.xml <meta-data android:name=\"com.google.android.geo.API_KEY\" android:value=\"YOUR API_KEY HERE!\"/> and for ios ios/Runner/AppDelegate.swift GMSServices.provideAPIKey( YOUR API_KEY HERE! ) Notifications iOS (APNS) Login to your Apple Developer Account,(if you don’t have, you need to create one to be able to test, publish ios app). Navigate to Certificates, Identifiers & Profiles > Keys > Add New from the left menu. Register a New key. Set the key name as FirebaseAPNS . Tick “Apple Push Notification Services (APNs)” Click Continue to register it. Once the key is registered follow below steps: Copy the Team ID (keep it aside) Download the Auth key File (keep it aside) Copy the Key ID (keep it aside) Click “Done” to complete. Open Firebase Dashboard > Project Settings Click on “Cloud Messaging” tab. For the iOS app, click “Upload”. Click on “Browse” to upload the recently downloaded Auth key file. Paste the Key ID Paste the Team ID Click upload to register it. All done now with ios notifications how to update theme For dark theme use it already inside the main.dart just update it dont delete it! Of course you can update the theme direct from the code if you have much experience First message page darkTheme: ThemeData( extensions: [ VMessageTheme.dark().copyWith( ///see options! ), ], ), for light theme use theme: ThemeData( extensions: [ VMessageTheme.dark().copyWith( ///see options! ), ], ), second Chats Room page for dark theme use darkTheme: ThemeData( extensions: [ VRoomTheme.light().copyWith( ///see options! ), ], ), for light theme use theme: ThemeData( extensions: [ VRoomTheme.light().copyWith( ///see options! ), ], ), one signal Get the onesignal id for push notifications [https://onesignal.com] see flutter docs then in apps/super_up_app/lib/v_chat_v2/v_chat_config.dart enable OneSignal push by add this constructor don't forget to update the app id in SConstants.oneSignalAppId vPush: VPush( enableVForegroundNotification: true, vPushConfig: const VLocalNotificationPushConfig(), ///if you support fcm push notifications fcmProvider: VChatFcmProver(), ///if you support OneSignal push notifications **THIS** oneSignalProvider: VChatOneSignalProver( appId: SConstants.oneSignalAppId, ), ), :::fin If firebase is available into your country its recommended to use it for notifications push which its 100% free service with no limits! ::: Admin panel super up introduce admin panel to control the app there are two types of login admin login and viewer login you will have two passwords for the admin one for admin another for viewer admin can do anything viewer can only see the data he cant edit it you can set the password for admin and viewer from the .env.production file in the backend files this file is hidden #Admin panel passwords be carfaul ControlPanelAdminPassword= \"xxxxxxxxxxxxx\" # put strong password for admin who can edit and update any thing in the app ControlPanelAdminPasswordViewer= \"xxxxxxxxxx-xxxx\" # put strong password for admin that can only read(see ,users data,chats data etc...) he cannot update any thing You can change it any tine you want to re deploy your app! publish open terminal inside the super_up_app folder for android, you can run flutter build apk --split-per-abi for store publish for web, you can run flutter build web --web-renderer html see backend section for how to upload for ios, you can run ios :::danger Doesn't update any package version unless you know what to do ::: Backend (Nodejs) Requirements Person who has experience to deal with ubuntu server and deploy otherwise you can contact me iam offer best upload experience Install Node.js (version v16.x or to 19.x ) and npm. Check the Node.js version using node -v . Install only if you not docker user cross-env npm i -g cross-env for managing production or development environments and pm2 for manage production deploy by npm i -g pm2 CLI ( npm install -g @nestjs/cli ). Install MongoDB (minimum v4.4 , recommended v6 ). .env file # you can out local url or docker url or url of mongo in another service DB_URL=\"YOUR_MONGO_URL\" # Dont update it ever after you set it! if you do all users will logout!!! JWT_SECRET=\"STRONG_PASSWORD\" # Dont update it ever after you set it! if you do all users will logout!!! issuer=\"your gmail\" # Dont update it ever after you set it! if you do all users will logout!!! audience=\"your gmail\" NODE_ENV=\"production\" # dont update it EDIT_MODE =\"false\" # set to false ignoreEnvFile=\"false\" # set to true if you will inject the env values from system os PORT=80 //exposed port of node js #Admin panel passwords be carfaul ControlPanelAdminPassword= \"xxxxxxxxxxxxx\" # put strong password for admin who can edit and update any thing in the app ControlPanelAdminPasswordViewer= \"xxxxxxxxxx-xxxx\" # put strong password for admin that can only read(see ,users data,chats data etc...) he cannot update any thing isOneSignalEnabled =\"false\" # set to true if you can provide oneSignalAppId and oneSignalApiKey to enable push by onesignal isFirebaseFcmEnabled =\"false\" # set to true if you provide the firebase admin.json file #set onesignal data if you support it by set isOneSignalEnabled to true oneSignalAppId=\"xxxxxxxxx-xxxxx-xxxxx-xxxx-xxxxxxxxx\" #set onesignal data if you support it by set isOneSignalEnabled to true oneSignalApiKey=\"xxxxxxxxx\" # SET THE Email data to let use use forget password OTP EMAIL_HOST=\"EMAIL HOST FROM THE PROVIDER COMPANY\" EMAIL_USER=\"YOUR EMAIL USER\" EMAIL_PASSWORD=\"EMAIL USER PASSWORD\" # SET AGORA API KEYS AGORA_APP_ID=\"\" # Get this from agora app console `Primary Certificate` value AGORA_APP_CERTIFICATE=\"\" privacy-policy You can update your privacy privacy page in the source code inside http://localhost:3000/privacy-policy.html this will be the privacy-policy of your app You can edit it inside public/privacy-policy.html You can edit the home inside public/home.html you can access it http://localhost:3000 firebase admin file Make sure the firebase account is the same as the one used in flutter app. To ensure chat notifications work properly, follow this video to obtain the firebase.adminsdk.json file. Replace the existing firebase.adminsdk.json file with your new one. open your account in firebase then enable the Cloud Messaging API (Legacy) from Obtaining OneSignal Keys Create a Firebase account and follow this video to obtain OneSignal keys. Update the following fields with your OneSignal keys: oneSignalAppId=\"xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxx\" oneSignalApiKey=\"xxxxxxxxxxxx\" Running the Code (Without Docker) You should be familiar with ubuntu server setup Install ubuntu server v 20.* and later Open a terminal in the backend root folder. Run npm i or npm i --force if issues occur. Generate a dist folder npm run build . Run npm run start:prod for live console logs for just make sure your server is configure well if you run in production mode. in your vps server then you should then stop and run this run pm2 start ecosystem.config.js --only normal --env production .for background serve to see logs run pm2 logs If you see app run in production, your code is production-ready. Access the development server at localhost:80 and production server at port 80 Update the port in .env.production if necessary. Update the PORT in the .env file if using Docker, and ensure you update the environment variable in the OS (env). you need to install nginx and connect domain to your server for security and more speed! dont forget to add the websocket support for nginx while you configure it inside the location of your server block don't forget to add this to enable websocket support proxy_set_header Upgrade $http_upgrade; proxy_set_header Connection \"upgrade\"; proxy_http_version 1.1; proxy_set_header Host $host; proxy_set_header X-Real-IP $remote_addr; proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for; proxy_set_header X-Forwarded-Proto $scheme; Deploy web run the flutter code to build the web version inside the super_up_app folder run this code flutter build web --web-renderer html now you can find the html folder inside the build folder you need to upload it to your server your server can your vps server you can use nginx to deploy your web code and admin code Deploy admin run the flutter code to build the web version inside the super_up_admin folder run this code flutter build web --web-renderer html now you can find the html folder inside the build folder you need to upload it to your server your server can your vps server you can use nginx to deploy your web code and admin code Running the Code (With Docker) Install Docker and Docker Compose. update .env.production this keys DB_URL= from mongodb://127.0.0.1:27017/super_up to DB_URL=************************************************************************* Run the Dockerfile if Docker is already installed on your system. Note that this only sets up v_chat_sdk and doesn't include MongoDB or Redis. You need to manage these separately OR. Use a compose file witch manage all dependency together. Run docker-compose up to start the containers and view logs, or run docker-compose up -d to run in the background. Running the Code (With Pm2) pm2 is a popular framework for run the nodejs applications in background with high performance You need first to install it by npm install pm2@latest -g You run the app is already ready to run with pm2 by run npm run pm2 To see the logs in real time you can run pm2 logs Common Errors If you encounter the error ERROR [ExceptionHandler] Configuration key \"JWT_SECRET\" does not exist , it means NestJS cannot read your .env.production file. To fix this, ensure you have injected the environment variable or that .env.production exists in the root of the project. It may be ignored by .git. If you see the error The default Firebase app does not exist. Make sure you call initializeApp() before using any of the Firebase services ,it means you have enabled FCM but have not configured it. :::tip Contact me Iam offer paid full server side setup See Plans ::: Get Support Choose Your Perfect Plan for Super Up Chat App Deployment Welcome to Super Up Chat App services! Our chat application, crafted with Flutter, provides a seamless multi-platform communication experience. Whether you're just starting or looking to expand your reach, we have a plan that fits your needs. Basic Plan - $100 (Node.js Backend Only) Ideal for beginners who want to set up the server side. Features: Purchase your own domain name and VPS server . Complete server side configuration including: MongoDB setup with security optimizations. Free SSL certification installation for secure connections. Full Node.js environment setup. Delivery Time: Ready within 1 to 2 days. Standard Plan - $200 (Android, Web, Admin Panel) A step-up for those ready to launch their Android and Web presence. Features: Everything from the Basic Plan. Flutter app configurations tailored to your brand: Custom app package name, logo, and app name. Compilation of the Android APK, Web Chat, and Admin Panel. Integration of third-party services (Agora.io, Google Ads, Google Maps, Firebase). Assistance with publishing the app on the Google Play Console ( Google Developer Account fee of $25 not included ). Delivery Time: Ready within 1 to 4 days. Premium Plan - $300 (Android, Web, Admin Panel, Windows, iOS, macOS) The full package for maximum reach across all platforms. Features: Inclusive of all offerings in the Basic and Standard Plans. Brand customization extends to iOS, macOS, and Windows versions. Compilation and build for the .ipa and macOS app versions. Publishing support for iOS, macOS, and Windows ( Apple Developer Account fee of $100 and Microsoft Account fees not included ). Delivery Time: Ready within 3 to 7 days. Absolutely, you can conclude your plans with an option for customization to meet specific business needs like this: Tailored Solutions for Your Business: In addition to the above plans, we also offer a full app rebuild service to fit the unique demands of your business. Whether you require specific features, design changes, or unique integrations, we can craft a solution that aligns perfectly with your vision. Custom Rebuild: Contact us with your requirements, and we will provide you with a bespoke quote. Flexible Solutions: We are committed to flexibility and will work closely with you to ensure that the final product meets your business objectives. Reach out to us through our Envato profile for a consultation and custom quote. Let's create a chat app that's uniquely yours! Adding this section provides a complete spectrum of services from standard plans to fully customized solutions, catering to a wider range of customers and their varying needs. Additional Information: Support: All plans come with basic installation support and guidance. Customization: Please provide all necessary branding materials upon purchase. Accounts: Costs associated with Google, Apple, and Microsoft developer accounts are not included in the plan prices. Timeframe: The indicated delivery times are from the start of the project. Let's launch your chat app together! :::tip Purchase To get Support you need to contact me at <NAME_EMAIL> OR Reach out to us through our Envato profile Or on Skype at live:.cid.607250433850e3a6 ::: :::tip Purchase_2 You should purchase the app first from codecanyou and send your purchase key in the email so i can process the support for you :::", "title": "Home"}, {"location": "#super-up-intro", "text": "Super up is a full social chat app clone from whatsapp cupertino ui support ( Android , ios , windows , web , macOS ) Current the admin panel under build you will receive it as updates for free in this project but you can install the current version of it", "title": "Super up Intro"}, {"location": "#technologies", "text": "Flutter for clint side last version or v 3.13.7 Nodejs v17.9.1 or later in the backend side Nestjs socket-io for real time management mongodb for data storage v v 5.* or v 6.* only Some backend tech Bearer token JWT ,ORM mongoose , agora-io for video and voice calls All media saved on the server on your vps server Google maps for share chat location mailer for send reset password OTP firebase-admin for push notifications geoip-lite and request-ip for detect user country :::tip Firebase This project not user firebase to save any data it only use the free firebase FCM for push notifications only! No for firebase high cost! :::", "title": "technologies"}, {"location": "#requirements", "text": "flutter last-sdk installed in your local machine nodejs , nestjs , mongodb installed in your vps server vps server should be at lest 2 cpu and 2GB ram and enough ssd storage at lest 20 GB you can check out this companies digitalocean , hostens make sure you have bandwidth more than 1TB Domain name it can be from namecheap firebase account for handle push notifications Optional agora-account if you intent to enable voice and video calls Optional onesignal account if you intent to push notifications over onesignal instead of firebase Optional google-api-key this key used for search and let users able to send locations in chat Optional google-ads-banner keys get banner id for ios and android if you want to enable it Optional For run ios you need paid apple developer account (99$) per year", "title": "requirements"}, {"location": "#after-purchasing-codecanyou", "text": "You will get all files for flutter project (android,ios,web,macos,windows) You will get the flutter admin panel project Postman collection for all apis Full nestjs code which its backend code Full support for future updates Free support for bug fixes only (there is paid support) You can use this project to modify and edit it serve your Customers ! :::tip VCHAT SDK This project use v-chat-sdk under the hood to serve the chat part This will not require you to purchase the v-chat-sdk it already impeded inside the source code if you are looking to implement a chat system into your exists or new flutter app then you should purchase v-chat-sdk ::: :::tip Contact me Iam offer paid full server side setup :::", "title": "after purchasing (codecanyou)"}, {"location": "#flutter", "text": "project structure in split packages To run the project first activate melos dart pub global activate melos Then run this commend in the root of the project melos bs this code will run pub get for you in all packages we have the following apps ( super_up_admin ) this is the admin panel apps ( super_up_app ) this app you should run it packages all needed packages the most important packages is the super_up_core the s_constants.dart in the lib/src", "title": "Flutter"}, {"location": "#lest-explain-packages", "text": "s_translate contains all arb files for super_up_app , super_up_admin projects super_up_core contains all logic for super up app itself like shared http requests,utils,widgets (DONT UPDATE) v_chat_firebase contains the firebase FCM only logic (DONT UPDATE) v_chat_input_ui this contains the ui and logic for the message input (recorder,file selector,location picker) v_chat_media_editor this contains the ui of media editor v_chat_message_page this contains the ui & logic of message page v_chat_room_page this contains the ui & logic of chat page v_chat_sdk_core contains all logic for v_chat app itself like shared http requests, socket,notifications (DONT UPDATE)", "title": "Lest explain packages"}, {"location": "#change-app-namepackagelogo", "text": "each app must have unique package name so you must change it! to be accepted in the stores this step should be done first before connect firebase we will use this package rename install by flutter pub global activate rename to change package name flutter pub global run rename --bundleId com.vchatsdk.vnotestarter Example => pub global run rename --bundleId com.XXXXXXX.XXXXXX to rename the app use flutter pub global run rename --appname \"YOUR APP NAME\" to update logo just update the logo.png in assets folder the logo must be .png then run the code in the super_up_app flutter pub run flutter_launcher_icons:main then this command dart run flutter_native_splash:create", "title": "Change app name,package,logo"}, {"location": "#connect-firebase", "text": "Recommend to user firebase flutter CLI which its new tool to make the firebase base connect straightforward android ios You can connect it easily be firebase follow up this video CLI tool", "title": "Connect firebase"}, {"location": "#sconstants", "text": "This file inside packages in [super_up_core] in lib/src/s_constants.dart This file contains the configuration of the app abstract class SConstants { ///your super up base domain url ///like this (example.com) not start https// or any sub domains example [superupdev.com] or server ip with port like [12.xxx.xxx:80] /// case of you use server ip just put the server ip connected to the port [http://ip:port] static const _productionBaseUrl = \"superupdev.online\"; ///your app name static const appName = \"Superup\"; ///android and ios admob ids [https://developers.google.com/admob/flutter/quick-start] [https://developers.google.com/ad-manager/mobile-ads-sdk/flutter/quick-start] static const androidAdUnitId = \"ca-app-pub-3940256099942544/6300978111\"; static const iosAdUnitId = \"ca-app-pub-3940256099942544/2934735716\"; ///setup video and voice calls [https://agora.io] static const agoraAppId = \"------------------------\"; ///change this to your google maps api key to enable google maps location picker static const googleMapsApiKey = \"AIzaSyAP---------------------\"; ///update this url to share the app for others static const googlePlayUrl = \"https://play.google.com/store/apps/details?id=com.app.superup\"; static const appleStoreUrl = \"https://testflight.apple.com/join/F4tAbW5J\"; ///get the onesignal id for push notifications [https://onesignal.com] static const oneSignalAppId = \"********-****-****-****-**************\"; ///don't update update only if you use server ip just return your server ip with port [12.xxx.xxx:80/] static String get baseMediaUrl { ///if you dont setup domain yet you can return the server ip like this [return Uri.parse(\"http://ip:port/\");] return \"https://api.$_productionBaseUrl/\"; } ///don't update update only if you use server ip just return your server ip with port [12.xxx.xxx:80/api/v1] static Uri get sApiBaseUrl { ///if you dont setup domain yet you can return the server ip like this [return Uri.parse(\"http://ip:port/api/v1\");] return Uri.parse(\"https://api.$_productionBaseUrl/api/v1\"); } }", "title": "SConstants"}, {"location": "#add-more-language", "text": "You can add new language by just open the [s_translation] package And inside lib/i18n create new file its name should follow the Standard of intl_short language code.arb .arb Inside this file copy the intl_en.arb and translate only the values and dont touch the key of the map Then, while you run the app, the new language will be added, and it will appears in the selections", "title": "Add more language"}, {"location": "#ads", "text": "get the ids from these urls get banner id for ios and android android and ios admob ids quick-start mobile-ads-sdk dont forget to update the android android/app/src/main/AndroidManifest.xml APPLICATION_ID not unit id be careful <meta-data android:name=\"com.google.android.gms.ads.APPLICATION_ID\" android:value=\"ca-app-pub-YOUR KEY HERE!\"/> put your android appId for ads here android:value= for Ios update the ios/Runner/Info.plist <key>GADApplicationIdentifier</key> <string>ca-app-pub-YOUR KEY HERE!</string>", "title": "Ads"}, {"location": "#agoraio", "text": "Create agora app and enable it copy the appId and enable Setup video and voice calls agora Static const agoraAppId = \"------------------------\"; enable the Secondary Certificate", "title": "Agora.io"}, {"location": "#google-api-key", "text": "Get google api key and enable all options like search and GEO locations for android and ios Don't forget to update the android android/app/src/main/AndroidManifest.xml <meta-data android:name=\"com.google.android.geo.API_KEY\" android:value=\"YOUR API_KEY HERE!\"/> and for ios ios/Runner/AppDelegate.swift GMSServices.provideAPIKey( YOUR API_KEY HERE! )", "title": "Google api key"}, {"location": "#notifications-ios-apns", "text": "Login to your Apple Developer Account,(if you don’t have, you need to create one to be able to test, publish ios app). Navigate to Certificates, Identifiers & Profiles > Keys > Add New from the left menu. Register a New key. Set the key name as FirebaseAPNS . Tick “Apple Push Notification Services (APNs)” Click Continue to register it. Once the key is registered follow below steps: Copy the Team ID (keep it aside) Download the Auth key File (keep it aside) Copy the Key ID (keep it aside) Click “Done” to complete. Open Firebase Dashboard > Project Settings Click on “Cloud Messaging” tab. For the iOS app, click “Upload”. Click on “Browse” to upload the recently downloaded Auth key file. Paste the Key ID Paste the Team ID Click upload to register it. All done now with ios notifications", "title": "Notifications iOS (APNS)"}, {"location": "#how-to-update-theme", "text": "For dark theme use it already inside the main.dart just update it dont delete it! Of course you can update the theme direct from the code if you have much experience", "title": "how to update theme"}, {"location": "#first-message-page", "text": "darkTheme: ThemeData( extensions: [ VMessageTheme.dark().copyWith( ///see options! ), ], ), for light theme use theme: ThemeData( extensions: [ VMessageTheme.dark().copyWith( ///see options! ), ], ),", "title": "First message page"}, {"location": "#second-chats-room-page", "text": "for dark theme use darkTheme: ThemeData( extensions: [ VRoomTheme.light().copyWith( ///see options! ), ], ), for light theme use theme: ThemeData( extensions: [ VRoomTheme.light().copyWith( ///see options! ), ], ),", "title": "second Chats Room page"}, {"location": "#one-signal", "text": "Get the onesignal id for push notifications [https://onesignal.com] see flutter docs then in apps/super_up_app/lib/v_chat_v2/v_chat_config.dart enable OneSignal push by add this constructor don't forget to update the app id in SConstants.oneSignalAppId vPush: VPush( enableVForegroundNotification: true, vPushConfig: const VLocalNotificationPushConfig(), ///if you support fcm push notifications fcmProvider: VChatFcmProver(), ///if you support OneSignal push notifications **THIS** oneSignalProvider: VChatOneSignalProver( appId: SConstants.oneSignalAppId, ), ), :::fin If firebase is available into your country its recommended to use it for notifications push which its 100% free service with no limits! :::", "title": "one signal"}, {"location": "#admin-panel", "text": "super up introduce admin panel to control the app there are two types of login admin login and viewer login you will have two passwords for the admin one for admin another for viewer admin can do anything viewer can only see the data he cant edit it you can set the password for admin and viewer from the .env.production file in the backend files this file is hidden #Admin panel passwords be carfaul ControlPanelAdminPassword= \"xxxxxxxxxxxxx\" # put strong password for admin who can edit and update any thing in the app ControlPanelAdminPasswordViewer= \"xxxxxxxxxx-xxxx\" # put strong password for admin that can only read(see ,users data,chats data etc...) he cannot update any thing You can change it any tine you want to re deploy your app!", "title": "Admin panel"}, {"location": "#publish", "text": "open terminal inside the super_up_app folder for android, you can run flutter build apk --split-per-abi for store publish for web, you can run flutter build web --web-renderer html see backend section for how to upload for ios, you can run ios :::danger Doesn't update any package version unless you know what to do :::", "title": "publish"}, {"location": "#backend-nodejs", "text": "", "title": "Backend (Nodejs)"}, {"location": "#requirements_1", "text": "Person who has experience to deal with ubuntu server and deploy otherwise you can contact me iam offer best upload experience Install Node.js (version v16.x or to 19.x ) and npm. Check the Node.js version using node -v . Install only if you not docker user cross-env npm i -g cross-env for managing production or development environments and pm2 for manage production deploy by npm i -g pm2 CLI ( npm install -g @nestjs/cli ). Install MongoDB (minimum v4.4 , recommended v6 ).", "title": "Requirements"}, {"location": "#env-file", "text": "# you can out local url or docker url or url of mongo in another service DB_URL=\"YOUR_MONGO_URL\" # Dont update it ever after you set it! if you do all users will logout!!! JWT_SECRET=\"STRONG_PASSWORD\" # Dont update it ever after you set it! if you do all users will logout!!! issuer=\"your gmail\" # Dont update it ever after you set it! if you do all users will logout!!! audience=\"your gmail\" NODE_ENV=\"production\" # dont update it EDIT_MODE =\"false\" # set to false ignoreEnvFile=\"false\" # set to true if you will inject the env values from system os PORT=80 //exposed port of node js #Admin panel passwords be carfaul ControlPanelAdminPassword= \"xxxxxxxxxxxxx\" # put strong password for admin who can edit and update any thing in the app ControlPanelAdminPasswordViewer= \"xxxxxxxxxx-xxxx\" # put strong password for admin that can only read(see ,users data,chats data etc...) he cannot update any thing isOneSignalEnabled =\"false\" # set to true if you can provide oneSignalAppId and oneSignalApiKey to enable push by onesignal isFirebaseFcmEnabled =\"false\" # set to true if you provide the firebase admin.json file #set onesignal data if you support it by set isOneSignalEnabled to true oneSignalAppId=\"xxxxxxxxx-xxxxx-xxxxx-xxxx-xxxxxxxxx\" #set onesignal data if you support it by set isOneSignalEnabled to true oneSignalApiKey=\"xxxxxxxxx\" # SET THE Email data to let use use forget password OTP EMAIL_HOST=\"EMAIL HOST FROM THE PROVIDER COMPANY\" EMAIL_USER=\"YOUR EMAIL USER\" EMAIL_PASSWORD=\"EMAIL USER PASSWORD\" # SET AGORA API KEYS AGORA_APP_ID=\"\" # Get this from agora app console `Primary Certificate` value AGORA_APP_CERTIFICATE=\"\"", "title": ".env file"}, {"location": "#privacy-policy", "text": "You can update your privacy privacy page in the source code inside http://localhost:3000/privacy-policy.html this will be the privacy-policy of your app You can edit it inside public/privacy-policy.html You can edit the home inside public/home.html you can access it http://localhost:3000", "title": "privacy-policy"}, {"location": "#firebase-admin-file", "text": "Make sure the firebase account is the same as the one used in flutter app. To ensure chat notifications work properly, follow this video to obtain the firebase.adminsdk.json file. Replace the existing firebase.adminsdk.json file with your new one. open your account in firebase then enable the Cloud Messaging API (Legacy) from", "title": "firebase admin file"}, {"location": "#obtaining-onesignal-keys", "text": "Create a Firebase account and follow this video to obtain OneSignal keys. Update the following fields with your OneSignal keys: oneSignalAppId=\"xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxx\" oneSignalApiKey=\"xxxxxxxxxxxx\"", "title": "Obtaining OneSignal Keys"}, {"location": "#running-the-code-without-docker", "text": "You should be familiar with ubuntu server setup Install ubuntu server v 20.* and later Open a terminal in the backend root folder. Run npm i or npm i --force if issues occur. Generate a dist folder npm run build . Run npm run start:prod for live console logs for just make sure your server is configure well if you run in production mode. in your vps server then you should then stop and run this run pm2 start ecosystem.config.js --only normal --env production .for background serve to see logs run pm2 logs If you see app run in production, your code is production-ready. Access the development server at localhost:80 and production server at port 80 Update the port in .env.production if necessary. Update the PORT in the .env file if using Docker, and ensure you update the environment variable in the OS (env). you need to install nginx and connect domain to your server for security and more speed! dont forget to add the websocket support for nginx while you configure it inside the location of your server block don't forget to add this to enable websocket support proxy_set_header Upgrade $http_upgrade; proxy_set_header Connection \"upgrade\"; proxy_http_version 1.1; proxy_set_header Host $host; proxy_set_header X-Real-IP $remote_addr; proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for; proxy_set_header X-Forwarded-Proto $scheme;", "title": "Running the Code (<PERSON> Docker)"}, {"location": "#deploy-web", "text": "run the flutter code to build the web version inside the super_up_app folder run this code flutter build web --web-renderer html now you can find the html folder inside the build folder you need to upload it to your server your server can your vps server you can use nginx to deploy your web code and admin code", "title": "Deploy web"}, {"location": "#deploy-admin", "text": "run the flutter code to build the web version inside the super_up_admin folder run this code flutter build web --web-renderer html now you can find the html folder inside the build folder you need to upload it to your server your server can your vps server you can use nginx to deploy your web code and admin code", "title": "Deploy admin"}, {"location": "#running-the-code-with-docker", "text": "Install Docker and Docker Compose. update .env.production this keys DB_URL= from mongodb://127.0.0.1:27017/super_up to DB_URL=************************************************************************* Run the Dockerfile if Docker is already installed on your system. Note that this only sets up v_chat_sdk and doesn't include MongoDB or Redis. You need to manage these separately OR. Use a compose file witch manage all dependency together. Run docker-compose up to start the containers and view logs, or run docker-compose up -d to run in the background.", "title": "Running the Code (<PERSON>)"}, {"location": "#running-the-code-with-pm2", "text": "pm2 is a popular framework for run the nodejs applications in background with high performance You need first to install it by npm install pm2@latest -g You run the app is already ready to run with pm2 by run npm run pm2 To see the logs in real time you can run pm2 logs", "title": "Running the Code (With Pm2)"}, {"location": "#common-errors", "text": "If you encounter the error ERROR [ExceptionHandler] Configuration key \"JWT_SECRET\" does not exist , it means NestJS cannot read your .env.production file. To fix this, ensure you have injected the environment variable or that .env.production exists in the root of the project. It may be ignored by .git. If you see the error The default Firebase app does not exist. Make sure you call initializeApp() before using any of the Firebase services ,it means you have enabled FCM but have not configured it. :::tip Contact me Iam offer paid full server side setup See Plans :::", "title": "Common Errors"}, {"location": "#get-support", "text": "Choose Your Perfect Plan for Super Up Chat App Deployment Welcome to Super Up Chat App services! Our chat application, crafted with Flutter, provides a seamless multi-platform communication experience. Whether you're just starting or looking to expand your reach, we have a plan that fits your needs.", "title": "Get Support"}, {"location": "#basic-plan-100-nodejs-backend-only", "text": "Ideal for beginners who want to set up the server side. Features: Purchase your own domain name and VPS server . Complete server side configuration including: MongoDB setup with security optimizations. Free SSL certification installation for secure connections. Full Node.js environment setup. Delivery Time: Ready within 1 to 2 days.", "title": "Basic Plan - $100 (Node.js Backend Only)"}, {"location": "#standard-plan-200-android-web-admin-panel", "text": "A step-up for those ready to launch their Android and Web presence. Features: Everything from the Basic Plan. Flutter app configurations tailored to your brand: Custom app package name, logo, and app name. Compilation of the Android APK, Web Chat, and Admin Panel. Integration of third-party services (Agora.io, Google Ads, Google Maps, Firebase). Assistance with publishing the app on the Google Play Console ( Google Developer Account fee of $25 not included ). Delivery Time: Ready within 1 to 4 days.", "title": "Standard Plan - $200 (Android, Web, Admin Panel)"}, {"location": "#premium-plan-300-android-web-admin-panel-windows-ios-macos", "text": "The full package for maximum reach across all platforms. Features: Inclusive of all offerings in the Basic and Standard Plans. Brand customization extends to iOS, macOS, and Windows versions. Compilation and build for the .ipa and macOS app versions. Publishing support for iOS, macOS, and Windows ( Apple Developer Account fee of $100 and Microsoft Account fees not included ). Delivery Time: Ready within 3 to 7 days. Absolutely, you can conclude your plans with an option for customization to meet specific business needs like this: Tailored Solutions for Your Business: In addition to the above plans, we also offer a full app rebuild service to fit the unique demands of your business. Whether you require specific features, design changes, or unique integrations, we can craft a solution that aligns perfectly with your vision. Custom Rebuild: Contact us with your requirements, and we will provide you with a bespoke quote. Flexible Solutions: We are committed to flexibility and will work closely with you to ensure that the final product meets your business objectives. Reach out to us through our Envato profile for a consultation and custom quote. Let's create a chat app that's uniquely yours! Adding this section provides a complete spectrum of services from standard plans to fully customized solutions, catering to a wider range of customers and their varying needs. Additional Information: Support: All plans come with basic installation support and guidance. Customization: Please provide all necessary branding materials upon purchase. Accounts: Costs associated with Google, Apple, and Microsoft developer accounts are not included in the plan prices. Timeframe: The indicated delivery times are from the start of the project.", "title": "Premium Plan - $300 (Android, Web, Admin Panel, Windows, iOS, macOS)"}, {"location": "#lets-launch-your-chat-app-together", "text": ":::tip Purchase To get Support you need to contact me at <NAME_EMAIL> OR Reach out to us through our Envato profile Or on Skype at live:.cid.607250433850e3a6 ::: :::tip Purchase_2 You should purchase the app first from codecanyou and send your purchase key in the email so i can process the support for you :::", "title": "Let's launch your chat app together!"}]}