// Copyright 2023, the hatemragab project author.
// All rights reserved. Use of this source code is governed by a
// MIT license that can be found in the LICENSE file.

import '../user/my_profile.dart';

/// Represents a single account session with all necessary data
class AccountSession {
  final String accountId; // Unique identifier for this account session
  final String email;
  final String accessToken;
  final SMyProfile profile;
  final DateTime lastActiveAt;
  final bool isActive; // Whether this is the currently active account

  const AccountSession({
    required this.accountId,
    required this.email,
    required this.accessToken,
    required this.profile,
    required this.lastActiveAt,
    required this.isActive,
  });

  /// Create account ID from user email and user ID
  static String createAccountId(String email, String userId) {
    return '${email}_$userId';
  }

  /// Copy with method for updating account session
  AccountSession copyWith({
    String? accountId,
    String? email,
    String? accessToken,
    SMyProfile? profile,
    DateTime? lastActiveAt,
    bool? isActive,
  }) {
    return AccountSession(
      accountId: accountId ?? this.accountId,
      email: email ?? this.email,
      accessToken: accessToken ?? this.accessToken,
      profile: profile ?? this.profile,
      lastActiveAt: lastActiveAt ?? this.lastActiveAt,
      isActive: isActive ?? this.isActive,
    );
  }

  /// Convert to map for storage
  Map<String, dynamic> toMap() {
    return {
      'accountId': accountId,
      'email': email,
      'accessToken': accessToken,
      'profile': profile.toMap(),
      'lastActiveAt': lastActiveAt.millisecondsSinceEpoch,
      'isActive': isActive,
    };
  }

  /// Create from map
  factory AccountSession.fromMap(Map<String, dynamic> map) {
    return AccountSession(
      accountId: map['accountId'] as String,
      email: map['email'] as String,
      accessToken: map['accessToken'] as String,
      profile: SMyProfile.fromMap(map['profile'] as Map<String, dynamic>),
      lastActiveAt: DateTime.fromMillisecondsSinceEpoch(map['lastActiveAt'] as int),
      isActive: map['isActive'] as bool? ?? false,
    );
  }

  @override
  String toString() {
    return 'AccountSession{accountId: $accountId, email: $email, isActive: $isActive, lastActiveAt: $lastActiveAt}';
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is AccountSession &&
          runtimeType == other.runtimeType &&
          accountId == other.accountId;

  @override
  int get hashCode => accountId.hashCode;
}
