/**
 * Copyright 2023, the hatemragab project author.
 * All rights reserved. Use of this source code is governed by a
 * MIT license that can be found in the LICENSE file.
 */

// import {CanActivate, ExecutionContext, Injectable} from "@nestjs/common";
// import {UserType} from "../utils/enums";
// import { IUser } from "../../api/user_modules/user/entities/user.entity";
//
// @Injectable()
// export class IsSuperGuard implements CanActivate {
//     async canActivate(
//         context: ExecutionContext
//     ): Promise<boolean> {
//         const request = context.switchToHttp().getRequest();
//         let myUser = request.user as IUser
//         return myUser.userType == UserType.SuperAdmin;
//     }
// }