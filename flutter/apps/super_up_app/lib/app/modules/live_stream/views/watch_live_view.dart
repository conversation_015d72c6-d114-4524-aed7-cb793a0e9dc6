// Copyright 2023, the hatemragab project author.
// All rights reserved. Use of this source code is governed by a
// MIT license that can be found in the LICENSE file.

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:super_up_core/super_up_core.dart';

import '../controllers/watch_live_controller.dart';
import '../services/live_stream_api_service.dart';
import '../models/live_stream_model.dart';
import 'live_stream_view.dart';
import 'share_live_stream_sheet.dart';

class WatchLiveView extends StatefulWidget {
  const WatchLiveView({super.key});

  @override
  State<WatchLiveView> createState() => _WatchLiveViewState();
}

class _WatchLiveViewState extends State<WatchLiveView> {
  late final WatchLiveController controller;

  @override
  void initState() {
    super.initState();
    controller = GetIt.I.get<WatchLiveController>();
    controller.onInit();
    controller.addListener(_handlePendingApproval);
  }

  void _handlePendingApproval() {
    final pendingApproval = controller.pendingApproval;
    if (pendingApproval != null && mounted) {
      final approved = pendingApproval['approved'] as bool;
      final message = pendingApproval['message'] as String;
      final streamId = pendingApproval['streamId'] as String;

      if (approved) {
        // Show success notification
        VAppAlert.showSuccessSnackBar(
          message: message,
          context: context,
        );

        // Auto-join the stream after a short delay
        Future.delayed(const Duration(seconds: 1), () {
          if (mounted) {
            final stream = controller.getStreamById(streamId);
            if (stream != null) {
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => LiveStreamView(
                    stream: stream,
                    isStreamer: false,
                  ),
                ),
              );
            }
          }
        });
      } else {
        // Show rejection notification
        VAppAlert.showErrorSnackBar(
          message: message,
          context: context,
        );
      }

      // Clear the pending approval
      controller.clearPendingApproval();
    }
  }

  @override
  void dispose() {
    controller.removeListener(_handlePendingApproval);
    controller.onClose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return CupertinoPageScaffold(
      navigationBar: CupertinoNavigationBar(
        middle: Text(
          'Live Streams',
          style: context.cupertinoTextTheme.textStyle.copyWith(
            fontSize: 18,
            fontWeight: FontWeight.w600,
          ),
        ),
        trailing: CupertinoButton(
          padding: EdgeInsets.zero,
          onPressed: controller.refreshStreams,
          child: const Icon(
            CupertinoIcons.refresh,
            size: 24,
          ),
        ),
      ),
      child: SafeArea(
        child: ValueListenableBuilder<SLoadingState<List<LiveStreamModel>>>(
          valueListenable: controller,
          builder: (context, state, child) {
            return VAsyncWidgetsBuilder(
              loadingState: state.loadingState,
              onRefresh: controller.refreshStreams,
              successWidget: () {
                final streams = state.data;

                if (streams.isEmpty) {
                  return _buildEmptyState(context);
                }

                return RefreshIndicator(
                  onRefresh: controller.refreshStreams,
                  child: ListView.separated(
                    padding: const EdgeInsets.all(16),
                    itemCount: streams.length,
                    separatorBuilder: (context, index) =>
                        const SizedBox(height: 16),
                    itemBuilder: (context, index) {
                      final stream = streams[index];
                      return _buildStreamCard(context, stream);
                    },
                  ),
                );
              },
            );
          },
        ),
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                color: CupertinoColors.systemGrey5,
                shape: BoxShape.circle,
              ),
              child: const Icon(
                CupertinoIcons.video_camera,
                size: 40,
                color: CupertinoColors.systemGrey,
              ),
            ),
            const SizedBox(height: 24),
            Text(
              'No Live Streams',
              style: context.cupertinoTextTheme.textStyle.copyWith(
                fontSize: 20,
                fontWeight: FontWeight.w600,
                color: CupertinoColors.systemGrey,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'There are no live streams at the moment.\nCheck back later or start your own!',
              style: context.cupertinoTextTheme.textStyle.copyWith(
                fontSize: 14,
                color: CupertinoColors.systemGrey2,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 32),
            CupertinoButton(
              color: CupertinoColors.systemBlue,
              borderRadius: BorderRadius.circular(8),
              onPressed: controller.refreshStreams,
              child: const Text('Refresh'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStreamCard(BuildContext context, LiveStreamModel stream) {
    return GestureDetector(
      onTap: () => _joinStream(context, stream),
      child: Container(
        decoration: BoxDecoration(
          color: CupertinoColors.systemBackground,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: CupertinoColors.systemGrey.withOpacity(0.1),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Stream thumbnail/preview
            Container(
              height: 180,
              width: double.infinity,
              decoration: BoxDecoration(
                color: CupertinoColors.black,
                borderRadius: const BorderRadius.vertical(
                  top: Radius.circular(12),
                ),
              ),
              child: Stack(
                children: [
                  // Thumbnail or placeholder
                  if (stream.thumbnailUrl != null)
                    ClipRRect(
                      borderRadius: const BorderRadius.vertical(
                        top: Radius.circular(12),
                      ),
                      child: Image.network(
                        stream.thumbnailUrl!,
                        width: double.infinity,
                        height: double.infinity,
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) {
                          return const Center(
                            child: Icon(
                              CupertinoIcons.video_camera,
                              size: 48,
                              color: Colors.white,
                            ),
                          );
                        },
                      ),
                    )
                  else
                    const Center(
                      child: Icon(
                        CupertinoIcons.video_camera,
                        size: 48,
                        color: Colors.white,
                      ),
                    ),

                  // Live indicator
                  Positioned(
                    top: 12,
                    left: 12,
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: CupertinoColors.systemRed,
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Container(
                            width: 6,
                            height: 6,
                            decoration: const BoxDecoration(
                              color: Colors.white,
                              shape: BoxShape.circle,
                            ),
                          ),
                          const SizedBox(width: 4),
                          const Text(
                            'LIVE',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),

                  // Viewer count
                  Positioned(
                    top: 12,
                    right: 12,
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.black.withValues(alpha: 0.6),
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          const Icon(
                            CupertinoIcons.eye,
                            size: 12,
                            color: Colors.white,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            '${stream.viewerCount}',
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 12,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),

            // Stream info
            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Streamer info
                  Row(
                    children: [
                      ClipOval(
                        child: Image.network(
                          _getFullImageUrl(stream.streamerData.userImage),
                          width: 40,
                          height: 40,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) {
                            return Container(
                              width: 40,
                              height: 40,
                              decoration: const BoxDecoration(
                                color: CupertinoColors.systemGrey,
                                shape: BoxShape.circle,
                              ),
                              child: const Icon(
                                CupertinoIcons.person_fill,
                                color: Colors.white,
                                size: 20,
                              ),
                            );
                          },
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              stream.streamerData.fullName,
                              style:
                                  context.cupertinoTextTheme.textStyle.copyWith(
                                fontSize: 14,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                            Text(
                              _formatDuration(stream.startedAt),
                              style:
                                  context.cupertinoTextTheme.textStyle.copyWith(
                                fontSize: 12,
                                color: CupertinoColors.systemGrey,
                              ),
                            ),
                          ],
                        ),
                      ),
                      // Share button
                      CupertinoButton(
                        padding: EdgeInsets.zero,
                        onPressed: () => _showShareSheet(context, stream),
                        child: Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: CupertinoColors.systemGrey6,
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: const Icon(
                            CupertinoIcons.share,
                            size: 18,
                            color: CupertinoColors.systemBlue,
                          ),
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: 12),

                  // Stream title
                  Text(
                    stream.title,
                    style: context.cupertinoTextTheme.textStyle.copyWith(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),

                  if (stream.description != null &&
                      stream.description!.isNotEmpty) ...[
                    const SizedBox(height: 8),
                    Text(
                      stream.description!,
                      style: context.cupertinoTextTheme.textStyle.copyWith(
                        fontSize: 14,
                        color: CupertinoColors.systemGrey,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _formatDuration(DateTime? startedAt) {
    if (startedAt == null) return 'Starting soon';

    final duration = DateTime.now().difference(startedAt);
    if (duration.inHours > 0) {
      return '${duration.inHours}h ${duration.inMinutes % 60}m ago';
    } else if (duration.inMinutes > 0) {
      return '${duration.inMinutes}m ago';
    } else {
      return 'Just started';
    }
  }

  void _joinStream(BuildContext context, LiveStreamModel stream) async {
    // Show loading indicator
    showCupertinoDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const CupertinoAlertDialog(
        content: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            CupertinoActivityIndicator(),
            SizedBox(width: 16),
            Text('Joining stream...'),
          ],
        ),
      ),
    );

    try {
      // Try to join the stream via API first to check for ban
      final result = await controller.joinStream(stream.id);

      // Close loading dialog
      if (context.mounted) {
        Navigator.of(context).pop();
      }

      if (result != null && context.mounted) {
        // Successfully joined, navigate to stream view
        context.toPage(LiveStreamView(
          stream: result,
          isStreamer: false,
        ));
      }
    } catch (error) {
      // Close loading dialog
      if (context.mounted) {
        Navigator.of(context).pop();
      }

      // Show ban error dialog
      if (context.mounted) {
        String errorMessage = 'Unable to join stream';

        // Check if it's a ban error
        if (error.toString().toLowerCase().contains('banned') ||
            error
                .toString()
                .toLowerCase()
                .contains('you are banned from this stream')) {
          errorMessage = 'You are banned from this stream';
          _showErrorDialog(context, errorMessage);
        } else if (error.toString().toLowerCase().contains('forbidden')) {
          errorMessage = 'Access denied to this stream';
          _showErrorDialog(context, errorMessage);
        } else if (error.toString().toLowerCase().contains('approval') ||
            error.toString().toLowerCase().contains('need approval')) {
          // Show join request dialog for approval-required streams
          _showJoinRequestDialog(context, stream);
        } else {
          errorMessage = 'Failed to join stream: ${error.toString()}';
          _showErrorDialog(context, errorMessage);
        }
      }
    }
  }

  void _showErrorDialog(BuildContext context, String message) {
    showCupertinoDialog(
      context: context,
      builder: (BuildContext context) {
        return CupertinoAlertDialog(
          title: const Text('Unable to Join Stream'),
          content: Text(message),
          actions: [
            CupertinoDialogAction(
              child: const Text('OK'),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
          ],
        );
      },
    );
  }

  void _showJoinRequestDialog(BuildContext context, LiveStreamModel stream) {
    showCupertinoDialog(
      context: context,
      builder: (BuildContext context) {
        return CupertinoAlertDialog(
          title: const Text('Join Request Required'),
          content: const Text(
              'This stream requires approval from the host. Would you like to send a join request?'),
          actions: [
            CupertinoDialogAction(
              child: const Text('Cancel'),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
            CupertinoDialogAction(
              isDefaultAction: true,
              child: const Text('Request to Join'),
              onPressed: () async {
                Navigator.of(context).pop();
                await _sendJoinRequest(context, stream);
              },
            ),
          ],
        );
      },
    );
  }

  Future<void> _sendJoinRequest(
      BuildContext context, LiveStreamModel stream) async {
    // Show loading
    showCupertinoDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const CupertinoAlertDialog(
        content: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            CupertinoActivityIndicator(),
            SizedBox(width: 16),
            Text('Sending request...'),
          ],
        ),
      ),
    );

    try {
      // Use the API service directly instead of creating a controller
      final apiService = GetIt.I.get<LiveStreamApiService>();
      await apiService.requestJoinStream(stream.id).timeout(
        const Duration(seconds: 10),
        onTimeout: () {
          throw Exception('Request timed out. Please try again.');
        },
      );

      // Close loading dialog
      if (context.mounted) {
        Navigator.of(context).pop();
      }

      // Show success dialog
      if (context.mounted) {
        showCupertinoDialog(
          context: context,
          builder: (context) => CupertinoAlertDialog(
            title: const Text('Request Sent'),
            content: const Text(
                'Your join request has been sent to the host. You will be notified when they respond.'),
            actions: [
              CupertinoDialogAction(
                child: const Text('OK'),
                onPressed: () {
                  Navigator.of(context).pop();
                },
              ),
            ],
          ),
        );
      }
    } catch (e) {
      // Close loading dialog
      if (context.mounted) {
        Navigator.of(context).pop();
      }

      // Show error
      if (context.mounted) {
        VAppAlert.showErrorSnackBar(
          message: 'Failed to send join request: ${e.toString()}',
          context: context,
        );
      }
    }
  }

  void _showShareSheet(BuildContext context, LiveStreamModel stream) {
    showCupertinoModalPopup(
      context: context,
      builder: (context) => ShareLiveStreamSheet(
        stream: stream,
        isHost: false, // Viewers are not hosts
      ),
    );
  }

  String _getFullImageUrl(String imageUrl) {
    if (imageUrl.startsWith('http')) {
      return imageUrl; // Already a full URL
    }
    // Construct full URL: baseMediaUrl + imageUrl
    return '${SConstants.baseMediaUrl}$imageUrl';
  }
}
