// Copyright 2023, the hatemragab project author.
// All rights reserved. Use of this source code is governed by a
// MIT license that can be found in the LICENSE file.

import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:super_up_core/super_up_core.dart';
import 'package:v_chat_sdk_core/v_chat_sdk_core.dart';

import '../models/live_stream_model.dart';
import '../services/live_stream_api_service.dart';
import '../views/live_stream_view.dart';

class WatchLiveController extends SLoadingController<List<LiveStreamModel>> {
  final LiveStreamApiService _apiService = GetIt.I.get<LiveStreamApiService>();

  Timer? _refreshTimer;
  StreamSubscription? _socketSubscription;
  Map<String, dynamic>? _pendingApproval;

  WatchLiveController() : super(SLoadingState([]));

  // Getter for pending approval data
  Map<String, dynamic>? get pendingApproval => _pendingApproval;

  // Clear pending approval
  void clearPendingApproval() {
    _pendingApproval = null;
    notifyListeners();
  }

  // Join approved stream
  LiveStreamModel? getStreamById(String streamId) {
    try {
      return data.firstWhere(
        (s) => s.id == streamId,
        orElse: () => throw Exception('Stream not found'),
      );
    } catch (e) {
      if (kDebugMode) {
        print('Stream not found: $streamId');
      }
      return null;
    }
  }

  @override
  void onInit() {
    getLiveStreams();
    _startAutoRefresh();
    _listenToSocketEvents();
  }

  @override
  void onClose() {
    _refreshTimer?.cancel();
    _socketSubscription?.cancel();
  }

  void _startAutoRefresh() {
    // Refresh live streams every 30 seconds
    _refreshTimer = Timer.periodic(const Duration(seconds: 30), (timer) {
      if (value.loadingState != VChatLoadingState.loading) {
        getLiveStreamsFromApi();
      }
    });
  }

  void _listenToSocketEvents() {
    // Listen for live stream events from socket
    _socketSubscription =
        VChatController.I.nativeStreams.socketStatusStream.listen((event) {
      if (event.isConnected) {
        // Socket connected, refresh streams
        getLiveStreamsFromApi();
      }
    });

    // Listen for join request responses
    final socket = VChatController.I.nativeApi.remote.socketIo.socket;
    socket.on('joinRequestResponse', (data) {
      if (data != null && data is Map<String, dynamic>) {
        final approved = data['approved'] as bool? ?? false;
        final streamId = data['streamId'] as String?;
        final message = data['message'] as String?;

        if (approved && streamId != null) {
          // Request was approved - show success notification
          if (kDebugMode) {
            print('Join request approved for stream: $streamId');
          }

          // Show success notification - we'll handle this in the view layer
          // Store the approval data for the view to handle
          _pendingApproval = {
            'streamId': streamId,
            'message': message ?? 'Your join request has been approved! 🎉',
            'approved': true,
          };
          notifyListeners();
        } else {
          // Request was rejected
          if (kDebugMode) {
            print('Join request rejected for stream: $streamId');
          }

          // Store the rejection data for the view to handle
          _pendingApproval = {
            'streamId': streamId,
            'message': message ?? 'Your join request was not approved.',
            'approved': false,
          };
          notifyListeners();
        }
      }
    });

    // You can add more specific socket listeners here for:
    // - live_stream_started
    // - live_stream_ended
    // - user_joined_stream
    // - user_left_stream
  }

  Future<void> getLiveStreams() async {
    try {
      // Try to load cached data first
      final cachedData = VAppPref.getMap("api/live_streams");
      if (cachedData != null) {
        final list = cachedData['data'] as List;
        data.clear();
        data.addAll(list.map((e) => LiveStreamModel.fromMap(e)).toList());
        setStateSuccess();
        update();
      }
    } catch (err) {
      if (kDebugMode) {
        print('Error loading cached live streams: $err');
      }
    }

    await getLiveStreamsFromApi();
  }

  Future<void> getLiveStreamsFromApi() async {
    await vSafeApiCall<List<LiveStreamModel>>(
      request: () async {
        return await _apiService.getLiveStreams(
          status: 'live',
          page: 1,
          limit: 50,
        );
      },
      onSuccess: (response) {
        data.clear();
        data.addAll(response);

        // Cache the data
        unawaited(VAppPref.setMap("api/live_streams", {
          "data": response.map((e) => e.toMap()).toList(),
        }));

        setStateSuccess();
        update();
      },
      onError: (exception, trace) {
        if (kDebugMode) {
          print('Error fetching live streams: $exception');
        }
        setStateError();
        update();
      },
    );
  }

  Future<void> refreshStreams() async {
    // Clear cache to ensure fresh data
    await VAppPref.removeKey("api/live_streams");
    await getLiveStreamsFromApi();
  }

  void clearCache() {
    VAppPref.removeKey("api/live_streams");
  }

  Future<LiveStreamModel?> joinStream(String streamId) async {
    try {
      final result = await _apiService.joinLiveStream(streamId);
      return result['stream'] as LiveStreamModel?;
    } catch (e) {
      if (kDebugMode) {
        print('Error joining stream: $e');
      }
      // Re-throw the error so it can be handled by the UI
      rethrow;
    }
  }

  void updateStreamViewerCount(String streamId, int newCount) {
    final streamIndex = data.indexWhere((stream) => stream.id == streamId);
    if (streamIndex != -1) {
      data[streamIndex] = data[streamIndex].copyWith(viewerCount: newCount);
      update();
    }
  }

  void removeEndedStream(String streamId) {
    data.removeWhere((stream) => stream.id == streamId);
    update();

    // Update cache
    unawaited(VAppPref.setMap("api/live_streams", {
      "data": data.map((e) => e.toMap()).toList(),
    }));
  }

  void addNewStream(LiveStreamModel stream) {
    // Add new stream to the beginning of the list
    data.insert(0, stream);
    update();

    // Update cache
    unawaited(VAppPref.setMap("api/live_streams", {
      "data": data.map((e) => e.toMap()).toList(),
    }));
  }
}
