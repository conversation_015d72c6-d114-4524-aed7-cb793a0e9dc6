{"configurations": [{"directories": [{"build": ".", "jsonFile": "directory-.-RelWithDebInfo-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.6.0"}, "projectIndex": 0, "source": "."}], "name": "RelWithDebInfo", "projects": [{"directoryIndexes": [0], "name": "Project"}], "targets": []}], "kind": "codemodel", "paths": {"build": "E:/Apps/BigBossProjects/codecanyon-FqWwCRY6-superup-cupertino-theme-full-whatsapp-clone-socketio-mongodb-flutter/source/flutter/apps/super_up_app/android/app/.cxx/RelWithDebInfo/4h1o2y15/arm64-v8a", "source": "C:/src/flutter/packages/flutter_tools/gradle/src/main/groovy"}, "version": {"major": 2, "minor": 3}}