{"buildFiles": ["C:\\src\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "E:\\Apps\\BigBossProjects\\codecanyon-FqWwCRY6-superup-cupertino-theme-full-whatsapp-clone-socketio-mongodb-flutter\\source\\flutter\\apps\\super_up_app\\android\\app\\.cxx\\Debug\\2i1v236m\\armeabi-v7a", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "E:\\Apps\\BigBossProjects\\codecanyon-FqWwCRY6-superup-cupertino-theme-full-whatsapp-clone-socketio-mongodb-flutter\\source\\flutter\\apps\\super_up_app\\android\\app\\.cxx\\Debug\\2i1v236m\\armeabi-v7a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}