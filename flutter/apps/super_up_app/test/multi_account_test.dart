// Copyright 2023, the hatemragab project author.
// All rights reserved. Use of this source code is governed by a
// MIT license that can be found in the LICENSE file.

import 'package:flutter_test/flutter_test.dart';
import 'package:super_up_core/super_up_core.dart';

void main() {
  group('Multi-Account Manager Tests', () {
    late MultiAccountManager manager;

    setUp(() {
      manager = MultiAccountManager.instance;
    });

    test('should create account ID correctly', () {
      const email = '<EMAIL>';
      const userId = 'user123';
      final accountId = AccountSession.createAccountId(email, userId);
      expect(accountId, equals('test@example.com_user123'));
    });

    test('should add account successfully', () async {
      // Create mock profile
      final profile = SMyProfile(
        baseUser: SBaseUser(
          id: 'user123',
          fullName: 'Test User',
          userImage: 'https://example.com/image.jpg',
          email: '<EMAIL>',
          isOnline: true,
          lastSeen: DateTime.now(),
        ),
        bio: 'Test bio',
        email: '<EMAIL>',
        registerStatus: RegisterStatus.accepted,
        isPrime: false,
        isVerified: false,
        userPrivacy: SUserPrivacy(
          whoCanSeeMyLastSeen: WhoCanSeeMyLastSeen.everyone,
          whoCanSeeMyProfileImage: WhoCanSeeMyProfileImage.everyone,
          whoCanSeeMyStory: WhoCanSeeMyStory.everyone,
          whoCanSeeMyBio: WhoCanSeeMyBio.everyone,
          whoCanSeeMyEmail: WhoCanSeeMyEmail.everyone,
          whoCanSeeMyPhone: WhoCanSeeMyPhone.everyone,
          whoCanCallMe: WhoCanCallMe.everyone,
          whoCanMessageMe: WhoCanMessageMe.everyone,
          whoCanAddMeToGroup: WhoCanAddMeToGroup.everyone,
          whoCanSeeMyReadReceipt: WhoCanSeeMyReadReceipt.everyone,
        ),
      );

      await manager.addAccount(
        email: '<EMAIL>',
        accessToken: 'token123',
        profile: profile,
      );

      expect(manager.accounts.length, equals(1));
      expect(manager.accounts.first.email, equals('<EMAIL>'));
      expect(manager.accounts.first.accessToken, equals('token123'));
    });

    test('should switch accounts correctly', () async {
      // Add two accounts
      final profile1 = _createMockProfile('user1', '<EMAIL>', 'User One');
      final profile2 = _createMockProfile('user2', '<EMAIL>', 'User Two');

      await manager.addAccount(
        email: '<EMAIL>',
        accessToken: 'token1',
        profile: profile1,
      );

      await manager.addAccount(
        email: '<EMAIL>',
        accessToken: 'token2',
        profile: profile2,
      );

      // Switch to first account
      final accountId1 = AccountSession.createAccountId('<EMAIL>', 'user1');
      await manager.switchToAccount(accountId1);

      expect(manager.currentAccount?.accountId, equals(accountId1));
      expect(manager.currentAccount?.isActive, isTrue);

      // Switch to second account
      final accountId2 = AccountSession.createAccountId('<EMAIL>', 'user2');
      await manager.switchToAccount(accountId2);

      expect(manager.currentAccount?.accountId, equals(accountId2));
      expect(manager.currentAccount?.isActive, isTrue);

      // Verify first account is no longer active
      final account1 = manager.getAccount(accountId1);
      expect(account1?.isActive, isFalse);
    });

    test('should remove account correctly', () async {
      // Add account
      final profile = _createMockProfile('user1', '<EMAIL>', 'Test User');
      await manager.addAccount(
        email: '<EMAIL>',
        accessToken: 'token123',
        profile: profile,
      );

      expect(manager.accounts.length, equals(1));

      // Remove account
      final accountId = AccountSession.createAccountId('<EMAIL>', 'user1');
      await manager.removeAccount(accountId);

      expect(manager.accounts.length, equals(0));
      expect(manager.currentAccount, isNull);
    });

    test('should handle multiple accounts correctly', () async {
      // Add multiple accounts
      for (int i = 1; i <= 3; i++) {
        final profile = _createMockProfile('user$i', 'test$<EMAIL>', 'User $i');
        await manager.addAccount(
          email: 'test$<EMAIL>',
          accessToken: 'token$i',
          profile: profile,
        );
      }

      expect(manager.accounts.length, equals(3));
      expect(manager.hasMultipleAccounts, isTrue);

      // Test sorting by last active
      final sortedAccounts = manager.getAccountsSortedByLastActive();
      expect(sortedAccounts.length, equals(3));
      
      // Most recently added should be first (since they have more recent lastActiveAt)
      expect(sortedAccounts.first.profile.baseUser.fullName, equals('User 3'));
    });

    test('should check if account exists for email', () async {
      final profile = _createMockProfile('user1', '<EMAIL>', 'Test User');
      await manager.addAccount(
        email: '<EMAIL>',
        accessToken: 'token123',
        profile: profile,
      );

      expect(manager.hasAccountForEmail('<EMAIL>'), isTrue);
      expect(manager.hasAccountForEmail('<EMAIL>'), isTrue); // Case insensitive
      expect(manager.hasAccountForEmail('<EMAIL>'), isFalse);
    });
  });
}

SMyProfile _createMockProfile(String userId, String email, String fullName) {
  return SMyProfile(
    baseUser: SBaseUser(
      id: userId,
      fullName: fullName,
      userImage: 'https://example.com/image.jpg',
      email: email,
      isOnline: true,
      lastSeen: DateTime.now(),
    ),
    bio: 'Test bio for $fullName',
    email: email,
    registerStatus: RegisterStatus.accepted,
    isPrime: false,
    isVerified: false,
    userPrivacy: SUserPrivacy(
      whoCanSeeMyLastSeen: WhoCanSeeMyLastSeen.everyone,
      whoCanSeeMyProfileImage: WhoCanSeeMyProfileImage.everyone,
      whoCanSeeMyStory: WhoCanSeeMyStory.everyone,
      whoCanSeeMyBio: WhoCanSeeMyBio.everyone,
      whoCanSeeMyEmail: WhoCanSeeMyEmail.everyone,
      whoCanSeeMyPhone: WhoCanSeeMyPhone.everyone,
      whoCanCallMe: WhoCanCallMe.everyone,
      whoCanMessageMe: WhoCanMessageMe.everyone,
      whoCanAddMeToGroup: WhoCanAddMeToGroup.everyone,
      whoCanSeeMyReadReceipt: WhoCanSeeMyReadReceipt.everyone,
    ),
  );
}
