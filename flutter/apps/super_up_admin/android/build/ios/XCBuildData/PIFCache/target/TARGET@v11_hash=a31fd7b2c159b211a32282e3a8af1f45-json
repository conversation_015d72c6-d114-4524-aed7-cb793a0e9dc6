{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e981965328fe7a3e1e6b780133706892962", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/google_mobile_ads", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "google_mobile_ads", "INFOPLIST_FILE": "Target Support Files/google_mobile_ads/ResourceBundle-google_mobile_ads-google_mobile_ads-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_NAME": "google_mobile_ads", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e98461a32e1f57ea77e4dce6decc91869f6", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98ee23e5879c24e4285fa8c1db5ab08d58", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/google_mobile_ads", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "google_mobile_ads", "INFOPLIST_FILE": "Target Support Files/google_mobile_ads/ResourceBundle-google_mobile_ads-google_mobile_ads-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "PRODUCT_NAME": "google_mobile_ads", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e981c738c5363a343cfd7d04bb532e6bf9a", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98ee23e5879c24e4285fa8c1db5ab08d58", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/google_mobile_ads", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "google_mobile_ads", "INFOPLIST_FILE": "Target Support Files/google_mobile_ads/ResourceBundle-google_mobile_ads-google_mobile_ads-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "PRODUCT_NAME": "google_mobile_ads", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e9819dab7a4d1f6db40bdfda0e5ea73489d", "name": "Release"}], "buildPhases": [{"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98161c6cd90a68d7ff6139025231b3b427", "type": "com.apple.buildphase.sources"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98ac45fb9125fc5feffa7415bf77a16bd4", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e985634884874de029afe6859e93082a6a3", "guid": "bfdfe7dc352907fc980b868725387e9832949a301e09e0a29c86f5938644334e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e515d1db089893e905ec4787588268a5", "guid": "bfdfe7dc352907fc980b868725387e98d566883d3fac775dbdb9b08b8ec15fb1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98176c1c6b3ea1b7ec376bd45aef233ea4", "guid": "bfdfe7dc352907fc980b868725387e98c0e26b78986facd3f2088e281573c894"}], "guid": "bfdfe7dc352907fc980b868725387e9854e167b6ceb3c42e750b0b578935ad34", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [], "guid": "bfdfe7dc352907fc980b868725387e9804037656a8578d8e730f9d99c54e40e2", "name": "google_mobile_ads-google_mobile_ads", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98bfcaaabe96491183229cf5e8736513d4", "name": "google_mobile_ads.bundle", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.bundle", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 0}], "type": "standard"}