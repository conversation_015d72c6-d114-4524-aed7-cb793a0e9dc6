{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98a5d2ac47362ca55ad606637695bcd401", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/DKPhotoGallery/DKPhotoGallery-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "DKPhotoGallery", "PRODUCT_NAME": "DKPhotoGallery", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98e17b78269136b0fa65d358367e747dee", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98e117f0feecd76786d1fa50d7caf0c7fc", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/DKPhotoGallery/DKPhotoGallery-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery.modulemap", "PRODUCT_MODULE_NAME": "DKPhotoGallery", "PRODUCT_NAME": "DKPhotoGallery", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9806b305b0977e6414c51335b3c31b372b", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98e117f0feecd76786d1fa50d7caf0c7fc", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/DKPhotoGallery/DKPhotoGallery-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery.modulemap", "PRODUCT_MODULE_NAME": "DKPhotoGallery", "PRODUCT_NAME": "DKPhotoGallery", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9846887e6fc0a81d83a27744b9e1d17219", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9897b9de4da8a462806947faf56ff71efe", "guid": "bfdfe7dc352907fc980b868725387e983cc8c71ed49feb76f4cbbb8c530d7890", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98772ad9ad5398cf49d051f5b3d6b377ca", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9843f5887c8626864e9238c93f1aee7311", "guid": "bfdfe7dc352907fc980b868725387e98008c1f4bf9b2d5f7202d5cd1130017f4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986e0563cf9b2220ad7b833271a1a5bc5d", "guid": "bfdfe7dc352907fc980b868725387e984f9eadfb2a549e241e7443e39283ff45"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9891dc9f9fc0e1d02859e7ad62cbb47693", "guid": "bfdfe7dc352907fc980b868725387e98c93126d34ed91d56061b70d4230c632b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fdf4b431752bd0494b16258819c65915", "guid": "bfdfe7dc352907fc980b868725387e9828bd506a8e58a74a83e51a4ee0698f26"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983b367a280097480e9ab884d6c3e57892", "guid": "bfdfe7dc352907fc980b868725387e98018a0b8e26dca25f47ee6e6416fc87ca"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ab57399c5f81b2b099ab6abdc8037f9d", "guid": "bfdfe7dc352907fc980b868725387e98e8c64bfb95df05791ec4290cbc5494ff"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e67af78675df0ae442b208a635e91a25", "guid": "bfdfe7dc352907fc980b868725387e9827fbcd79f66dd22175cdd3ce05ac2f17"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c2899dbb35caf7cc02c837ab0a6fedd1", "guid": "bfdfe7dc352907fc980b868725387e981d1c486af0c463cadfbd45318d335fa2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9844d62d0930bb6d8f71b8ba9a9aa0c2d9", "guid": "bfdfe7dc352907fc980b868725387e98b91a93e6198817e3f8634174e11453dc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f6ff2497de281044bd322872e230eeec", "guid": "bfdfe7dc352907fc980b868725387e98c523cba3b1fce58e583b674661c0bb32"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e901ae46e0035d119202ec7e0da2f212", "guid": "bfdfe7dc352907fc980b868725387e98657fc6f1c57085c8af8ff36c761b6451"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f316ab953a7d70e2864d29558ec1e8bb", "guid": "bfdfe7dc352907fc980b868725387e9823f808cf0524d84741616cf5e2d75cae"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98902ec8fd685629d96280967080a71a7a", "guid": "bfdfe7dc352907fc980b868725387e98fa71eb92f32bbe872e25c09850c2275b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b9c17a2eab1ad8b8b5b25ad4252219be", "guid": "bfdfe7dc352907fc980b868725387e988f287002a8f7987cc9a6ed51f031f881"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9810ebb1978f26bce155b57c02c14f6e43", "guid": "bfdfe7dc352907fc980b868725387e987b52fdb34e87acc0f79048b0c3ae0f75"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f33a1b753ba65aac293a1248fac5f83a", "guid": "bfdfe7dc352907fc980b868725387e987e8f107b752a77bef6a6c9c71017f681"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e1fa4ab68bd18c3914da059d75ca497a", "guid": "bfdfe7dc352907fc980b868725387e9829c66c46c0969f409785a8a1ee5bb6fe"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aa1ea858e5a53298e2cb68c0371cd0a7", "guid": "bfdfe7dc352907fc980b868725387e98690907929b22f0e6e1f19dd54af2f7a8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bc036c912b9cebe73cc22df786d0883c", "guid": "bfdfe7dc352907fc980b868725387e98dfc0ab596579904fea077ad8b7cd8cf2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987f7a1c5cd5c3054b72295719f4ccc5e9", "guid": "bfdfe7dc352907fc980b868725387e98651cdc0315d562c24f79ca9c939ecfb4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984faeba857b9036a5d66238b5ad86639b", "guid": "bfdfe7dc352907fc980b868725387e986216995667fd8c1c34f47367b1d73644"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9852e2cbcb71dda2e4ec3b55257e14d608", "guid": "bfdfe7dc352907fc980b868725387e98d385a51b1f39d09999196c27fc52864a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987265dcad7624456ee921763f8b03c3c3", "guid": "bfdfe7dc352907fc980b868725387e98719afd72e9d2867017da4f85a0f419c8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ef6739a25b8df8918749c25af4377b38", "guid": "bfdfe7dc352907fc980b868725387e98c5e719c0d8f4d19da3a2211ac64cb8e5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b69db237e2ae875caa926e00fc5b2662", "guid": "bfdfe7dc352907fc980b868725387e98fb50130688eabd4ffbb8cd7fa5dc885f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9810e1515e0cd31dd5d16772ecd55d5775", "guid": "bfdfe7dc352907fc980b868725387e980299de115d2b1057f55a7c683d82b432"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98819f6fe7922a733a880f1f91579c88bb", "guid": "bfdfe7dc352907fc980b868725387e98f691883aa42b9dc6ade512317c58c999"}], "guid": "bfdfe7dc352907fc980b868725387e98ab21fca89785b4d5c7f52a7c7163248b", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9807daa3f9265322026266a25d0045b5bb", "guid": "bfdfe7dc352907fc980b868725387e982d14572baade11aa31f7103184a5c9d2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d7e2dd08eff3fdc559a3f5fed1d9957c", "guid": "bfdfe7dc352907fc980b868725387e9825c9a1111847cf8b7bf92583fe38cf90"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987b9e1c3b6563a543b6fbe5f0759aef8f", "guid": "bfdfe7dc352907fc980b868725387e980e628e5352232890ddc8c5490eb916ac"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9812cc64573a371c51e6cdc1c4fd688b8c", "guid": "bfdfe7dc352907fc980b868725387e98fe92c2e836d8a8fb8d1c00b1e180f444"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989d11e2d400847835a96dea825dfa8835", "guid": "bfdfe7dc352907fc980b868725387e98be47adcdfc61ef025994d1b4a91c32ac"}], "guid": "bfdfe7dc352907fc980b868725387e98e04466fdca5b7440766495a9ce05b51b", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98f8ec83da023cdc39c2ea56930d2ffc25", "targetReference": "bfdfe7dc352907fc980b868725387e98d3f65728b12dd217475d1283ee417937"}], "guid": "bfdfe7dc352907fc980b868725387e986d5ec3a15d24d2a80a6f289765a1998a", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98d3f65728b12dd217475d1283ee417937", "name": "DKPhotoGallery-DKPhotoGallery"}, {"guid": "bfdfe7dc352907fc980b868725387e98c46180aea4e87057640961e6db37df0d", "name": "SDWebImage"}, {"guid": "bfdfe7dc352907fc980b868725387e9872eabefc63c14dfe52fb0c95ad90294e", "name": "SwiftyGif"}], "guid": "bfdfe7dc352907fc980b868725387e989d0a1858a86fd6e6731ed20f88a1e515", "name": "DKPhotoGallery", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e986e90c628ccd44af657bee5ff4af2f692", "name": "DKPhotoGallery.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}