{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9800e11b606a421fe340afd27cd552c33b", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/just_audio/just_audio-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/just_audio/just_audio-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/just_audio/just_audio.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "just_audio", "PRODUCT_NAME": "just_audio", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ba6fd30a2d2c0667ddeeb471b87f3c67", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9845959a4e7823b4e91a5e15c06999deef", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/just_audio/just_audio-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/just_audio/just_audio-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/just_audio/just_audio.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "just_audio", "PRODUCT_NAME": "just_audio", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e985872c6b49f1d92292872024b383071ad", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9845959a4e7823b4e91a5e15c06999deef", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/just_audio/just_audio-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/just_audio/just_audio-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/just_audio/just_audio.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "just_audio", "PRODUCT_NAME": "just_audio", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9869811edde2fbec4b998cd7c872ea4b10", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98f68c5fdfa6a514d5f73f425c0dcaef17", "guid": "bfdfe7dc352907fc980b868725387e9833ef405b3d295f2fa909ce6b579f3f08", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9893c79e521584df042bdd543d509da2e3", "guid": "bfdfe7dc352907fc980b868725387e985edf44ee558eb0e02a279bd88b713d0c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983accfc180553b4915c165587643d93e5", "guid": "bfdfe7dc352907fc980b868725387e9857ea467ff7ad5daa775824574903a2d9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ecbbbbd845a355188aece2078b926841", "guid": "bfdfe7dc352907fc980b868725387e981caaf3d1ab2a8026a90c114bf44be1a2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f62b1559b6bd821c442834d495846439", "guid": "bfdfe7dc352907fc980b868725387e9870bdeaaf455f39bfa582beb721a16da3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98517be8c45a035e4bb96619d383e9675a", "guid": "bfdfe7dc352907fc980b868725387e981206a16fee21ead7253110c7b594aeb8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982713e1cbf819e3637ea98f0995262783", "guid": "bfdfe7dc352907fc980b868725387e985bbbea000317e6908f30d718a5db2ebb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bf6c6e152de6913ed87ae46c82c99292", "guid": "bfdfe7dc352907fc980b868725387e987a065af2610f73e182d8aef95321d80e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eb954d60f1b09dc02ac6a7a4737b93c2", "guid": "bfdfe7dc352907fc980b868725387e98ed0c24bb444769d2cf2834d23c7d03ae", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d52977f60ce27c54dac72362487fdae0", "guid": "bfdfe7dc352907fc980b868725387e98bc31eed2a685d1141d1d53ede175d69c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f0af8b8e55483791169f14e5bb821d1b", "guid": "bfdfe7dc352907fc980b868725387e98d3c6364cb38df7a80cb1b3724d09be2f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988860ca5701d838334e32fc6dde6e56b6", "guid": "bfdfe7dc352907fc980b868725387e9879c3b59c98dcb1d8281bfbadabba25fa", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98b1c6e0dac1c0f3288cdfd038dfd59299", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e5282cce46a1de9b2b458f158865eb29", "guid": "bfdfe7dc352907fc980b868725387e9825cbf0e06fc072238459211f367fd818"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eae962982d838a63e95340febed0bffb", "guid": "bfdfe7dc352907fc980b868725387e989cd4bb70a4f3907f3a8fc899a7735519"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982a82407f287565074a9c125131c4f75c", "guid": "bfdfe7dc352907fc980b868725387e98fd452e70f6d8b47643057b38e09a0773"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e02dfcfe7ee38d67e96d597642a2e832", "guid": "bfdfe7dc352907fc980b868725387e98bdef310cda13f6db235456079e1742e9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f60197eb507a4fd1055a051bcd24a4a0", "guid": "bfdfe7dc352907fc980b868725387e987b53063ddb063f9ce43482ffc36975bd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9818ca7322faa48de9aed159f5d5931178", "guid": "bfdfe7dc352907fc980b868725387e9866b5df8c0be2f36046bcc3ea0076d5ce"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f89afe2f4c129bafcb2ae217b6c9ffe1", "guid": "bfdfe7dc352907fc980b868725387e986f1d59c66a0fc74f6bef31779020698c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f874fd1eb624d681910ca4b801ce3987", "guid": "bfdfe7dc352907fc980b868725387e98b48d635eb12a83e92dded002cc742c7c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dbaf711fe7dc12ba493945cd1ada3d27", "guid": "bfdfe7dc352907fc980b868725387e983130b499cb851b93399de8e25d31a800"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9850366249360cab9c2d1bc6ce50678312", "guid": "bfdfe7dc352907fc980b868725387e989c60358b4be3f019b707262de302a95a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981353cac1821bb12f9e7c23961dafeb07", "guid": "bfdfe7dc352907fc980b868725387e987bcc1507a70fa285c58c3be70f16a10d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9803905b066bd0cd0eb7b7a2c21e648b94", "guid": "bfdfe7dc352907fc980b868725387e987d7bbd40c37ca5256ae863020302100e"}], "guid": "bfdfe7dc352907fc980b868725387e984828d07df21b03575280a435412b2bd2", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e987b9e1c3b6563a543b6fbe5f0759aef8f", "guid": "bfdfe7dc352907fc980b868725387e984dff9b9164ea7278ab55766135fcbf91"}], "guid": "bfdfe7dc352907fc980b868725387e98e02b84aa74e21e5d0824e135f776cc74", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e9898f5fd0d63861b94769f137ac6982066", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}], "guid": "bfdfe7dc352907fc980b868725387e98f1ed40c0d488e7d17a2574221f5de571", "name": "just_audio", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98f09d8f416b4b8830579237c0f3add196", "name": "just_audio.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}