{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98c3453f8f9378882d9c58a70ae5fffdb1", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/video_player_avfoundation/video_player_avfoundation-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/video_player_avfoundation/video_player_avfoundation-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/video_player_avfoundation/video_player_avfoundation.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "video_player_avfoundation", "PRODUCT_NAME": "video_player_avfoundation", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984e5109184a56ddb528ec6b7b2855b0ae", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98e035d7aad63c8c6291aa0cc47216822c", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/video_player_avfoundation/video_player_avfoundation-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/video_player_avfoundation/video_player_avfoundation-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/video_player_avfoundation/video_player_avfoundation.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "video_player_avfoundation", "PRODUCT_NAME": "video_player_avfoundation", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98d9210f07b54db7da04e749a023ff31e0", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98e035d7aad63c8c6291aa0cc47216822c", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/video_player_avfoundation/video_player_avfoundation-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/video_player_avfoundation/video_player_avfoundation-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/video_player_avfoundation/video_player_avfoundation.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "video_player_avfoundation", "PRODUCT_NAME": "video_player_avfoundation", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98cc50e126d21cf34f696d19ceaefff3c5", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c0fc9b0bb52e078b820ed7cfc6cd6d69", "guid": "bfdfe7dc352907fc980b868725387e988723089b916f18cccbe99dac90f3d161", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ad442f68b654611e41021c57b39ad3c1", "guid": "bfdfe7dc352907fc980b868725387e98bef11d7cdace33df17f590b113af770e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98715ebbb28841924e44981c0ff92e6d48", "guid": "bfdfe7dc352907fc980b868725387e98174843dff822f2ba5752af7148ed2699", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986655b878da9184a9f683fa1a14496b71", "guid": "bfdfe7dc352907fc980b868725387e98be2aed0b006656fd1c14a579764117ec", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986581410352e100951869b5429e4216e2", "guid": "bfdfe7dc352907fc980b868725387e98ff703c548986e656e017b6d98b3b0a9f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b7390e665b9e955e36e85a7517a85217", "guid": "bfdfe7dc352907fc980b868725387e9803aa67ec71a674294d887692b533af59", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98790ae95a1363689953b2fccb411c7c8e", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98a884250b276c16190d3580cc718c3878", "guid": "bfdfe7dc352907fc980b868725387e98c42ea5589c01a81b9ca6a9b23ac9733c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e53f52e3d8d67f3dabda0fa140a7ecd6", "guid": "bfdfe7dc352907fc980b868725387e98fff603d63255f55c2c58c6dc8d901ed4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980c5e0d54d705ef0912bf1af78845ddd3", "guid": "bfdfe7dc352907fc980b868725387e98666d0fe1d8b0764a8e2c57ce27c0f583"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c50c1ac6c1f5c8646f2f86d412285ecc", "guid": "bfdfe7dc352907fc980b868725387e984d0f69f8be78e8bca8ec7bd65776cb0a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dbf77c4d7c8b03d4413ca4f944761c20", "guid": "bfdfe7dc352907fc980b868725387e989e2169bc56577faa2c47b11c15f18a31"}], "guid": "bfdfe7dc352907fc980b868725387e9811d7b36500c299ef280fb8c40bf28c3c", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e987b9e1c3b6563a543b6fbe5f0759aef8f", "guid": "bfdfe7dc352907fc980b868725387e9815cbf4a87d8b760b002f94f323669536"}], "guid": "bfdfe7dc352907fc980b868725387e98765db7fd2b3dbdb331d72ad68f3022ee", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98b1ba22f939becea1bb5e2f71e8ae7f24", "targetReference": "bfdfe7dc352907fc980b868725387e98e4af9c7b061f7f7eb180b825e303ea04"}], "guid": "bfdfe7dc352907fc980b868725387e984886d1b730f66b8b699d431161a1b604", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e98e4af9c7b061f7f7eb180b825e303ea04", "name": "video_player_avfoundation-video_player_avfoundation_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e988a0a5b40b007f81bee1472e4d0fb23da", "name": "video_player_avfoundation", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98b5f237537920ce49888f6f7f73c80a6c", "name": "video_player_avfoundation.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}