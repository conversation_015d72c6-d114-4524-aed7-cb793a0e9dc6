{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e983171051dc6089d9b15b55cf6a10f46ed", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/camera_avfoundation/camera_avfoundation-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/camera_avfoundation/camera_avfoundation-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/camera_avfoundation/camera_avfoundation.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "camera_avfoundation", "PRODUCT_NAME": "camera_avfoundation", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e983000b497bb09479468c0390e594d8342", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e986e59a9a22ba447240603fa2508522e28", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/camera_avfoundation/camera_avfoundation-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/camera_avfoundation/camera_avfoundation-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/camera_avfoundation/camera_avfoundation.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "camera_avfoundation", "PRODUCT_NAME": "camera_avfoundation", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98c6c98f0462a9c32dd4b5f1d229426903", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e986e59a9a22ba447240603fa2508522e28", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/camera_avfoundation/camera_avfoundation-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/camera_avfoundation/camera_avfoundation-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/camera_avfoundation/camera_avfoundation.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "camera_avfoundation", "PRODUCT_NAME": "camera_avfoundation", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e980bc205f56ddd18721c867471c3867697", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e984d868c55bb2e01e6c62576ece88a46a5", "guid": "bfdfe7dc352907fc980b868725387e9881afd7795fc3b75eadab28e95f653da2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980ac2f427a5571500ef3bdba07180429c", "guid": "bfdfe7dc352907fc980b868725387e980da87eb93d764aa2f92d388ea8905cdb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982584046390f18b3a5b2f21848b0a2a49", "guid": "bfdfe7dc352907fc980b868725387e9815b8e9decba37fe08282cdd56332d4fe", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98427a0c7946e17a3368616827c32749b6", "guid": "bfdfe7dc352907fc980b868725387e98e080f2b7fb19b36931b8030c4c0a39ec", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9849947005b4cb0f477e69e755909d4016", "guid": "bfdfe7dc352907fc980b868725387e98ad9bdef5e51518210da8a5c2358cc47c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bcf5b9ff6252c931124b30f64588e527", "guid": "bfdfe7dc352907fc980b868725387e98294763271b28c7c700c0cd84050baa86", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d144ddaf57988e76955a1b20e4443906", "guid": "bfdfe7dc352907fc980b868725387e982dd970a1da8ee76538e0687d9b5e3739", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9805c12653474014887084f8aefb43458d", "guid": "bfdfe7dc352907fc980b868725387e98870a13c30011eb72da5e69caa033be70", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9896d32a8f452fbf403db7366aab2a6207", "guid": "bfdfe7dc352907fc980b868725387e98720643cd265094ffa74619162177f5a6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ec9b435279255e51acce60a80a9394a2", "guid": "bfdfe7dc352907fc980b868725387e98be78cba5b2209117d69a6ef4ea87c12d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c7b6f653dd7a9bddffd9bd3216d5065e", "guid": "bfdfe7dc352907fc980b868725387e98fe142b8f2751c5f7353f2e8f774555ce", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980304720e8f3d95893dfd3aa4e53f726b", "guid": "bfdfe7dc352907fc980b868725387e9824e2c2d4bc108e112260efe33cd133bc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f9cef40f68ebc943d49923c4712fcec4", "guid": "bfdfe7dc352907fc980b868725387e98b71ee8de4e2e57980939eda70c113dda", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e981a370673afe48c5e5b57fb4f303b8a37", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9869e4bb7b386dfb6041cab0478d40bca9", "guid": "bfdfe7dc352907fc980b868725387e98251fa87b14a46256a65aea8046382815"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982716038ae0366bd8b83e34ec16c78c53", "guid": "bfdfe7dc352907fc980b868725387e98b19e0eb078f9e8d47053c37a73a30240"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a099532d454d0443265211ea3e20b420", "guid": "bfdfe7dc352907fc980b868725387e98cb1e23f9e121c0d14de5bd042fede637"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9894c0f7d1d8fddccc7ef7fb49bfe27869", "guid": "bfdfe7dc352907fc980b868725387e98a65b1200d91c6695d1e4f79892e33204"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981b3f1153b2f1a26ced1e461a9cbfddd9", "guid": "bfdfe7dc352907fc980b868725387e985ee385a70cddddfd067c7d3f61037690"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988f119180ab7f87a3c1a6d87c2bc25915", "guid": "bfdfe7dc352907fc980b868725387e98cfbf34818b153ebb7fbe9cbae8bbc279"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9884e8e6f53737d3d8d29135722e6335c4", "guid": "bfdfe7dc352907fc980b868725387e98f445e05cfc77255c65487c8fe7da70b5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985fc35e1d58a935f849ba2be4d5cff268", "guid": "bfdfe7dc352907fc980b868725387e9891a460e97c199acc7f95f11aea498438"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986b897ce10378719a3fcdf8a3444fe863", "guid": "bfdfe7dc352907fc980b868725387e98a130967d0a56aac6820379751e092477"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98847c7d947d037e4a174accf6286195aa", "guid": "bfdfe7dc352907fc980b868725387e98e8d92f831579bcccd594f8d509b5d0cf"}], "guid": "bfdfe7dc352907fc980b868725387e988aa9516a25305f519908a499366583ae", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e987b9e1c3b6563a543b6fbe5f0759aef8f", "guid": "bfdfe7dc352907fc980b868725387e98a98357da29796fa7ba184eeb42eeabcf"}], "guid": "bfdfe7dc352907fc980b868725387e98a775a8254b292ef44c8d2229c94936be", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98e90c81574e4dc7bcabb9bf8386afb39a", "targetReference": "bfdfe7dc352907fc980b868725387e98b9038e7e871b75150c57ed973218b158"}], "guid": "bfdfe7dc352907fc980b868725387e988eaff77d0a06402ee4d9c53d81ecc38a", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e98b9038e7e871b75150c57ed973218b158", "name": "camera_avfoundation-camera_avfoundation_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98f08a09402d437c098acddc7bcf497e64", "name": "camera_avfoundation", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e983903b9d6299cde09dc2b081ad04abafe", "name": "camera_avfoundation.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}