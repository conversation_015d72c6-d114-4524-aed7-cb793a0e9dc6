{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98345a271a0994c145f04ce6321f356433", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/wakelock_plus", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "wakelock_plus", "INFOPLIST_FILE": "Target Support Files/wakelock_plus/ResourceBundle-thermal-wakelock_plus-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "11.0", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_NAME": "thermal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e98243642a4e764d56d4de729d955af079a", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98eab4076ac19090c24c3f4d1277e30fc2", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/wakelock_plus", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "wakelock_plus", "INFOPLIST_FILE": "Target Support Files/wakelock_plus/ResourceBundle-thermal-wakelock_plus-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "11.0", "PRODUCT_NAME": "thermal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e98065337bef4196d7e0f2db99e9839567b", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98eab4076ac19090c24c3f4d1277e30fc2", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/wakelock_plus", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "wakelock_plus", "INFOPLIST_FILE": "Target Support Files/wakelock_plus/ResourceBundle-thermal-wakelock_plus-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "11.0", "PRODUCT_NAME": "thermal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e984908856ab66f88d4a5347a5d18bee68a", "name": "Release"}], "buildPhases": [{"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e982d2687282a94532dce0129fe025dd107", "type": "com.apple.buildphase.sources"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e986707b91444412c978082a089b067d81e", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98fac8f9aca1d7acb50394f875300ee8e8", "guid": "bfdfe7dc352907fc980b868725387e98da4c52f9f675646bff93aec981e3a88a"}], "guid": "bfdfe7dc352907fc980b868725387e98d63e7e0a7d93a73c536715176da920b9", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [], "guid": "bfdfe7dc352907fc980b868725387e98783e90de49ca9314c826dcf3e387700d", "name": "wakelock_plus-thermal", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98b790b7d9d7f8a03144ea001fc2025f47", "name": "thermal.bundle", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.bundle", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 0}], "type": "standard"}