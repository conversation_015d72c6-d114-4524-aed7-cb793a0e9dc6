{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98e31842ca4e4884354b8264c89bd84755", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/DKImagePickerController", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "DKImagePickerController", "INFOPLIST_FILE": "Target Support Files/DKImagePickerController/ResourceBundle-DK<PERSON>magePickerController-DKImagePickerController-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_NAME": "DKImagePickerController", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e98fa3c3d6578d53e825386d428d2a22eb7", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98389f74c7c2b371987edcc1324814663a", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/DKImagePickerController", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "DKImagePickerController", "INFOPLIST_FILE": "Target Support Files/DKImagePickerController/ResourceBundle-DK<PERSON>magePickerController-DKImagePickerController-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "PRODUCT_NAME": "DKImagePickerController", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e989e1fd72107438e84644aa637a5fa59da", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98389f74c7c2b371987edcc1324814663a", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/DKImagePickerController", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "DKImagePickerController", "INFOPLIST_FILE": "Target Support Files/DKImagePickerController/ResourceBundle-DK<PERSON>magePickerController-DKImagePickerController-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "PRODUCT_NAME": "DKImagePickerController", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e98619e2577138d4593754d4bbc57500c85", "name": "Release"}], "buildPhases": [{"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e987415f6251944cdcaa7c90897b358b7c0", "type": "com.apple.buildphase.sources"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98b9a8eb30931c3a6d0106eca34607cb2f", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9803504529b1d7a0a1b20c299fea3b7059", "guid": "bfdfe7dc352907fc980b868725387e9846de0bc70f07a24a7b215dcc54073d4f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986727ea14cc13b7531bab7fa9c7af0946", "guid": "bfdfe7dc352907fc980b868725387e980320349d60ad842722b976ab581a5563"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9894cf9ca724075cf387d5398cc025a5d2", "guid": "bfdfe7dc352907fc980b868725387e98039a205bc6da0423d9d989c1c85fb627"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9810829eb70ddc7152194b0af63b813dbc", "guid": "bfdfe7dc352907fc980b868725387e98c8ff7727a0727463c338422493590223"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98615f85bdb260151e60e0c5596dfc3c84", "guid": "bfdfe7dc352907fc980b868725387e98c3267ce245567f139469c74369c2c7b6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9879fbc671b49bc0939f33f99e055ae15e", "guid": "bfdfe7dc352907fc980b868725387e982b09ada9ab516649cef4ae709afe83ad"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987b1dbb7263bf4bf0a54365d80a38b57a", "guid": "bfdfe7dc352907fc980b868725387e987716c83584d37ec2ae759c528d8d040c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c05da326d966d4ac2e2d29f9931794da", "guid": "bfdfe7dc352907fc980b868725387e98e44ab3d8e1a789dc9c87244fccc29231"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cbd0bba593cb75ba7eeab1f88a2f4533", "guid": "bfdfe7dc352907fc980b868725387e98c88585f87ef1e8f08236f2d2b8399a9f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e42224b1bb1b9015ccdeaf360de002f9", "guid": "bfdfe7dc352907fc980b868725387e98a7221dda33b66677cf68e6eff7dfb530"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a80efda97433915d3efdaf3f2792825e", "guid": "bfdfe7dc352907fc980b868725387e9821c7ce734ba0cd7362e69955a9e4d22e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986218c9a4927d18c71c4260577c3e0f05", "guid": "bfdfe7dc352907fc980b868725387e98d9a7d1d5004cd82cad71097498438865"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9862bc79353145ecc797b8fce7fd090d20", "guid": "bfdfe7dc352907fc980b868725387e988d0cbfa9806c35cceeaaa0384725f9cd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a9b007d8d184cdacfae29729aa6436e0", "guid": "bfdfe7dc352907fc980b868725387e981fbaa6f04c38f664b8b1039f75c5148a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9853e1d269bb51d2703351871f3f292022", "guid": "bfdfe7dc352907fc980b868725387e9850d104ae7ce0a9d90777bc9c96a4f29f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98984085a135172b0a98067cf8bb813529", "guid": "bfdfe7dc352907fc980b868725387e98370b16c9f4739e019bcdecb37e73d103"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ffdc6c68b78f6f9e3b27d7712f2b64e4", "guid": "bfdfe7dc352907fc980b868725387e98143226d10b7f59f623878dc489124290"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9841a230320ff21a47500bb8800da435fd", "guid": "bfdfe7dc352907fc980b868725387e985e606b6a5ee04d3d025d0cba08e5e488"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cbf91af7c3996f6ce6773ef990089818", "guid": "bfdfe7dc352907fc980b868725387e987dc41067a24ed883cbc3b6e37a220cca"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f36dfd4c3cb60cf0310ec0d27a9eb9d7", "guid": "bfdfe7dc352907fc980b868725387e98cf429166fade157b6f84dd04ea711dbe"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bfee4931e90f79863956014df595a5d1", "guid": "bfdfe7dc352907fc980b868725387e9893a685e7b67b9468de6eac05f7bf66e3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984135bc73a067fc50661fd6629f09552f", "guid": "bfdfe7dc352907fc980b868725387e98f18cce7e21adaa2c07faeea952665c0d"}], "guid": "bfdfe7dc352907fc980b868725387e9859deac6f50173cda25c039a8e12e3978", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [], "guid": "bfdfe7dc352907fc980b868725387e9898fccba7a2febdedb43dddbf2e949fc3", "name": "DKImagePickerController-DKImagePickerController", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98ab5e1f747dfe477b655528b07584898d", "name": "DKImagePickerController.bundle", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.bundle", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 0}], "type": "standard"}