{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e986aac232603a5990df92fa151b189bdea", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/sqlite3/sqlite3-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/sqlite3/sqlite3-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/sqlite3/sqlite3.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "sqlite3", "PRODUCT_NAME": "sqlite3", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98b7ae2547a2feb827740bc79059aad3d0", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98627274f1b5806612f6de316806ffa433", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/sqlite3/sqlite3-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/sqlite3/sqlite3-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/sqlite3/sqlite3.modulemap", "PRODUCT_MODULE_NAME": "sqlite3", "PRODUCT_NAME": "sqlite3", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98b25618508bac5902fe49ba0cb26ebfba", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98627274f1b5806612f6de316806ffa433", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/sqlite3/sqlite3-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/sqlite3/sqlite3-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/sqlite3/sqlite3.modulemap", "PRODUCT_MODULE_NAME": "sqlite3", "PRODUCT_NAME": "sqlite3", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98678448645e1676599ad363e0522f766a", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e982de07c14d6da2ab92de7645697ed191f", "guid": "bfdfe7dc352907fc980b868725387e983a77fbd83f38608d8252bf8bad7479a7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ca98938030f0f094bfff206570f618b2", "guid": "bfdfe7dc352907fc980b868725387e988498909279fea3502a9fff018f1ee27b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984884e9ef55442f8e56bd933508e3e715", "guid": "bfdfe7dc352907fc980b868725387e98153c02faa8d7c05b18c6a7a6d3e4611c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9821452e3fb81cc06bdacd94534bc21551", "guid": "bfdfe7dc352907fc980b868725387e989654ec3927d80f45a0c702151d374c82", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fbedfe1801a2d870d3d91bcae5937593", "guid": "bfdfe7dc352907fc980b868725387e9841cc1824a621b8c7724cb3f696a9fc44", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e986a4be93b0bd81546015aa191f8f04d06", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"additionalCompilerOptions": "-fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e9888a6c8417151ca225733faba633a2747", "guid": "bfdfe7dc352907fc980b868725387e9842eac746a7e121f00e2073579d50b26f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985ec51774adce4193df800aa123779436", "guid": "bfdfe7dc352907fc980b868725387e983a6bd7a41d19787f4e56affcfb8fb85d"}], "guid": "bfdfe7dc352907fc980b868725387e98fb3b9cc4c0c1e4aaf8ec16117618ca5e", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e987b9e1c3b6563a543b6fbe5f0759aef8f", "guid": "bfdfe7dc352907fc980b868725387e98838de031b1494058b4a8e1a2ada2f98d"}], "guid": "bfdfe7dc352907fc980b868725387e98c76a24e4ae0b6f77d48873578bc31030", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e9824edb03f102c3a7e85a9a8e40a7a07f5", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [], "guid": "bfdfe7dc352907fc980b868725387e98571e4e34b09fc4b3712323a8c5b27905", "name": "sqlite3", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9876b1db6d5be94b5986fb227a952d8a23", "name": "sqlite3.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}