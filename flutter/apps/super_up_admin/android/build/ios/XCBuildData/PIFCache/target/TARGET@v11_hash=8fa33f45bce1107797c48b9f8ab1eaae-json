{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e981965328fe7a3e1e6b780133706892962", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/google_mobile_ads/google_mobile_ads-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/google_mobile_ads/google_mobile_ads-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/google_mobile_ads/google_mobile_ads.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "google_mobile_ads", "PRODUCT_NAME": "google_mobile_ads", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98f60f21207bdf48370aadaa7a5944056e", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98ee23e5879c24e4285fa8c1db5ab08d58", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/google_mobile_ads/google_mobile_ads-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/google_mobile_ads/google_mobile_ads-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/google_mobile_ads/google_mobile_ads.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "google_mobile_ads", "PRODUCT_NAME": "google_mobile_ads", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ca83b02fd91b34a498c4c1d10d117548", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98ee23e5879c24e4285fa8c1db5ab08d58", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/google_mobile_ads/google_mobile_ads-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/google_mobile_ads/google_mobile_ads-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/google_mobile_ads/google_mobile_ads.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "google_mobile_ads", "PRODUCT_NAME": "google_mobile_ads", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98d212063812730634d67c1838935e42c2", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e980fec26951a25b3bfd07780112e6164a0", "guid": "bfdfe7dc352907fc980b868725387e98902fe1021f0d622770a24b19b7c4c7c7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dd373ce815ce81a6cb863132990cfe36", "guid": "bfdfe7dc352907fc980b868725387e983882ddf30b00110d4d7f75d33473d0af", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98706bfd0808c3c1133681e8fed491af31", "guid": "bfdfe7dc352907fc980b868725387e98e9e1eed0daca0f014da52d3465e764fa", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9849ff3a03935174068c34346c6be2bcaf", "guid": "bfdfe7dc352907fc980b868725387e98a555add95ca4ba83625845793ee86bf7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9844047d42c5cf38b01822177172bd5492", "guid": "bfdfe7dc352907fc980b868725387e984eedbdd32ab5a2955bb09dc20b632898", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985c1f99090524695a235a5c452376f425", "guid": "bfdfe7dc352907fc980b868725387e986c5d3a773578a9dec2e73412c9279cda", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988f648a33afa27ac3c942da609da591e8", "guid": "bfdfe7dc352907fc980b868725387e981a9f1c7e0665444d0fa1c2142a32a93b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dceb3636d4c9972447f003e455acb617", "guid": "bfdfe7dc352907fc980b868725387e9856777b0592c2d84e060b25719760c8c1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98596a562b96e70bff4accd90521a95e55", "guid": "bfdfe7dc352907fc980b868725387e98d1a4ef170e5ce6e8e71b6405e8c88632", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989e329c55f00ba3f7ad9ae27df0daaf24", "guid": "bfdfe7dc352907fc980b868725387e984cb7af1a468daa9260cd44261ccbd431", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f8a5cc4ad462f0a6747e553036b279c1", "guid": "bfdfe7dc352907fc980b868725387e98568c2db2d54037cdcb850960a58592d4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98494f0ce1537d748551c91110d235d2bd", "guid": "bfdfe7dc352907fc980b868725387e989d692e6eef804773de76cb6ce94f5cbc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9826d9ac08266887349e4fe3312d2a0e29", "guid": "bfdfe7dc352907fc980b868725387e98479e85ecf8c8f12708f961f9ca8f7789", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980cb1add949c52b556df2b9c6560f1312", "guid": "bfdfe7dc352907fc980b868725387e989b85444ee5f4545af13d61dbf4b1f1b1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98073de3d63ac17ea5eb99a561c56bbbbb", "guid": "bfdfe7dc352907fc980b868725387e983d8edf85ed4bb3048872ad6284df221a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988404dd74d97ad6da597ca31e297a160c", "guid": "bfdfe7dc352907fc980b868725387e98074af6713433650d5f1f93a9e168c0bd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d567935d10fa7c8251b661e659aacc1e", "guid": "bfdfe7dc352907fc980b868725387e98796c63024751088bef8815eb5b52b9bb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985892c665d73d1e82368ed6fd21cdccc6", "guid": "bfdfe7dc352907fc980b868725387e988ee4703b7a9279509fa1347c79075b8f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988ff4ba0a1c606231d04946e9a3d896fa", "guid": "bfdfe7dc352907fc980b868725387e981f8e54eb0b23caf3de9a592cb2d56f75", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cea9725aaeb18b99e29a244c91856a5c", "guid": "bfdfe7dc352907fc980b868725387e98825667e9fc887ec30874bd7066be964e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98427e9cb0fafc676e031e5f8b48412992", "guid": "bfdfe7dc352907fc980b868725387e9810fc3dc160f0a3fae2248b6adffa50d5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f75b541127cad9172ba980c5f10511b9", "guid": "bfdfe7dc352907fc980b868725387e980b5d723aeb7ed37a16b30fde964e117e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987d83aa384cb21ac1679af262319405af", "guid": "bfdfe7dc352907fc980b868725387e98408e8875be5331a6e276f0ba97717fa5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9852656473d813e658670a4762f44047c7", "guid": "bfdfe7dc352907fc980b868725387e98a13e606cbbfd39a170e763f81b8c82b0", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e985da9e827eef44a73ab95aff9462753c7", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e986a924c12176f7d971d1369c95f902d1f", "guid": "bfdfe7dc352907fc980b868725387e98d75dbaba054496dd79478e3a813a4b11"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988a9078e020e8d4f061ce1bb4feee5418", "guid": "bfdfe7dc352907fc980b868725387e98df0ee5433c07246708169e049172d5d1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98edfbd6859175f3469991e3b2cccdd73e", "guid": "bfdfe7dc352907fc980b868725387e986e8aa70d5ff7ea2b411cfaa0eafe0bf0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e19ba0847b6b86a53397268ae6d7c919", "guid": "bfdfe7dc352907fc980b868725387e9833d688150d4088c88c496137f24083c4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9895e957630be221d09c75cbd3ff6bcc43", "guid": "bfdfe7dc352907fc980b868725387e98612a28d72355bf828b1ccdeae4938dbe"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bfc5db813fde67b2f153f1f03021ae33", "guid": "bfdfe7dc352907fc980b868725387e98acc76b5a6e2dee4b35c34f97e8719a6b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9876238821a7c14a1d49c919a5fa7d5e64", "guid": "bfdfe7dc352907fc980b868725387e98725462256367d7175fe2c8a58bb394e3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9869029dcd1bb116c96dac1be4fa77f3ce", "guid": "bfdfe7dc352907fc980b868725387e98c7fceff8aa1c9963e568ef13c0b13e2b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98553205fd8ff729d476ac90e7f493c901", "guid": "bfdfe7dc352907fc980b868725387e985fa9b94f8821fa1c5a93a1545946d3e6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9820dbfa60f184816003cd1a1493ddb391", "guid": "bfdfe7dc352907fc980b868725387e98867bad1779ddad94afa609acc92619b3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9800a5661db0826febf660b9dbcde77cb2", "guid": "bfdfe7dc352907fc980b868725387e986129084658ae8b9865f396c234dd101b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98609d8dbff302e5b5f85358a0a1794c23", "guid": "bfdfe7dc352907fc980b868725387e98d8606de1858b249a216e0dbc5af72728"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98748d6ab6ca48f69b971a9db398dce289", "guid": "bfdfe7dc352907fc980b868725387e98724c8e1d99d2c74ab82eba9ef466f477"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e84096bda89f6b3e0f5dd8c5bc7115d9", "guid": "bfdfe7dc352907fc980b868725387e98a4a00d396eb0d95112770e20322b1238"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eb45f48b06bea781f26cc723bebb0130", "guid": "bfdfe7dc352907fc980b868725387e98a9be7990e8cc991d3755a5719e2113bb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982a65f8cc0d0d11cde98e9e74e8e1ab77", "guid": "bfdfe7dc352907fc980b868725387e98295da050c32531a1fb4dc9ae0264d4b1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d17e8fe6283c2a1a9ac019719aee775a", "guid": "bfdfe7dc352907fc980b868725387e9880f2ee6abd1379108d014dfdbed14684"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9801653c016a2649363c52b4946b4bd5fe", "guid": "bfdfe7dc352907fc980b868725387e9818149a58bc2fac8254874717d38b8794"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9856df5697222ff418c400a958052eaf5e", "guid": "bfdfe7dc352907fc980b868725387e98857c614f9e18316081bd4fa1cdcd591b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c21adc59eab3ccf11bb7f1e11353b7d3", "guid": "bfdfe7dc352907fc980b868725387e9822cfad4dd4e8c69d543447c4eca835a9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9877becc4b6790e76fe61f446bbd2cd528", "guid": "bfdfe7dc352907fc980b868725387e98de00129e3270ecf6ff3b9a1449b10c5c"}], "guid": "bfdfe7dc352907fc980b868725387e98ef387155c7be58dd1abcd21a411a4e6e", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e987b9e1c3b6563a543b6fbe5f0759aef8f", "guid": "bfdfe7dc352907fc980b868725387e98c961a969fb41a7bdab90c538b30528fd"}], "guid": "bfdfe7dc352907fc980b868725387e9817c34e700eb0a17b1be9eca81b57986b", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e984da9032ff76790acb4b37e742746ef15", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e98cd53d937e824fad9f4527eeeb684cb40", "name": "Google-Mobile-Ads-SDK"}, {"guid": "bfdfe7dc352907fc980b868725387e9804037656a8578d8e730f9d99c54e40e2", "name": "google_mobile_ads-google_mobile_ads"}, {"guid": "bfdfe7dc352907fc980b868725387e988efdc4dd0ac29b43123295eca853f4ed", "name": "webview_flutter_wkwebview"}], "guid": "bfdfe7dc352907fc980b868725387e9811f9347c979613c5502173cd5b43060d", "name": "google_mobile_ads", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98b1f16b2926bf8e21151b2cb149aa6540", "name": "google_mobile_ads.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}