{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98019775dab6a7cbf6d21ab7b0409d0183", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "webview_flutter_wkwebview", "PRODUCT_NAME": "webview_flutter_wkwebview", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e985fae6b87488d5841a802320a818efa16", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e988c50ab2cd4ebcec00cc6f5661e9e7842", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "webview_flutter_wkwebview", "PRODUCT_NAME": "webview_flutter_wkwebview", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98414486957297b26b63085bb5d04ea1e6", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e988c50ab2cd4ebcec00cc6f5661e9e7842", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "webview_flutter_wkwebview", "PRODUCT_NAME": "webview_flutter_wkwebview", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e989e3864c381e8cd3bdfc2c4d825d50db0", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c99900d4af8dfdc2084faa0f40c9d8d9", "guid": "bfdfe7dc352907fc980b868725387e987e3ab67ff067a0667347ae00b6643af5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9892dbd39438e307cb66dc8b599cd0b8ba", "guid": "bfdfe7dc352907fc980b868725387e987495f8ccaf1a4edccdfe635bd76aec49", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985282e8cfc9bc6781f39792157652d734", "guid": "bfdfe7dc352907fc980b868725387e988a4d8467a9f70a539c52bd0a9f185005", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986d291e934bd26ae1f1dcbb7029f505c1", "guid": "bfdfe7dc352907fc980b868725387e982f3615616c8a5659ba4abfa524b38ff4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9884ed99866d416a464d6d48b182d8cb55", "guid": "bfdfe7dc352907fc980b868725387e98f5dd1e01b661a294b35500491392794c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985e817027e7bd09991b0d3ffa27b3071e", "guid": "bfdfe7dc352907fc980b868725387e98ac621291ca1fdec07c596316c45c1210", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9831e232d2754f2a5ea17ffc0bf89395aa", "guid": "bfdfe7dc352907fc980b868725387e9803cb715dda6650b7ac5db6b8d29da03c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b1c8751d24eb2e9869bad13aa927e8bc", "guid": "bfdfe7dc352907fc980b868725387e9807244fe85cdfe10232da0d35623e11d1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fb5b4a6347ec73e75980c6518476a21e", "guid": "bfdfe7dc352907fc980b868725387e983ac5cfbf360375aa40e3c0e81b62dcdd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982146710c022d8d75e5a2f24dd0b84194", "guid": "bfdfe7dc352907fc980b868725387e980a3df9aea3edff24bf00b0a8c06071d2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989e33526ca5bf11c19fa2dabd9cc1afdd", "guid": "bfdfe7dc352907fc980b868725387e98a6a1d47d8bcc4df9e26e7ee289d8ca3c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98656e72474a6f2c89ec80054ac9f6b72d", "guid": "bfdfe7dc352907fc980b868725387e98d85850261fb2fd393a15dde204b6cd7e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9890054a1acc04ba660158e5bb29142b74", "guid": "bfdfe7dc352907fc980b868725387e989ecbcf063b095cc26fdbc091075ab3a0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9842adc67f8b5e11c0e90d80bf4b72dca7", "guid": "bfdfe7dc352907fc980b868725387e9871d1f347b50ebbf147b895c3364831f0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98da74c3f7b14cf5722ca119d47af9cd9b", "guid": "bfdfe7dc352907fc980b868725387e98c35dabe3f310c52bb8ed53c37c351d62", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bad5a9325809c8b34e8fddcbfce6b21a", "guid": "bfdfe7dc352907fc980b868725387e9867d9810154e54210f740aca06d3cb99e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ec7c607646f65770284d839a67789060", "guid": "bfdfe7dc352907fc980b868725387e98ec5984f4522c72bf6646f9b546911306", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98978a6825c0c0f90863be3584b61ddbf7", "guid": "bfdfe7dc352907fc980b868725387e98c692fb79cffa24b1c7d11baacf51be99", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98da69663f4871ae20c80f3b028cf036b9", "guid": "bfdfe7dc352907fc980b868725387e98570d870d0286ca31a18087f877c52ccf", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fbce6e90feebc824196f8170a6e75d4e", "guid": "bfdfe7dc352907fc980b868725387e98ee2cd3337d247bf3e5f38d9dcfdae1da", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988f2ce56f78a212f1a6b0794ffdf00cb4", "guid": "bfdfe7dc352907fc980b868725387e983022b15b4212a4983c49523518384a9f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989a6f9b8d7d2fe76fdf674f0c7d058dc2", "guid": "bfdfe7dc352907fc980b868725387e98cda28dd29effb4126d1b489ad7324914", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fe15e5506db4a4b1baef33aba888f640", "guid": "bfdfe7dc352907fc980b868725387e9889c44bb88cf6ed4313d2be576d6083a8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9852b3cf89cef22f71b8cbccf90cc1de4b", "guid": "bfdfe7dc352907fc980b868725387e98778e94507a017bcd613648e4c8cd7c3e", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e989ff9a5d6552892b5d286adcea746ab88", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e980917917eabb23854d63a3bd890ba08d6", "guid": "bfdfe7dc352907fc980b868725387e9809e6e7cc484d0511a421e234f6b2d083"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c2cfa5cf9ec420b70399764e87f7297b", "guid": "bfdfe7dc352907fc980b868725387e9853e16c23ec35abb510024684d3b4f0a0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98daf861cc49221b631ae9a216c66b1024", "guid": "bfdfe7dc352907fc980b868725387e984c2cc0331e7997d70e55c8659a191ac6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9887e032c16df4450182a68bebcb801270", "guid": "bfdfe7dc352907fc980b868725387e98f5df2c5c0995c48aedcdcbf0da433ab7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a78f1bb5c6720e745dd215727be<PERSON>bad", "guid": "bfdfe7dc352907fc980b868725387e98534759c6baeb12ec0110bce1f092d6d8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9802f4577f86d7289ade66a2fde78ffc61", "guid": "bfdfe7dc352907fc980b868725387e98d58e1a6becedd93a99fedb42904e4de8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bc578da39232921f1ebdcc642a7129c5", "guid": "bfdfe7dc352907fc980b868725387e985b6f6c63548186ea6e6dc709e1571f6d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9821954d62eb4268e8181ad8b1df07d4e5", "guid": "bfdfe7dc352907fc980b868725387e988676fb0a02f6e8470451315b6347f0fe"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985f392c2bb80ef4882ee33b53513c78bd", "guid": "bfdfe7dc352907fc980b868725387e982c94d34ba18a25d21c94e56e08c5f275"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9834317b453b2fbec321ac8760a14c7477", "guid": "bfdfe7dc352907fc980b868725387e98930208444d7e3e02002d571878fe4215"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a780802e71677b07b6b70ccf98d57973", "guid": "bfdfe7dc352907fc980b868725387e98b7d138a357ae67f7cb739bc6860dad26"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e1270ee150959fb4365152febe3a34a4", "guid": "bfdfe7dc352907fc980b868725387e98545843b5eb4f9d86aa4ba4e88a9e7c94"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ffc294aabacdc96fe486fbf1d5f1cb8f", "guid": "bfdfe7dc352907fc980b868725387e988be49b424dcb2c71c0dc020b069b022d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b23952f92057c3f9e0e83e52e7645e74", "guid": "bfdfe7dc352907fc980b868725387e98588c4c186feeeb08d807abfab56698b8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9893c890f221056b260645fd09305b167e", "guid": "bfdfe7dc352907fc980b868725387e98def860148149f1bd9dafa421cff606d0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989d849944492d2f7a95da5a67820142dc", "guid": "bfdfe7dc352907fc980b868725387e98e987e5966f69c58adbca6116db8b3362"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982b0aeea0f7391c53c45b0438db64a28f", "guid": "bfdfe7dc352907fc980b868725387e980c7178d80d3bcfd6aceddb11fa6acddf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98105c3214bfca34a1af9092ce12de1207", "guid": "bfdfe7dc352907fc980b868725387e986975d80d40a3725c6c191733f423191c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9803a8cbed580b6214b7ba7fa11b2b6133", "guid": "bfdfe7dc352907fc980b868725387e98aec1e48fc1daee6961bb7b47b9e287c5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9886db50782343da3caaca9dd3e9085ad9", "guid": "bfdfe7dc352907fc980b868725387e983e1f302ce0943fc824e71970a1263da3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a24402f78a011c4cc8f75246b3291702", "guid": "bfdfe7dc352907fc980b868725387e98b5259903c2ac4a08aaab3f82ee7c28b9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fc7d01f04bf5f44b25afa14dff779aec", "guid": "bfdfe7dc352907fc980b868725387e98292b03e407829d142bdd2aa396c08125"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983acbec55909cd22acd58f15ff4b110b9", "guid": "bfdfe7dc352907fc980b868725387e98ab9d78f6529fb16492db232e6a68bf94"}], "guid": "bfdfe7dc352907fc980b868725387e98648feff8e468f1bd4cdc3a5f2aabe21c", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e987b9e1c3b6563a543b6fbe5f0759aef8f", "guid": "bfdfe7dc352907fc980b868725387e982c9fcbd10585c2d835654be88cce4e8a"}], "guid": "bfdfe7dc352907fc980b868725387e9896a21967f9c8b0d81d07382098ff774a", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e983aaf03d85757f08f35a377bc35e054ce", "targetReference": "bfdfe7dc352907fc980b868725387e987c93e943aa0a38b5f6684beaf6b4a3a1"}], "guid": "bfdfe7dc352907fc980b868725387e98ea8760202b9f038bd2829c78435d2fcd", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e987c93e943aa0a38b5f6684beaf6b4a3a1", "name": "webview_flutter_wkwebview-webview_flutter_wkwebview_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e988efdc4dd0ac29b43123295eca853f4ed", "name": "webview_flutter_wkwebview", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e980823710353e0487822d6da09bf8d6254", "name": "webview_flutter_wkwebview.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}