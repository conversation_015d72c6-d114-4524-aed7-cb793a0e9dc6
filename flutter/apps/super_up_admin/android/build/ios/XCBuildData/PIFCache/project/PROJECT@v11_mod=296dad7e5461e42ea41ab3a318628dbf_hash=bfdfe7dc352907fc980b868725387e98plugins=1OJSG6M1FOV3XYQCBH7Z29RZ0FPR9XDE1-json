{"appPreferencesBuildSettings": {}, "buildConfigurations": [{"buildSettings": {"ALWAYS_SEARCH_USER_PATHS": "NO", "CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED": "YES", "CLANG_ANALYZER_NONNULL": "YES", "CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION": "YES_AGGRESSIVE", "CLANG_CXX_LANGUAGE_STANDARD": "gnu++14", "CLANG_CXX_LIBRARY": "libc++", "CLANG_ENABLE_MODULES": "YES", "CLANG_ENABLE_OBJC_ARC": "YES", "CLANG_ENABLE_OBJC_WEAK": "YES", "CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING": "YES", "CLANG_WARN_BOOL_CONVERSION": "YES", "CLANG_WARN_COMMA": "YES", "CLANG_WARN_CONSTANT_CONVERSION": "YES", "CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS": "YES", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "YES_ERROR", "CLANG_WARN_DOCUMENTATION_COMMENTS": "YES", "CLANG_WARN_EMPTY_BODY": "YES", "CLANG_WARN_ENUM_CONVERSION": "YES", "CLANG_WARN_INFINITE_RECURSION": "YES", "CLANG_WARN_INT_CONVERSION": "YES", "CLANG_WARN_NON_LITERAL_NULL_CONVERSION": "YES", "CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF": "YES", "CLANG_WARN_OBJC_LITERAL_CONVERSION": "YES", "CLANG_WARN_OBJC_ROOT_CLASS": "YES_ERROR", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "YES", "CLANG_WARN_RANGE_LOOP_ANALYSIS": "YES", "CLANG_WARN_STRICT_PROTOTYPES": "YES", "CLANG_WARN_SUSPICIOUS_MOVE": "YES", "CLANG_WARN_UNGUARDED_AVAILABILITY": "YES_AGGRESSIVE", "CLANG_WARN_UNREACHABLE_CODE": "YES", "CLANG_WARN__DUPLICATE_METHOD_MATCH": "YES", "COPY_PHASE_STRIP": "NO", "DEBUG_INFORMATION_FORMAT": "dwarf", "ENABLE_STRICT_OBJC_MSGSEND": "YES", "ENABLE_TESTABILITY": "YES", "GCC_C_LANGUAGE_STANDARD": "gnu11", "GCC_DYNAMIC_NO_PIC": "NO", "GCC_NO_COMMON_BLOCKS": "YES", "GCC_OPTIMIZATION_LEVEL": "0", "GCC_PREPROCESSOR_DEFINITIONS": "POD_CONFIGURATION_DEBUG=1 DEBUG=1 $(inherited)", "GCC_WARN_64_TO_32_BIT_CONVERSION": "YES", "GCC_WARN_ABOUT_RETURN_TYPE": "YES_ERROR", "GCC_WARN_UNDECLARED_SELECTOR": "YES", "GCC_WARN_UNINITIALIZED_AUTOS": "YES_AGGRESSIVE", "GCC_WARN_UNUSED_FUNCTION": "YES", "GCC_WARN_UNUSED_VARIABLE": "YES", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "MTL_ENABLE_DEBUG_INFO": "INCLUDE_SOURCE", "MTL_FAST_MATH": "YES", "ONLY_ACTIVE_ARCH": "YES", "PRODUCT_NAME": "$(TARGET_NAME)", "STRIP_INSTALLED_PRODUCT": "NO", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "DEBUG", "SWIFT_OPTIMIZATION_LEVEL": "-<PERSON><PERSON>", "SWIFT_VERSION": "5.0", "SYMROOT": "${SRCROOT}/../build"}, "guid": "bfdfe7dc352907fc980b868725387e983f8723597279233a8f05ceff9fe3456f", "name": "Debug"}, {"buildSettings": {"ALWAYS_SEARCH_USER_PATHS": "NO", "CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED": "YES", "CLANG_ANALYZER_NONNULL": "YES", "CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION": "YES_AGGRESSIVE", "CLANG_CXX_LANGUAGE_STANDARD": "gnu++14", "CLANG_CXX_LIBRARY": "libc++", "CLANG_ENABLE_MODULES": "YES", "CLANG_ENABLE_OBJC_ARC": "YES", "CLANG_ENABLE_OBJC_WEAK": "YES", "CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING": "YES", "CLANG_WARN_BOOL_CONVERSION": "YES", "CLANG_WARN_COMMA": "YES", "CLANG_WARN_CONSTANT_CONVERSION": "YES", "CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS": "YES", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "YES_ERROR", "CLANG_WARN_DOCUMENTATION_COMMENTS": "YES", "CLANG_WARN_EMPTY_BODY": "YES", "CLANG_WARN_ENUM_CONVERSION": "YES", "CLANG_WARN_INFINITE_RECURSION": "YES", "CLANG_WARN_INT_CONVERSION": "YES", "CLANG_WARN_NON_LITERAL_NULL_CONVERSION": "YES", "CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF": "YES", "CLANG_WARN_OBJC_LITERAL_CONVERSION": "YES", "CLANG_WARN_OBJC_ROOT_CLASS": "YES_ERROR", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "YES", "CLANG_WARN_RANGE_LOOP_ANALYSIS": "YES", "CLANG_WARN_STRICT_PROTOTYPES": "YES", "CLANG_WARN_SUSPICIOUS_MOVE": "YES", "CLANG_WARN_UNGUARDED_AVAILABILITY": "YES_AGGRESSIVE", "CLANG_WARN_UNREACHABLE_CODE": "YES", "CLANG_WARN__DUPLICATE_METHOD_MATCH": "YES", "COPY_PHASE_STRIP": "NO", "DEBUG_INFORMATION_FORMAT": "dwarf-with-dsym", "ENABLE_NS_ASSERTIONS": "NO", "ENABLE_STRICT_OBJC_MSGSEND": "YES", "GCC_C_LANGUAGE_STANDARD": "gnu11", "GCC_NO_COMMON_BLOCKS": "YES", "GCC_PREPROCESSOR_DEFINITIONS": "POD_CONFIGURATION_PROFILE=1 $(inherited)", "GCC_WARN_64_TO_32_BIT_CONVERSION": "YES", "GCC_WARN_ABOUT_RETURN_TYPE": "YES_ERROR", "GCC_WARN_UNDECLARED_SELECTOR": "YES", "GCC_WARN_UNINITIALIZED_AUTOS": "YES_AGGRESSIVE", "GCC_WARN_UNUSED_FUNCTION": "YES", "GCC_WARN_UNUSED_VARIABLE": "YES", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "MTL_ENABLE_DEBUG_INFO": "NO", "MTL_FAST_MATH": "YES", "PRODUCT_NAME": "$(TARGET_NAME)", "STRIP_INSTALLED_PRODUCT": "NO", "SWIFT_COMPILATION_MODE": "wholemodule", "SWIFT_OPTIMIZATION_LEVEL": "-O", "SWIFT_VERSION": "5.0", "SYMROOT": "${SRCROOT}/../build"}, "guid": "bfdfe7dc352907fc980b868725387e98431d6fbac4fcf14f03a16b06b7267016", "name": "Profile"}, {"buildSettings": {"ALWAYS_SEARCH_USER_PATHS": "NO", "CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED": "YES", "CLANG_ANALYZER_NONNULL": "YES", "CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION": "YES_AGGRESSIVE", "CLANG_CXX_LANGUAGE_STANDARD": "gnu++14", "CLANG_CXX_LIBRARY": "libc++", "CLANG_ENABLE_MODULES": "YES", "CLANG_ENABLE_OBJC_ARC": "YES", "CLANG_ENABLE_OBJC_WEAK": "YES", "CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING": "YES", "CLANG_WARN_BOOL_CONVERSION": "YES", "CLANG_WARN_COMMA": "YES", "CLANG_WARN_CONSTANT_CONVERSION": "YES", "CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS": "YES", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "YES_ERROR", "CLANG_WARN_DOCUMENTATION_COMMENTS": "YES", "CLANG_WARN_EMPTY_BODY": "YES", "CLANG_WARN_ENUM_CONVERSION": "YES", "CLANG_WARN_INFINITE_RECURSION": "YES", "CLANG_WARN_INT_CONVERSION": "YES", "CLANG_WARN_NON_LITERAL_NULL_CONVERSION": "YES", "CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF": "YES", "CLANG_WARN_OBJC_LITERAL_CONVERSION": "YES", "CLANG_WARN_OBJC_ROOT_CLASS": "YES_ERROR", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "YES", "CLANG_WARN_RANGE_LOOP_ANALYSIS": "YES", "CLANG_WARN_STRICT_PROTOTYPES": "YES", "CLANG_WARN_SUSPICIOUS_MOVE": "YES", "CLANG_WARN_UNGUARDED_AVAILABILITY": "YES_AGGRESSIVE", "CLANG_WARN_UNREACHABLE_CODE": "YES", "CLANG_WARN__DUPLICATE_METHOD_MATCH": "YES", "COPY_PHASE_STRIP": "NO", "DEBUG_INFORMATION_FORMAT": "dwarf-with-dsym", "ENABLE_NS_ASSERTIONS": "NO", "ENABLE_STRICT_OBJC_MSGSEND": "YES", "GCC_C_LANGUAGE_STANDARD": "gnu11", "GCC_NO_COMMON_BLOCKS": "YES", "GCC_PREPROCESSOR_DEFINITIONS": "POD_CONFIGURATION_RELEASE=1 $(inherited)", "GCC_WARN_64_TO_32_BIT_CONVERSION": "YES", "GCC_WARN_ABOUT_RETURN_TYPE": "YES_ERROR", "GCC_WARN_UNDECLARED_SELECTOR": "YES", "GCC_WARN_UNINITIALIZED_AUTOS": "YES_AGGRESSIVE", "GCC_WARN_UNUSED_FUNCTION": "YES", "GCC_WARN_UNUSED_VARIABLE": "YES", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "MTL_ENABLE_DEBUG_INFO": "NO", "MTL_FAST_MATH": "YES", "PRODUCT_NAME": "$(TARGET_NAME)", "STRIP_INSTALLED_PRODUCT": "NO", "SWIFT_COMPILATION_MODE": "wholemodule", "SWIFT_OPTIMIZATION_LEVEL": "-O", "SWIFT_VERSION": "5.0", "SYMROOT": "${SRCROOT}/../build"}, "guid": "bfdfe7dc352907fc980b868725387e989c63c53754d36800c8b5a5c73030d5d2", "name": "Release"}], "classPrefix": "", "defaultConfigurationName": "Release", "developmentRegion": "en", "groupTree": {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98d0b25d39b515a574839e998df229c3cb", "path": "../Podfile", "sourceTree": "SOURCE_ROOT", "type": "file"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98cd1a61b74956c7caa8c912e7a130f7b3", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/audio_service-0.18.18/darwin/audio_service/Sources/audio_service/AudioServicePlugin.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b5c843c43e4903a703fd39c432dd1b25", "path": "../../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/audio_service-0.18.18/darwin/audio_service/Sources/audio_service/include/audio_service/AudioServicePlugin.h", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98a6f272f93b97861cc5504b5293102965", "name": "audio_service", "path": "audio_service", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98114b3a8a646948c34259148d948282e7", "name": "include", "path": "include", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98907a4d4c079c23819e3d8a2fa4061f58", "name": "audio_service", "path": "audio_service", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c0963a8f400744e3a4e28bf5f59feb5d", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ec9ddbc53be845cc71efe3aa83dafbab", "name": "audio_service", "path": "audio_service", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9825f0861594586ea01b3048271a1562a7", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e8e48ab58f09a343191c97fc95556cee", "name": "audio_service", "path": "audio_service", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ea4ee53f98cfa9424b05e7cda921831a", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981a9e1e85be080f82bf9114d8163fde5f", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98be142004615a8828d6cd286ffd2a3dbf", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98202c614c7e66f62c7fe277d8d4d2f2aa", "name": "super_up_admin", "path": "super_up_admin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a52df127f4868645f8c95d5c3416924a", "name": "apps", "path": "apps", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98167eb8b1aed43155cd06852e07819b32", "name": "flutter", "path": "flutter", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989c17ceec01795aed96e4cf008befe792", "name": "source2", "path": "source2", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985ad4a48c69f5164822565f44efe15c4e", "name": "Desktop", "path": "Desktop", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98828d0a3b0e6d998cfde3ff0591794aa2", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9869b238aa46d60e80c264f82c8aa78842", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9862ef27efaa439891c0b479299bf7b154", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d5fe64ee59fe6e8be62fb976e0b2f912", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98099ed0ab0893796179ede2b34d72f31d", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9863ebd97820ba5b93207d6f4235be25f1", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b673b726765805208e8e6d7674a47758", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a8ac12c3f860aae027b6300e3e5103f0", "name": "..", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/audio_service-0.18.18/darwin/audio_service/Sources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e989965a360751e74628fb1118f217ffe25", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/audio_service-0.18.18/darwin/audio_service.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98b1eec69740a22f3c1844afb0fd07a55e", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/audio_service-0.18.18/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98955baa73f4b42273523436428c0cdc42", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98031b311675ae1441312fa3d2d7aa5a44", "path": "audio_service.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9806d4f29372455dd7d0fe42b3dd3c3439", "path": "audio_service-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9818983b59b8b5762b2025447b2ae2862e", "path": "audio_service-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c02f0aa39832e12c9a7c3190ebaba6ee", "path": "audio_service-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b3bacb46cfb6088ca43eec104113864f", "path": "audio_service-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98919b556e94562e9da8873e5ba0755381", "path": "audio_service.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98a221a6fedaff15f85f2b61d1ceea37d7", "path": "audio_service.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9891679e5f3dee225aa2e961451928fab1", "name": "Support Files", "path": "../../../../Pods/Target Support Files/audio_service", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983911f85e112e8e8ff079a034f7e52c16", "name": "audio_service", "path": "../.symlinks/plugins/audio_service/darwin", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e980d4c65485007f62a06bb113368947f74", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/audio_session-0.2.2/ios/audio_session/Sources/audio_session/AudioSessionPlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98cd16b996c86596c22655663c0c1835eb", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/audio_session-0.2.2/ios/audio_session/Sources/audio_session/DarwinAudioSession.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984f97df4e27dc25f4e3fd93675633b270", "path": "../../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/audio_session-0.2.2/ios/audio_session/Sources/audio_session/include/audio_session/AudioSessionPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9847ad63e104c5f08c61c14cb8ebba0474", "path": "../../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/audio_session-0.2.2/ios/audio_session/Sources/audio_session/include/audio_session/DarwinAudioSession.h", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9822cbebd1e9ac5d3d5b3d2b83601ff5ae", "name": "audio_session", "path": "audio_session", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ea1d7d8947c704e1d315c604bd7fd4d0", "name": "include", "path": "include", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986d6799bad93055b0cc5cd4445251fe40", "name": "audio_session", "path": "audio_session", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9833bdf1512fb09a6362cbbeec19b630c9", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a595af90c6cabe9d22a61ac0887a9e3b", "name": "audio_session", "path": "audio_session", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980fff3cbc6ee015b0d7077f4fb45ea518", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98922a2f82cbfb53f630acdfee8c1bbd1b", "name": "audio_session", "path": "audio_session", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9851733ac8f60248d76080491cb4c4ca86", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9885a6f8c245bb32ea648134c83e4bc42d", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983f7c06ebeb84acd65aa473e5fde48764", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98623e85504253c259a3ef1725fd0b5087", "name": "super_up_admin", "path": "super_up_admin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9843b90ce82784a6a8acb47959a988704a", "name": "apps", "path": "apps", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ca58528a37db10fba72ac0fbdf2ad743", "name": "flutter", "path": "flutter", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980966d4f106a06c5c7b6107780b4e17d0", "name": "source2", "path": "source2", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9847a7cf5c956821f1f7d5d85dfdad0b0f", "name": "Desktop", "path": "Desktop", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983dde3769e6184634f71089bab4c6340a", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982b7f3f7d607e27f81a2a0d45bb5d5fad", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a48d7af916eee684e8ad928aeb2d783f", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987f528688610782fc558d392d21c126db", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98093a50ddc18452971df31a7ad5abd33b", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98477839f040bd010e94a5155abd3d7f71", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9801d52f03492b914bf673378bc98e6e7c", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98271c6d5a9e95fcf08efc6ff488809ea8", "name": "..", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/audio_session-0.2.2/ios/audio_session/Sources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98dea8da6d33a34668c91f17819b202a08", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/audio_session-0.2.2/ios/audio_session.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e985a2137029b29caf949d92fbf2ded04c8", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/audio_session-0.2.2/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e987f05ab914386efa788c5c35912a51145", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98b1bce1c32d2766e337dc48e4a37849ec", "path": "audio_session.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b34d66a2513559af644a8a6d78dcf782", "path": "audio_session-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98adec4e9b96b533571e99ecadb181cf59", "path": "audio_session-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9847eb3ddc9bd8ff7d04ebc2f4b73ad52d", "path": "audio_session-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98af2b4379ae9ccf2876447a4712ce3f0d", "path": "audio_session-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e988ff130eadbf027166cbbb67db85503dd", "path": "audio_session.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9837c79105aff4b460aa477d43ff910f2d", "path": "audio_session.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98ef0ad5f41dd8beaa5e818a2a7db78f2a", "name": "Support Files", "path": "../../../../Pods/Target Support Files/audio_session", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98735f55397f226ca81b5e3d8855fa3477", "name": "audio_session", "path": "../.symlinks/plugins/audio_session/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983b8380d9a549a218f6899e4f95664f5a", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/background_downloader-8.8.0/ios/Classes/BackgroundDownloaderPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9864c13f0a0e973703119f3ecf4936feab", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/background_downloader-8.8.0/ios/Classes/BackgroundDownloaderPlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98be2b752248ab0d8b9bf66144c14972f2", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/background_downloader-8.8.0/ios/Classes/BDPlugin.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98db8e1c8d7fad8165de654f7e56a9185b", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/background_downloader-8.8.0/ios/Classes/Helpers.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98cb6bbc5f9ee004c16f202e61d53b5a18", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/background_downloader-8.8.0/ios/Classes/HoldingQueue.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98bdbf5ee5cf5f10cc6f2b49183c6b364e", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/background_downloader-8.8.0/ios/Classes/Notifications.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98118843db85d1de2e0ef32f249a35186b", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/background_downloader-8.8.0/ios/Classes/OpenFile.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98e58ad9f4e826c530c87f64bd6454b973", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/background_downloader-8.8.0/ios/Classes/ParallelDownloader.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98bc1f600517e68acbcc7e8340eb07c035", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/background_downloader-8.8.0/ios/Classes/Permissions.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e981098a4afb6f3e63dddd3b01d0ca36eb8", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/background_downloader-8.8.0/ios/Classes/SharedStorage.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e982ed158bcfb2aadfe6e2ebdf2efd95b62", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/background_downloader-8.8.0/ios/Classes/Task.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e982e02a3e5df42967af1d269995151ead3", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/background_downloader-8.8.0/ios/Classes/TaskFunctions.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9887ff3cae09b58b45128f3ca8bfb94c9f", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/background_downloader-8.8.0/ios/Classes/Uploader.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e981e8a92da4b6b8b2637238f495798675f", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/background_downloader-8.8.0/ios/Classes/UrlSessionDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98e5af5f1b3ebecb8f5cd98d1c96528593", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/background_downloader-8.8.0/ios/Classes/WiFi.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98d7b9b75ace922de150f1a7a6ea351470", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98880cbc6638cb845e308d9a7852dd5534", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988ec3b731b698ca58820be35cc5b78546", "name": "background_downloader", "path": "background_downloader", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9842edb81bfb26c64ce25221ebb1c0b290", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9852346f8856e98142f1cfaa71f738cad5", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98dcaceba6bbe67f47f47283abc205e0a7", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98cdefc8fdeccdedb90a693a4f01da7549", "name": "super_up_admin", "path": "super_up_admin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9807b7b2e7ca7e9b2d9e071166526d135d", "name": "apps", "path": "apps", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b2ee155f6c7504434cf52f8e2ffe1829", "name": "flutter", "path": "flutter", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98be46535f36ca480f49770fad19ee808a", "name": "source2", "path": "source2", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f4188f5e4ddbc34481d25c21b5508ad7", "name": "Desktop", "path": "Desktop", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981552611fe1a1259798c3096410f41715", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988baf6d6eb6ecfecc0f20703e5d22cf69", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98cd3ed6b581e86cafc4b17d6b91b5bf29", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986d192a9544603b961b44d7db9968ba19", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e3a76c61fecf49d09b59729a3121a961", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985695f3fffa51386bf2fdfd1777ee3fcc", "name": "..", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/background_downloader-8.8.0/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e988eeedfde45240cbb9b426739fe308dd1", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/background_downloader-8.8.0/ios/background_downloader.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e986f5209ac59605486de9d675eb10b79ec", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/background_downloader-8.8.0/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e980da9b23521a75cc417c065c78035b51d", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98828e48660891b9f38ff8d518a891358e", "path": "background_downloader.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e987fa9e6419a90572ba5b39ab7ef3ccc38", "path": "background_downloader-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98ad0b6b126e8bd7ea4a969d75b5b82247", "path": "background_downloader-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98871ea973575c892830daa28d70330bd8", "path": "background_downloader-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982e9161cd6c55d6844e747f9279a79646", "path": "background_downloader-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98196e393fa3dda1ad9604be43e641d81a", "path": "background_downloader.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9801c98d4106e43a96de9a55dbc28c768d", "path": "background_downloader.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98b37e7fe885dbf7a11a93bfb302fbfe6d", "name": "Support Files", "path": "../../../../Pods/Target Support Files/background_downloader", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98080e853a1d7a797b29bf8a541be5c041", "name": "background_downloader", "path": "../.symlinks/plugins/background_downloader/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e984ee40f6e492cc7c2e9067b31ab101250", "path": "../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.17+4/ios/camera_avfoundation/Sources/camera_avfoundation/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98da4e394c9cf1832e49cd0fe4e06c2ece", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9833d30bf7f1f6a9e618ef5e4dc77eee2e", "name": "camera_avfoundation", "path": "camera_avfoundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e6a12d1bd562f60b7bca3d824fdedadd", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9814c13b39f8e21b42807b62579744708f", "name": "camera_avfoundation", "path": "camera_avfoundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98bd63ecc93ccfa40076908bd51083dc25", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9888d99f3885c76072b2ac624806f3570b", "name": "camera_avfoundation", "path": "camera_avfoundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986e8d110ee8561b8f7dc5e5d789b20432", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98758b90bc78f9ba38e44da5396100b8f0", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f459f340227c12f44c407b2ce7a466b9", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982d344dbf01c80f621e5a4649b4b1d1b3", "name": "super_up_admin", "path": "super_up_admin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f9cf88544093b7ce6b7cf2acb013816c", "name": "apps", "path": "apps", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980a52e7e2c16caedd278f861563ea684a", "name": "flutter", "path": "flutter", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986ac3cfd4c4e52c8f6c3bb40646812660", "name": "source2", "path": "source2", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98008cc39d89e10dce19bb2ff52f7c050c", "name": "Desktop", "path": "Desktop", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988ab7f369194f2a1620cc117ce9d00b35", "name": "..", "path": ".", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e982716038ae0366bd8b83e34ec16c78c53", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.17+4/ios/camera_avfoundation/Sources/camera_avfoundation/CameraPermissionUtils.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98a099532d454d0443265211ea3e20b420", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.17+4/ios/camera_avfoundation/Sources/camera_avfoundation/CameraPlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9894c0f7d1d8fddccc7ef7fb49bfe27869", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.17+4/ios/camera_avfoundation/Sources/camera_avfoundation/CameraProperties.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e981b3f1153b2f1a26ced1e461a9cbfddd9", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.17+4/ios/camera_avfoundation/Sources/camera_avfoundation/FLTCam.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e988f119180ab7f87a3c1a6d87c2bc25915", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.17+4/ios/camera_avfoundation/Sources/camera_avfoundation/FLTCamMediaSettingsAVWrapper.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9884e8e6f53737d3d8d29135722e6335c4", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.17+4/ios/camera_avfoundation/Sources/camera_avfoundation/FLTSavePhotoDelegate.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e985fc35e1d58a935f849ba2be4d5cff268", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.17+4/ios/camera_avfoundation/Sources/camera_avfoundation/FLTThreadSafeEventChannel.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e986b897ce10378719a3fcdf8a3444fe863", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.17+4/ios/camera_avfoundation/Sources/camera_avfoundation/messages.g.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98847c7d947d037e4a174accf6286195aa", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.17+4/ios/camera_avfoundation/Sources/camera_avfoundation/QueueUtils.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984d868c55bb2e01e6c62576ece88a46a5", "path": "../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.17+4/ios/camera_avfoundation/Sources/camera_avfoundation/include/camera_avfoundation-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980ac2f427a5571500ef3bdba07180429c", "path": "../../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.17+4/ios/camera_avfoundation/Sources/camera_avfoundation/include/camera_avfoundation/CameraPermissionUtils.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982584046390f18b3a5b2f21848b0a2a49", "path": "../../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.17+4/ios/camera_avfoundation/Sources/camera_avfoundation/include/camera_avfoundation/CameraPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98427a0c7946e17a3368616827c32749b6", "path": "../../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.17+4/ios/camera_avfoundation/Sources/camera_avfoundation/include/camera_avfoundation/CameraPlugin_Test.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9849947005b4cb0f477e69e755909d4016", "path": "../../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.17+4/ios/camera_avfoundation/Sources/camera_avfoundation/include/camera_avfoundation/CameraProperties.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98bcf5b9ff6252c931124b30f64588e527", "path": "../../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.17+4/ios/camera_avfoundation/Sources/camera_avfoundation/include/camera_avfoundation/FLTCam.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d144ddaf57988e76955a1b20e4443906", "path": "../../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.17+4/ios/camera_avfoundation/Sources/camera_avfoundation/include/camera_avfoundation/FLTCam_Test.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9805c12653474014887084f8aefb43458d", "path": "../../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.17+4/ios/camera_avfoundation/Sources/camera_avfoundation/include/camera_avfoundation/FLTCamMediaSettingsAVWrapper.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9896d32a8f452fbf403db7366aab2a6207", "path": "../../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.17+4/ios/camera_avfoundation/Sources/camera_avfoundation/include/camera_avfoundation/FLTSavePhotoDelegate.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ec9b435279255e51acce60a80a9394a2", "path": "../../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.17+4/ios/camera_avfoundation/Sources/camera_avfoundation/include/camera_avfoundation/FLTSavePhotoDelegate_Test.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c7b6f653dd7a9bddffd9bd3216d5065e", "path": "../../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.17+4/ios/camera_avfoundation/Sources/camera_avfoundation/include/camera_avfoundation/FLTThreadSafeEventChannel.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980304720e8f3d95893dfd3aa4e53f726b", "path": "../../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.17+4/ios/camera_avfoundation/Sources/camera_avfoundation/include/camera_avfoundation/messages.g.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f9cef40f68ebc943d49923c4712fcec4", "path": "../../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.17+4/ios/camera_avfoundation/Sources/camera_avfoundation/include/camera_avfoundation/QueueUtils.h", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98aa0e0b4bc273db8f43c1f5538fa235e8", "name": "camera_avfoundation", "path": "camera_avfoundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a0e37b52866ce8b926372d1086fe34d0", "name": "include", "path": "include", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986bc7f6dfef326590987fe43d412ec78a", "name": "camera_avfoundation", "path": "camera_avfoundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a820e8f54591fb8ef320e27e0bab9a7e", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9850c448116dee5aad3f7eb1181a711c42", "name": "camera_avfoundation", "path": "camera_avfoundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98da1654c4c55dfdc0fe5cead0dbd6b604", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a362fbe8d0ee66ec1b27607fa53fe9a3", "name": "camera_avfoundation", "path": "camera_avfoundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ca0e11f46f8195733dcc53714be6066a", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a6181c4ea2c571f6d8e2bae9e854a933", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d578a0ec77993908f6591e36fa233b6a", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9883aa420ca68872a188f0dd65b07fb469", "name": "super_up_admin", "path": "super_up_admin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ff071eea09bfc6a60c068657a0eca692", "name": "apps", "path": "apps", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989d173ca8eae67c63197cb5fde87a940c", "name": "flutter", "path": "flutter", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b0bb2881e388f7398fab7748ba071e39", "name": "source2", "path": "source2", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98506f8aecf26d2c893d7af841099cdbc0", "name": "Desktop", "path": "Desktop", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9844d223c1cdd9e8e53cfaaf973598ab79", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98727e8c909faeccc13472afa79af06464", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e7495c756307cbfcdd8a2302bb0bb194", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9829befbed04c29b9247f29f713f279c81", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9829748b39366b500c257278e8124e606d", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ee8c7659cefebb662887497e4c2c8f82", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983c1f73c5dc455af4a3db4bf92b4cd881", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f684ff6418107daf5e0e863e011a770e", "name": "..", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.17+4/ios/camera_avfoundation/Sources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98d1e336c28fe8f87dcd18e726b3842a72", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.17+4/ios/camera_avfoundation.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e983ca25f6a6a43a93413a59f770396545e", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.17+4/ios/camera_avfoundation/Sources/camera_avfoundation/include/CameraPlugin.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98f091696fb971ade01a0d0d23dead9ad7", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.17+4/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98cc4fc79c808b23aeeb2be00bd44d9584", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e9834a034dbcec8b284b4733f948426fbce", "path": "camera_avfoundation.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9869e4bb7b386dfb6041cab0478d40bca9", "path": "camera_avfoundation-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98e8980a07a15b0b45cc6d75898239cfd5", "path": "camera_avfoundation-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983e82b08437bf0d6eacbe8e6c48068e39", "path": "camera_avfoundation-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e983171051dc6089d9b15b55cf6a10f46ed", "path": "camera_avfoundation.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e986e59a9a22ba447240603fa2508522e28", "path": "camera_avfoundation.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98778350e58271e837c68062e8aec02957", "path": "ResourceBundle-camera_avfoundation_privacy-camera_avfoundation-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98363c8a322018b224f760ab279b1398eb", "name": "Support Files", "path": "../../../../Pods/Target Support Files/camera_avfoundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983bd9bcf07b43a5eab14acad166a4cd17", "name": "camera_avfoundation", "path": "../.symlinks/plugins/camera_avfoundation/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e981f8a2d7ee081d2b4c8c8b7b520d6accf", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/connectivity_plus-6.1.0/darwin/connectivity_plus/Sources/connectivity_plus/ConnectivityPlusPlugin.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e988bf55716a78aae81f01a847ef973cc3a", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/connectivity_plus-6.1.0/darwin/connectivity_plus/Sources/connectivity_plus/ConnectivityProvider.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e984eef15cb8414250a8c5fe8dbc8508a89", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/connectivity_plus-6.1.0/darwin/connectivity_plus/Sources/connectivity_plus/PathMonitorConnectivityProvider.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98e25ec7873fe250c3265b21e2c16d407a", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/connectivity_plus-6.1.0/darwin/connectivity_plus/Sources/connectivity_plus/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e988c11c014fae21a4da3ffe15e21ee7ae4", "name": "connectivity_plus", "path": "connectivity_plus", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98392cbef9c0efdfb6843dca9c29935cde", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98995ea31cb5783386532dbb19496ee1bc", "name": "connectivity_plus", "path": "connectivity_plus", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98639d926f01002835099c789a17024b4e", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9836a50b4cd14967031da08a0338292f2b", "name": "connectivity_plus", "path": "connectivity_plus", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98718087a76d1d9a97927ce8b3474e7cbc", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98175703336c5516e1bb349c776f44eb8b", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b6fbf1a4cb32c202a85fb794dfd3b71a", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d29703d6b9cccbc593679ea7f774a114", "name": "super_up_admin", "path": "super_up_admin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983cd3ea4a422ff7f08c58c3a6d0230cc9", "name": "apps", "path": "apps", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a4e2b6d0eeb5c631f3826895d20556f1", "name": "flutter", "path": "flutter", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e3c299062fce0c2adeb85e3fa379125a", "name": "source2", "path": "source2", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e5e3e972016e51f2bc122da14f105aef", "name": "Desktop", "path": "Desktop", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987d959f60f824c26669b63bb422ecfaf7", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986b97af0d248a4e1bcda9b685ef56aee8", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98593a0a404ee0897e3900d9833c4e0db3", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98cd7768b5735f74b6345aad247ba05b28", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c00129b757b4b969bcceb27785a41f03", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98de6e93cbc35c7f4a1e962a2d59bcacbc", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9842a58d81815f3689ed05ecd04a1c557f", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98cafbd8fba4a8f048393d7bbb5d5cb32f", "name": "..", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/connectivity_plus-6.1.0/darwin/connectivity_plus/Sources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98acb485e55d4a3c44eaa4ca824edbe6b3", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/connectivity_plus-6.1.0/darwin/connectivity_plus.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e9889ced766a3698f8e6ac469073792adcc", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/connectivity_plus-6.1.0/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e980076de8f0ddcdc2217df6d1c61ea17b6", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98c9bdb51ce618832753b524867cb95d42", "path": "connectivity_plus.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e987519c301f59917e60e3dc81ff173a4a8", "path": "connectivity_plus-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98a3a59c82c1b706144582eb7e3ebb154f", "path": "connectivity_plus-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98aa50d874c2b2c623e130fc22ae56c599", "path": "connectivity_plus-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982dbc8afa7a45476e8ec3153c376a4e86", "path": "connectivity_plus-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98bdacff66f0c5dace7c17fb0daf3fa359", "path": "connectivity_plus.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9859bff3ba451bbfca4e6f1893d2f2aff8", "path": "connectivity_plus.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e984d3f4abe9b22d1b03f78b7d4139e570c", "path": "ResourceBundle-connectivity_plus_privacy-connectivity_plus-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98ada424e849c9af1cc291a5750b210fb2", "name": "Support Files", "path": "../../../../Pods/Target Support Files/connectivity_plus", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98bec14c7a1fcadea38a107f7c309d692b", "name": "connectivity_plus", "path": "../.symlinks/plugins/connectivity_plus/darwin", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9846c99bff8bb002dfb3771865401e223e", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/device_info_plus-11.1.0/ios/device_info_plus/Sources/device_info_plus/FPPDeviceInfoPlusPlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98beab888610d09cfb5d6916b0c3a5090d", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/device_info_plus-11.1.0/ios/device_info_plus/Sources/device_info_plus/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}, {"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989578db0172efd25a96919c62602d8504", "path": "../../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/device_info_plus-11.1.0/ios/device_info_plus/Sources/device_info_plus/include/device_info_plus/FPPDeviceInfoPlusPlugin.h", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98903ab578208ccd4a6f7df4aa93d3a29e", "name": "device_info_plus", "path": "device_info_plus", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98071c1b1a019c6e86c6484c24f99a2173", "name": "include", "path": "include", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985c5f2a851a3afcf5aa016dbf5e5c695c", "name": "device_info_plus", "path": "device_info_plus", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9803be62bd268a9661abbe8fb6a3f38ec2", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9891ce2dc3a4748ae1be9ed56e7a310371", "name": "device_info_plus", "path": "device_info_plus", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988527dbc3f28c03e05e6bdf16f5bf4325", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987ea6711367aea92588c96ac1c01e8cf4", "name": "device_info_plus", "path": "device_info_plus", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988c745a64bed9868079cc5ee82df328af", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ccd4ad2ffe5904d5d99844e74a611050", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98645feeff41233b74599e939c425fb954", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f25a74957f6ee239897f325dc29c5852", "name": "super_up_admin", "path": "super_up_admin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981154726d1472136646ef1bbea52c8dab", "name": "apps", "path": "apps", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98938bdd30a4a17480db60c3e6698c0f4e", "name": "flutter", "path": "flutter", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981014773f9983b29f3715fb0e38e669a1", "name": "source2", "path": "source2", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d934891d08bc401452235441d0631b70", "name": "Desktop", "path": "Desktop", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98de65be3f4a219243c276ce7b4831f52a", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98430cd3845f9059495387fec366587339", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c7aa24b8b7dd5ea800fac1b17b9fcc9b", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9836893e7e4a4c103a04e2058cff3adb2b", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985d4c1cb158ef2963f163664def0ee1c7", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98fd76865d02c3f26fa3f13869e438ad20", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986920d2901ddd5aee40a352945701f58b", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98cc18d1e09f233308c00b3699409e8036", "name": "..", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/device_info_plus-11.1.0/ios/device_info_plus/Sources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e986f481e8c19f2055f47679be10facc01e", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/device_info_plus-11.1.0/ios/device_info_plus.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98ee7ae8a6a9de1f1abe3351327fe99356", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/device_info_plus-11.1.0/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9856f0f4b5d6d1d2034a83c56aa49a73d8", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e980732e40a008010779e6b593147527432", "path": "device_info_plus.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98982d92304a29c484a4c30e8bcb0c4593", "path": "device_info_plus-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98675e309f243d3f65a4d97b40e745c12f", "path": "device_info_plus-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981ffe048417075ef8cf839ab357a0c649", "path": "device_info_plus-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988a5a13e0143def24cb7a739304002087", "path": "device_info_plus-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9814d318c33aa5f558f2533d4754feecec", "path": "device_info_plus.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98c34016233ad834b1623e8d3296581c6e", "path": "device_info_plus.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98e51cfed02ee1d0f84e7d9d9a968e3589", "path": "ResourceBundle-device_info_plus_privacy-device_info_plus-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e988fafc8349b2ac6cf17f32b3ec3e4d859", "name": "Support Files", "path": "../../../../Pods/Target Support Files/device_info_plus", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98edd4208b96c2b4520e41629bfb76da3c", "name": "device_info_plus", "path": "../.symlinks/plugins/device_info_plus/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983760d608cbf3f63642b6e830ef49a620", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/file_picker-8.1.3/ios/Classes/FileInfo.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ad7b411166dfc91a6842a83147107cc8", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/file_picker-8.1.3/ios/Classes/FileInfo.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98727bde083a599c081a6f3386906eeecc", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/file_picker-8.1.3/ios/Classes/FilePickerPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9878f0e9049b62e472a80d9a0576bee751", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/file_picker-8.1.3/ios/Classes/FilePickerPlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983385c1a4f0d4dd2de447cb1bbb8d9296", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/file_picker-8.1.3/ios/Classes/FileUtils.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98c6915ef300f55040b312b6ac89058b8d", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/file_picker-8.1.3/ios/Classes/FileUtils.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d72663f90bb1e314f618883767ea3b83", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/file_picker-8.1.3/ios/Classes/ImageUtils.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e985a06941a2735f75a4e9b18c04e90a4f6", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/file_picker-8.1.3/ios/Classes/ImageUtils.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98ea95fd23d91e89de1db363351c0910fc", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98cc37e552af38e047f1abd139b9708f68", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/file_picker-8.1.3/ios/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e980cab3502a8fb1c0a1de32c5d4204a841", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983b1e6eaa851756a08a36df0875dec636", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9811448f17c67a1a9a6e77e112e9f81978", "name": "file_picker", "path": "file_picker", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e9a1cfed78b5935ef6b61ed1c58d94b2", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9800b3bd6840bc5718b19a2d3ec75add92", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9864e458a3ce174c3cb823e258f166a2c4", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a358216adc8a9c2a82a20cdb3a4583b0", "name": "super_up_admin", "path": "super_up_admin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989c739796120bdab6a8231852292de961", "name": "apps", "path": "apps", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983feeefb9c8bbd42db313edd46de4f72c", "name": "flutter", "path": "flutter", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988d83c44cdd775efabe1ade9fc6263c06", "name": "source2", "path": "source2", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c51d02c1bdeb51922468debf0e7667db", "name": "Desktop", "path": "Desktop", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9878bfe6670163dead1c4269be228524f7", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98474cadc168a88e523bb9a8d9526cf795", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ec206024ccdd82141e93ee9bfec5b19f", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9806b2b0138f3008016a4b1b00d7b4feba", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e0c6754e84c53462cdf2aa241b2df6b2", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9835cdfd3cf1f156e42054ad66a61860bb", "name": "..", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/file_picker-8.1.3/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98bb2c9c92bf5bd11e0a6e4d23d6bf9a21", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/file_picker-8.1.3/ios/file_picker.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e985fd12ab5c6a2f45e89d41c6129d6cca4", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/file_picker-8.1.3/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98883ab7396bdea9809e8de3afe719de7a", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e9859250bca5969d97b5bf816be361a10a7", "path": "file_picker.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e984092f04f9679a5efdec5fb45b3f84002", "path": "file_picker-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e980190d07ae316fbe6090ad1cf81bf17f5", "path": "file_picker-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f1e765a6d634dcd6ad055a84c5ab688b", "path": "file_picker-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98bc9303df8d0c7b646aabc31e2b6cd094", "path": "file_picker-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9800ab50bb2e221fc8ab79dca3298f8d0b", "path": "file_picker.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98bdf15db0b2306813ee39ba0608111a8b", "path": "file_picker.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98440a63cd66a7a808d526cf4ed6a6ca26", "path": "ResourceBundle-file_picker_ios_privacy-file_picker-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e987d8320b481ffbaf4a4a7707b7f01f237", "name": "Support Files", "path": "../../../../Pods/Target Support Files/file_picker", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f4b077a24308f76699ec568ba2ec08d8", "name": "file_picker", "path": "../.symlinks/plugins/file_picker/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98287d43208866861d76b7ef7953a9ebd6", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/file_saver-0.2.14/ios/Classes/Dialog.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985215970147080993cf7f22b8922a2f7a", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/file_saver-0.2.14/ios/Classes/FileSaverPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9884b649c2e83b6ffac5012efaead4e667", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/file_saver-0.2.14/ios/Classes/FileSaverPlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98b2d8a328f95aaf61526811c359fc0a41", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/file_saver-0.2.14/ios/Classes/SwiftFileSaverPlugin.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e985f5676c9369021a91d6659430d0fa869", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d540f46adcfbdff4939344e2bac22355", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9876a59acba81e9e2abb90adf35012163d", "name": "file_saver", "path": "file_saver", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986afe06ddbcd4786cdf1a1b9503c9f99c", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9886f87daed1c1d224824d8e1bf2eaa350", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989ed5a28cd60405a31df305ed7e7111e7", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9895a26527cec7712f22db51a00859a024", "name": "super_up_admin", "path": "super_up_admin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9825196511962a9a3c0056f2c8c2230eb7", "name": "apps", "path": "apps", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d523b505b5bf0fd789ca6eff2a3c7c83", "name": "flutter", "path": "flutter", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b1009aff50bceadc319205fa0cb4f13e", "name": "source2", "path": "source2", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986d3ffc5dca30214edec73f85b6ce9645", "name": "Desktop", "path": "Desktop", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98617da30a9ac943fcfa0ab03293721100", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9824e237127ae573527ad2f717715b82c9", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98002530db0eef7e91fe4407bc0df0195e", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b8f7dd117f953521c8b27dd9e184b16f", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987158a880e74956d7fe6ae311681cc2c3", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98bfc0846009efbdacee044461261284f2", "name": "..", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/file_saver-0.2.14/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98e8aeeb720d36d59abd024481c5b2ed7a", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/file_saver-0.2.14/ios/file_saver.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e983431c5308e0a7c047f7436275c1aa2d4", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/file_saver-0.2.14/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98399686074d3a7206c8136d2221b4dd3e", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e981c053889d9e67d134c63e23bf71ddbbc", "path": "file_saver.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98867d70ac017e4a5a574aafc4306c2b1a", "path": "file_saver-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9890bf2ddd6560b640ad939ab964740a09", "path": "file_saver-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c42322fb6844739359f456cf51c9f111", "path": "file_saver-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984dc89b64078f6fe133619e836ab5ee8d", "path": "file_saver-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e984dd9427e9561bf53364c2f8336a0cdfd", "path": "file_saver.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98b580d27c64643c4d732842f597954c94", "path": "file_saver.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e986ed19512e1f291d9a217ab686ed1c841", "name": "Support Files", "path": "../../../../Pods/Target Support Files/file_saver", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ed375e6eb2c15ecf0972b072615db28e", "name": "file_saver", "path": "../.symlinks/plugins/file_saver/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98c9681d43fa20725e303684b366d7eace", "path": "Flutter.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98f6c44318f03b6378d7a7f62c300161bc", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e987dc11784de99d4e74a2b27f16d79dbad", "path": "Flutter.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e984dbfeadf199196186ccd3cf7a3b02750", "path": "Flutter.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98c217900a4efc813e49e8c9c7222e6d20", "name": "Support Files", "path": "../Pods/Target Support Files/Flutter", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98aa116fb78cd115a2e97c3f5f36f19c05", "name": "Flutter", "path": "../Flutter", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98086514fedc12bdd544e317fb6e23f1fc", "path": "../../../../../../../../../../../.pub-cache/git/flutter_callkit_incoming-2f74feb49f61441ddc678bb43afd2c6e8a208c82/ios/Classes/Call.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9863eb6f07a5d540e5e1691ed3cb735261", "path": "../../../../../../../../../../../.pub-cache/git/flutter_callkit_incoming-2f74feb49f61441ddc678bb43afd2c6e8a208c82/ios/Classes/CallkitIncomingAppDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e985bc593d5f682dcdfc5f7ecbac3a5a463", "path": "../../../../../../../../../../../.pub-cache/git/flutter_callkit_incoming-2f74feb49f61441ddc678bb43afd2c6e8a208c82/ios/Classes/CallManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b7095083d146cd7600855f0368c30c21", "path": "../../../../../../../../../../../.pub-cache/git/flutter_callkit_incoming-2f74feb49f61441ddc678bb43afd2c6e8a208c82/ios/Classes/FlutterCallkitIncomingPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9831838b33e5260be3cb93d52bbed423ec", "path": "../../../../../../../../../../../.pub-cache/git/flutter_callkit_incoming-2f74feb49f61441ddc678bb43afd2c6e8a208c82/ios/Classes/FlutterCallkitIncomingPlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98bed406d88de010b7e19ac358c4ce0d5d", "path": "../../../../../../../../../../../.pub-cache/git/flutter_callkit_incoming-2f74feb49f61441ddc678bb43afd2c6e8a208c82/ios/Classes/NSUserActivity.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98ff1d876b315a34df37c699bfb2da20f9", "path": "../../../../../../../../../../../.pub-cache/git/flutter_callkit_incoming-2f74feb49f61441ddc678bb43afd2c6e8a208c82/ios/Classes/StringUtils.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9890b0820e7a9567f1e80ab5b83f50ac37", "path": "../../../../../../../../../../../.pub-cache/git/flutter_callkit_incoming-2f74feb49f61441ddc678bb43afd2c6e8a208c82/ios/Classes/SwiftFlutterCallkitIncomingPlugin.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98a6e764fb17917a4c30dde06045a3ccea", "path": "../../../../../../../../../../../.pub-cache/git/flutter_callkit_incoming-2f74feb49f61441ddc678bb43afd2c6e8a208c82/ios/Classes/WeakArray.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e981e8005aad3d5cfbd57b974a111622efd", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9830137ebedd317c70a80db92998ccfd70", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981e1f69c690bf020fc689a9fea6f9f03b", "name": "flutter_callkit_incoming", "path": "flutter_callkit_incoming", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984a8e6b909d456561efae0b25b365ace2", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987dc497de9272f6a32db72f0f4b39eb38", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986292393cf71343b17f60f858bbcaaf45", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e594a6006949cf51872911e1228696db", "name": "super_up_admin", "path": "super_up_admin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9837e81aa420e7017a2ce7099c87ddf23d", "name": "apps", "path": "apps", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987c89c5e50c91a223c689d99a027ba68c", "name": "flutter", "path": "flutter", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a2fefe21bb284df161788f3607889b7b", "name": "source2", "path": "source2", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980ad51b28e1bdbe386c97c33c00f2ab1c", "name": "Desktop", "path": "Desktop", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981635229546c62ba025dab3a3ab9aaae1", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98db45b78f4e74e081ad1d0f809f6c0106", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98fb7da4e269dccd9333cc55c5efaa3bba", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983caaf00f9b34377d0bbaec339445a43f", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981da12986edd03ec1a7816ead3ba4b94b", "name": "..", "path": "../../../../../../../../../../.pub-cache/git/flutter_callkit_incoming-2f74feb49f61441ddc678bb43afd2c6e8a208c82/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98bb498c0f4473781635922fe0361c8f5f", "path": "../../../../../../../../../../.pub-cache/git/flutter_callkit_incoming-2f74feb49f61441ddc678bb43afd2c6e8a208c82/ios/flutter_callkit_incoming.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e982ce4600de4961e92b5c1a56d1c418b00", "path": "../../../../../../../../../../.pub-cache/git/flutter_callkit_incoming-2f74feb49f61441ddc678bb43afd2c6e8a208c82/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9888facd922b9728e781460a784ac256db", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98521fbc3e273563ec3014527fadd22e8a", "path": "flutter_callkit_incoming.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e985d6743146ac4bdb17b8b1e3d4c14c560", "path": "flutter_callkit_incoming-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98d2634170ba2d8548115bf544024cc885", "path": "flutter_callkit_incoming-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9858fa297d7386c39c72251af8e7bdf818", "path": "flutter_callkit_incoming-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98081b7167e8cca875f98866c841c622e8", "path": "flutter_callkit_incoming-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e986f4d4be2467a0f9ba2922a3c42669013", "path": "flutter_callkit_incoming.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9847edf88671b86f6b0c7218dd2daee60a", "path": "flutter_callkit_incoming.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98759f7606471a27b6210a8589ce014a38", "name": "Support Files", "path": "../../../../Pods/Target Support Files/flutter_callkit_incoming", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9861f803c0486d1f09aa9f1b62ff19922a", "name": "flutter_callkit_incoming", "path": "../.symlinks/plugins/flutter_callkit_incoming/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98204b929e3620cce8c26514b19ab9decc", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_fgbg-0.6.0/ios/Classes/FlutterFGBGPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98216329205cf01bf48ac2b935ee0ab474", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_fgbg-0.6.0/ios/Classes/FlutterFGBGPlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98881404fdc67aaf12d9258eb08b35b739", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_fgbg-0.6.0/ios/Classes/SwiftFlutterFgbgPlugin.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e981b7a2eb06c7d42ddad5703586561972c", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e44879f0f397f965eb3569ae5439707a", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987129ff0ea04dd5b72e76cb5166e908a0", "name": "flutter_fgbg", "path": "flutter_fgbg", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98139e0d7cd8a106fd8d866976e91ab934", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c2c41d9713d969c0fefdff48504e52d3", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b0e4f3a2474d764135e2dd8bcae00924", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98906e216ae9b584874e704d2f5a07db98", "name": "super_up_admin", "path": "super_up_admin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f653f042c68ecfc68299b93765daffba", "name": "apps", "path": "apps", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98706924514427a6e90c61e636112616de", "name": "flutter", "path": "flutter", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9871a07ffe513edeeba63cb3520fed0478", "name": "source2", "path": "source2", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c017e49e08304fa6138c1b94f40deaa6", "name": "Desktop", "path": "Desktop", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98cbe868ee0e9cef4c76fd65742d446edd", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98aa8cbf5de550364ecee468b0a230ee33", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98db0b8d89fa59ec4efea95462f6be40f4", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ad1e18fb804b57c03c15e8b0e506126b", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9885fa1df62f94f9ab1210414a05551c01", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9830531b71c960cf213121e0f591530187", "name": "..", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_fgbg-0.6.0/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98a09f99f919245112534891d4c8d05c3d", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_fgbg-0.6.0/ios/flutter_fgbg.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98cb67453f534e19bc008ecfb29da7d505", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_fgbg-0.6.0/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9841d67081d884beb1a37351adfc8ac70e", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e981f30b4480397f5b9e1cd810089bd857a", "path": "flutter_fgbg.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e985535944193bc8360825c9d2f03fd804c", "path": "flutter_fgbg-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98b857a4ae79b8bc808d24e0f5b3e51a39", "path": "flutter_fgbg-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980c4cbb07b7334cea068380a372be3582", "path": "flutter_fgbg-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ce6a6105094da19e87da73f09a844211", "path": "flutter_fgbg-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9817e9e04a8418864220a27e86f9afd12a", "path": "flutter_fgbg.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9854363af526c2b0159b42859d4a9b9dcd", "path": "flutter_fgbg.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98b76bbe5bfbfcf86ca92cff2bd6b9eafc", "name": "Support Files", "path": "../../../../Pods/Target Support Files/flutter_fgbg", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c6850d461cda051aa6edc087ff6278d3", "name": "flutter_fgbg", "path": "../.symlinks/plugins/flutter_fgbg/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9878069ccbbed9e15ce2c68227c84f578d", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_local_notifications-18.0.0/ios/Classes/ActionEventSink.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9874771bf99a603be6d8290918e23f80a2", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_local_notifications-18.0.0/ios/Classes/ActionEventSink.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98361b8dad66979ce39922facd6a87eded", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_local_notifications-18.0.0/ios/Classes/Converters.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e988bb05adfb4c865764fbc1f8a4cbd55e8", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_local_notifications-18.0.0/ios/Classes/Converters.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e8740a653b8b265c06e063b56b184aed", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_local_notifications-18.0.0/ios/Classes/FlutterEngineManager.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9813ed30d2782f71cece8134909c0c301d", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_local_notifications-18.0.0/ios/Classes/FlutterEngineManager.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9837981a68abf6a11039ae95c2898e415d", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_local_notifications-18.0.0/ios/Classes/FlutterLocalNotificationsPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9861604c0ce18774f9204de1005083d87e", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_local_notifications-18.0.0/ios/Classes/FlutterLocalNotificationsPlugin.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e986b4e2bc589b65b94308a959b4a894c7e", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e9851ae7a517aad063a38bbf7df0c098d06", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_local_notifications-18.0.0/ios/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98967513be433747497f7477bec94c3f17", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f6ba7501ceeaded963560976503b7c7a", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d39fbb2b1d348fd5c05d123b39bf036b", "name": "flutter_local_notifications", "path": "flutter_local_notifications", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f5ab8dba72bf5bdbb1c5dc35299e8cfe", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989b7d989a39345d3d83bff86354b28de2", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989294604caa9fbba68fff45774c590824", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98eeb934d504a264a29b3c0683bd7f6054", "name": "super_up_admin", "path": "super_up_admin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983929b2467e9f3d8221e6de1b28355a3f", "name": "apps", "path": "apps", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9836138269fa240622d820064f71afc860", "name": "flutter", "path": "flutter", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980004ef5558b75a6ec6ec1f50ea070dc5", "name": "source2", "path": "source2", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98259fb2addc2adcee28890d0a0b2059f9", "name": "Desktop", "path": "Desktop", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c00a95acff74529d01b6934188d0e096", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98250b8c46b8da3afe235726151a9a40f8", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c51672564b443a1af64f343b673488d0", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985fc40bf0f4264523389ba66e4b543c25", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f857fb7f118ba9acd3e3f3b98893d4f3", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c04a9c0eb8a537934f71cc396d1f0c83", "name": "..", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_local_notifications-18.0.0/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98d736d82ab359369254132a80a6f9faaa", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_local_notifications-18.0.0/ios/flutter_local_notifications.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98beba28aeea04ad447516628dbad48a47", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_local_notifications-18.0.0/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98246b8b5b73c34c2b5f81a85659bdbd40", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e9882548e077393186dab8190bb063d53c9", "path": "flutter_local_notifications.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9817c880866b100ea015e0b09f372af8f4", "path": "flutter_local_notifications-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e980ef770a0bb1d8653401cfc4205287492", "path": "flutter_local_notifications-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d70160a59670601f3cd0b34d8bde3e59", "path": "flutter_local_notifications-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982c98017e02426c017da62e63d10209e0", "path": "flutter_local_notifications-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98372b69a5c4d65bda5ea428d99f7dc20f", "path": "flutter_local_notifications.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98fc4991620c7faceb7b9edb7456ba09f7", "path": "flutter_local_notifications.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e985695da3edb3ba83d7e233cb72dbc93b4", "path": "ResourceBundle-flutter_local_notifications_privacy-flutter_local_notifications-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98eabb1525d1179631374a09de8f16c04c", "name": "Support Files", "path": "../../../../Pods/Target Support Files/flutter_local_notifications", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9864888374463c415074d23616e8361c70", "name": "flutter_local_notifications", "path": "../.symlinks/plugins/flutter_local_notifications/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d6eefad432fee502e4363a1a5785c923", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_native_splash-2.4.2/ios/Classes/FlutterNativeSplashPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e981bf7c5b7f1114419fe26e394acfac6d3", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_native_splash-2.4.2/ios/Classes/FlutterNativeSplashPlugin.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98452a33db6559998e58ecdd60a947d56c", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e9839363b1cc7e08c2c373422d441e89b9f", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_native_splash-2.4.2/ios/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98e77dd989d414131838835a310dda0e05", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a8b68fe8234105b5537ac3ad51c4cfb3", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9842271b079782a5bdc58b706021255dd4", "name": "flutter_native_splash", "path": "flutter_native_splash", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983237ec3f0b2970c3147e76e51251f99b", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98995ba22227cf52a0014e4894cf1bf9ba", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9809729429a3f630ec713525efe7aa9720", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e20386a1e0bb2e19ee1120da88146ced", "name": "super_up_admin", "path": "super_up_admin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98610a94dc9cbf5b1ca7225b31fa33db2c", "name": "apps", "path": "apps", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9897471f2c1ecdf20223c8020fb09e8778", "name": "flutter", "path": "flutter", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985a8c40b5c27d08578c30ac074e46a6b3", "name": "source2", "path": "source2", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987f99faaaf2d1a4fbee658d088b30a518", "name": "Desktop", "path": "Desktop", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982f22b032a33afb92dbb2fbcdb43e515a", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9874bb753269cf933432c8ed901549b431", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98aa4adf66d8d8897a4b2ae2936e6e193d", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ecca674706e832683d52bd02fc29fe26", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f27abb69be655dc2cba5611c1e662b12", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987d05ddb2d4081a1bb017fb2d44c86580", "name": "..", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_native_splash-2.4.2/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e982efaf6a9e4a3bddf7f0a5de38a96e7f6", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_native_splash-2.4.2/ios/flutter_native_splash.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e987af22afc7e6462b042af1d1004227e1b", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_native_splash-2.4.2/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e985cdcd3fdb3650fd21173c263c30f5c72", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e9887345c2fc3bfd2b6d6dc980da145bb88", "path": "flutter_native_splash.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98871bbfc1a97d12adf175942b10593f29", "path": "flutter_native_splash-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e985a07e2e9e9009eab0697159bb9b4da07", "path": "flutter_native_splash-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987bf287def7669aeb917145fba4af75c4", "path": "flutter_native_splash-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d12c25ea11997c738ba6add28dbb3bfa", "path": "flutter_native_splash-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98c6953ee8c181f4bfd28ea4cde9db2d14", "path": "flutter_native_splash.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e986183efed30a01a51cc9b7965fe914354", "path": "flutter_native_splash.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9824d390a7ea6705079d88c35b039b7317", "path": "ResourceBundle-flutter_native_splash_privacy-flutter_native_splash-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98ca2e7e68961176a7c459981d7503cac4", "name": "Support Files", "path": "../../../../Pods/Target Support Files/flutter_native_splash", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e45e3c9004b1e2c1eee6b85a3e43b561", "name": "flutter_native_splash", "path": "../.symlinks/plugins/flutter_native_splash/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98384342f149ec6f32bb67f16ec03e58a2", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/gal-2.3.0/darwin/Classes/GalPlugin.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e982d0e521459581581096082ecc33877db", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9881ef4bd71c476be3f0703387a48ecfb6", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989176c2523d88d6b0e51513f79967ec7b", "name": "gal", "path": "gal", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98235859ae5b31761ffd22f0b0d7bbf0ca", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9893a5c91d97952684e546fb6ab4daef31", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a012637640c49732101c5dc552f60ffa", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98bfc2ba1585ce7ab8c794bf943a7b5bd9", "name": "super_up_admin", "path": "super_up_admin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98278e64d292de4586e9f12b9c5a7055f6", "name": "apps", "path": "apps", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98fd73d9520260dcf175eea3d320ff3302", "name": "flutter", "path": "flutter", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9877fb1c06435833510dc4052f01200a34", "name": "source2", "path": "source2", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9834eb5c0fafd24d3f2310dadea2f78f9e", "name": "Desktop", "path": "Desktop", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983c053936a88cae9daf4e6c23c333ae1c", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9836902f877248506e819ea1a499764d15", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ea1db2fd55b08f624447721c3e1b031a", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98efcc314a2a5262ac9cc5c16f02165406", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982cbe3aaccb42118f738817c9970b4b7c", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989c8a78507ac608cf13a0311e7de6f5f9", "name": "..", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/gal-2.3.0/darwin", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e9814bf89e35bd0be690630430c39305c05", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/gal-2.3.0/darwin/gal.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98204269efc682aa2e72c9998018f29887", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/gal-2.3.0/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98b4329cb81956f53beac77bdf4ea191a1", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e987b385369703d99d4dcb25e044cb4b348", "path": "gal.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e985e1254e35c06f54228ce3343e5907934", "path": "gal-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9849843b93716ae949af4c4f149e4b9d48", "path": "gal-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d7edccd1364062373a9306dd6a05057e", "path": "gal-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9818127877f6c49d236ec17f604f35831e", "path": "gal-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e989933054a5fc031b77d9ea49dda5b15be", "path": "gal.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e985c1f7b806ebab73f28622f8915e70308", "path": "gal.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9824b273a09067f992cbdc5826cc984285", "name": "Support Files", "path": "../../../../Pods/Target Support Files/gal", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984c49b83a0c8ee46dcf861f2bf8b9b553", "name": "gal", "path": "../.symlinks/plugins/gal/darwin", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "file.xib", "guid": "bfdfe7dc352907fc980b868725387e985634884874de029afe6859e93082a6a3", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/google_mobile_ads-5.2.0/ios/Classes/NativeTemplates/GoogleAdsMobileIosNativeTemplates/GADTFullScreenTemplateView.xib", "sourceTree": "<group>", "type": "file"}, {"fileType": "file.xib", "guid": "bfdfe7dc352907fc980b868725387e98e515d1db089893e905ec4787588268a5", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/google_mobile_ads-5.2.0/ios/Classes/NativeTemplates/GoogleAdsMobileIosNativeTemplates/GADTMediumTemplateView.xib", "sourceTree": "<group>", "type": "file"}, {"fileType": "file.xib", "guid": "bfdfe7dc352907fc980b868725387e98176c1c6b3ea1b7ec376bd45aef233ea4", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/google_mobile_ads-5.2.0/ios/Classes/NativeTemplates/GoogleAdsMobileIosNativeTemplates/GADTSmallTemplateView.xib", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e985e5a7ba7c55ad1db7d9ea6975ecd4d7f", "name": "GoogleAdsMobileIosNativeTemplates", "path": "GoogleAdsMobileIosNativeTemplates", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98099aadad2ede59444ccbf612b4d45a6f", "name": "NativeTemplates", "path": "NativeTemplates", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9822753e87fb95b1b4a2345dfef5f50a21", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c5c4c3749343ca90bd57a36f6ae505ea", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d4a9c1f9a509bee77934d90470e47aee", "name": "google_mobile_ads", "path": "google_mobile_ads", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ea09a33283746025d8d9017bcb145d23", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ccdbcc931f3d8fa495b0d0a226a9b030", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9821291efebc1539b8d9fd3aaefba17c78", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98566ca9e79b1905075f30f1823fcceee3", "name": "super_up_admin", "path": "super_up_admin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9869ec2a34d367afcfd592079c32b8e5ad", "name": "apps", "path": "apps", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987b19f59173e8aa99bed3e3bee7e2d7b0", "name": "flutter", "path": "flutter", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98563b1d81499ac335550eb17ab3415935", "name": "source2", "path": "source2", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98715ff949d84e6ed3d439ada761165249", "name": "Desktop", "path": "Desktop", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982ba25e9f6f9a90616bedf43cfc6d2c63", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980b8b95878d9b3bb46426aaace5ccc3e1", "name": "..", "path": ".pub-cache", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980fec26951a25b3bfd07780112e6164a0", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/google_mobile_ads-5.2.0/ios/Classes/FLTAd_Internal.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e986a924c12176f7d971d1369c95f902d1f", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/google_mobile_ads-5.2.0/ios/Classes/FLTAd_Internal.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98dd373ce815ce81a6cb863132990cfe36", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/google_mobile_ads-5.2.0/ios/Classes/FLTAdInstanceManager_Internal.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e988a9078e020e8d4f061ce1bb4feee5418", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/google_mobile_ads-5.2.0/ios/Classes/FLTAdInstanceManager_Internal.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98706bfd0808c3c1133681e8fed491af31", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/google_mobile_ads-5.2.0/ios/Classes/FLTAdUtil.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98edfbd6859175f3469991e3b2cccdd73e", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/google_mobile_ads-5.2.0/ios/Classes/FLTAdUtil.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9849ff3a03935174068c34346c6be2bcaf", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/google_mobile_ads-5.2.0/ios/Classes/FLTAppStateNotifier.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98e19ba0847b6b86a53397268ae6d7c919", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/google_mobile_ads-5.2.0/ios/Classes/FLTAppStateNotifier.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9844047d42c5cf38b01822177172bd5492", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/google_mobile_ads-5.2.0/ios/Classes/FLTConstants.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985c1f99090524695a235a5c452376f425", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/google_mobile_ads-5.2.0/ios/Classes/FLTGoogleMobileAdsCollection_Internal.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9895e957630be221d09c75cbd3ff6bcc43", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/google_mobile_ads-5.2.0/ios/Classes/FLTGoogleMobileAdsCollection_Internal.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988f648a33afa27ac3c942da609da591e8", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/google_mobile_ads-5.2.0/ios/Classes/FLTGoogleMobileAdsPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98bfc5db813fde67b2f153f1f03021ae33", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/google_mobile_ads-5.2.0/ios/Classes/FLTGoogleMobileAdsPlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98dceb3636d4c9972447f003e455acb617", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/google_mobile_ads-5.2.0/ios/Classes/FLTGoogleMobileAdsReaderWriter_Internal.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9876238821a7c14a1d49c919a5fa7d5e64", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/google_mobile_ads-5.2.0/ios/Classes/FLTGoogleMobileAdsReaderWriter_Internal.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98596a562b96e70bff4accd90521a95e55", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/google_mobile_ads-5.2.0/ios/Classes/FLTMediationExtras.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989e329c55f00ba3f7ad9ae27df0daaf24", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/google_mobile_ads-5.2.0/ios/Classes/FLTMediationNetworkExtrasProvider.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f8a5cc4ad462f0a6747e553036b279c1", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/google_mobile_ads-5.2.0/ios/Classes/FLTMobileAds_Internal.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9869029dcd1bb116c96dac1be4fa77f3ce", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/google_mobile_ads-5.2.0/ios/Classes/FLTMobileAds_Internal.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d567935d10fa7c8251b661e659aacc1e", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/google_mobile_ads-5.2.0/ios/Classes/FLTNSString.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98e84096bda89f6b3e0f5dd8c5bc7115d9", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/google_mobile_ads-5.2.0/ios/Classes/FLTNSString.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98494f0ce1537d748551c91110d235d2bd", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/google_mobile_ads-5.2.0/ios/Classes/NativeTemplates/FLTNativeTemplateColor.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98553205fd8ff729d476ac90e7f493c901", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/google_mobile_ads-5.2.0/ios/Classes/NativeTemplates/FLTNativeTemplateColor.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9826d9ac08266887349e4fe3312d2a0e29", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/google_mobile_ads-5.2.0/ios/Classes/NativeTemplates/FLTNativeTemplateFontStyle.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9820dbfa60f184816003cd1a1493ddb391", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/google_mobile_ads-5.2.0/ios/Classes/NativeTemplates/FLTNativeTemplateFontStyle.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980cb1add949c52b556df2b9c6560f1312", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/google_mobile_ads-5.2.0/ios/Classes/NativeTemplates/FLTNativeTemplateStyle.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9800a5661db0826febf660b9dbcde77cb2", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/google_mobile_ads-5.2.0/ios/Classes/NativeTemplates/FLTNativeTemplateStyle.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98073de3d63ac17ea5eb99a561c56bbbbb", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/google_mobile_ads-5.2.0/ios/Classes/NativeTemplates/FLTNativeTemplateTextStyle.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98609d8dbff302e5b5f85358a0a1794c23", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/google_mobile_ads-5.2.0/ios/Classes/NativeTemplates/FLTNativeTemplateTextStyle.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988404dd74d97ad6da597ca31e297a160c", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/google_mobile_ads-5.2.0/ios/Classes/NativeTemplates/FLTNativeTemplateType.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98748d6ab6ca48f69b971a9db398dce289", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/google_mobile_ads-5.2.0/ios/Classes/NativeTemplates/FLTNativeTemplateType.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98cea9725aaeb18b99e29a244c91856a5c", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/google_mobile_ads-5.2.0/ios/Classes/NativeTemplates/GoogleAdsMobileIosNativeTemplates/GADTFullScreenTemplateView.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98d17e8fe6283c2a1a9ac019719aee775a", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/google_mobile_ads-5.2.0/ios/Classes/NativeTemplates/GoogleAdsMobileIosNativeTemplates/GADTFullScreenTemplateView.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98427e9cb0fafc676e031e5f8b48412992", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/google_mobile_ads-5.2.0/ios/Classes/NativeTemplates/GoogleAdsMobileIosNativeTemplates/GADTMediumTemplateView.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9801653c016a2649363c52b4946b4bd5fe", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/google_mobile_ads-5.2.0/ios/Classes/NativeTemplates/GoogleAdsMobileIosNativeTemplates/GADTMediumTemplateView.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f75b541127cad9172ba980c5f10511b9", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/google_mobile_ads-5.2.0/ios/Classes/NativeTemplates/GoogleAdsMobileIosNativeTemplates/GADTSmallTemplateView.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9856df5697222ff418c400a958052eaf5e", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/google_mobile_ads-5.2.0/ios/Classes/NativeTemplates/GoogleAdsMobileIosNativeTemplates/GADTSmallTemplateView.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987d83aa384cb21ac1679af262319405af", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/google_mobile_ads-5.2.0/ios/Classes/NativeTemplates/GoogleAdsMobileIosNativeTemplates/GADTTemplateView.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98c21adc59eab3ccf11bb7f1e11353b7d3", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/google_mobile_ads-5.2.0/ios/Classes/NativeTemplates/GoogleAdsMobileIosNativeTemplates/GADTTemplateView.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98ab276434f242f47d3661064f65622775", "name": "GoogleAdsMobileIosNativeTemplates", "path": "GoogleAdsMobileIosNativeTemplates", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98eccf2b9921adf1f1bf41387b42d6d64b", "name": "NativeTemplates", "path": "NativeTemplates", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985892c665d73d1e82368ed6fd21cdccc6", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/google_mobile_ads-5.2.0/ios/Classes/UserMessagingPlatform/FLTUserMessagingPlatformManager.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98eb45f48b06bea781f26cc723bebb0130", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/google_mobile_ads-5.2.0/ios/Classes/UserMessagingPlatform/FLTUserMessagingPlatformManager.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988ff4ba0a1c606231d04946e9a3d896fa", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/google_mobile_ads-5.2.0/ios/Classes/UserMessagingPlatform/FLTUserMessagingPlatformReaderWriter.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e982a65f8cc0d0d11cde98e9e74e8e1ab77", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/google_mobile_ads-5.2.0/ios/Classes/UserMessagingPlatform/FLTUserMessagingPlatformReaderWriter.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e986eb16a89caa959385c15bf0c2b460d92", "name": "UserMessagingPlatform", "path": "UserMessagingPlatform", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d151817ad820a5c5763b761a7e8b6bcf", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d108d76a9bf0f939c799fdbfc6618c0c", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98fc30589a67ff9a67c803d6bf58b8c2ae", "name": "google_mobile_ads", "path": "google_mobile_ads", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a8b3876537a95ad4a0a92de48b2ae6dd", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b757feace4fe47f45d16e9e3f5018d81", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9882139d93821bc640cc0099279b8b7407", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987b285ae03e4ce12468e31e82c0a99853", "name": "super_up_admin", "path": "super_up_admin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9838d2bb06e27124aa7008c35cd810eb41", "name": "apps", "path": "apps", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9846e70f1364e7384d79a0389b5058d7a0", "name": "flutter", "path": "flutter", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9815101b6a89b72b90f2e06836cf175305", "name": "source2", "path": "source2", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98221204a9d4825c5daa3c6def8723d6e5", "name": "Desktop", "path": "Desktop", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f901fa8cd8787428bcd0eb65070e7cdc", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983a3850bb98c86cdf59da7426529e10f6", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98381c372819f950de629289b60e4d4a77", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f5cb9f616db362ab14113f99b266132e", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98437e1ad60b1c294fa18bccaeae000242", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98bd1aacbe5e14e670a1b6f369d02b8a99", "name": "..", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/google_mobile_ads-5.2.0/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98bf46142932c43885b4c9dbc7be8083d9", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/google_mobile_ads-5.2.0/ios/google_mobile_ads.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e9885adf3b29b4bbfa838f74dd570ad320f", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/google_mobile_ads-5.2.0/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9880a79612611d92725182c4390f34dd3b", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98297bcd2f677e01833a2651b76aaf5c30", "path": "google_mobile_ads.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9877becc4b6790e76fe61f446bbd2cd528", "path": "google_mobile_ads-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9875248a6fff6b9212ce60c1ff61209903", "path": "google_mobile_ads-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a2820c35023b49f492380e4b7de091bb", "path": "google_mobile_ads-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9852656473d813e658670a4762f44047c7", "path": "google_mobile_ads-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e981965328fe7a3e1e6b780133706892962", "path": "google_mobile_ads.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98ee23e5879c24e4285fa8c1db5ab08d58", "path": "google_mobile_ads.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9897e129e0e7382abd5847c8bbb0a31580", "path": "ResourceBundle-google_mobile_ads-google_mobile_ads-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e988921d708e709257bfc8ea032f27c67b8", "name": "Support Files", "path": "../../../../Pods/Target Support Files/google_mobile_ads", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98cf0e76afd4063bf4b4bb24136947f8b9", "name": "google_mobile_ads", "path": "../.symlinks/plugins/google_mobile_ads/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981ce09a174aa3197cf45066164bc33738", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_cropper-8.0.2/ios/Classes/FLTImageCropperPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9888f67812da3191630a497c090092f787", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_cropper-8.0.2/ios/Classes/FLTImageCropperPlugin.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e980014d718354693d98961335ab69512ea", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c47307775d2158cc13bbad130396b6b2", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989a32eb8b233c27a2091e9274d8fd98a9", "name": "image_cropper", "path": "image_cropper", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983bca45cc41573373a00709ab4f106f08", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c73b4ef8aa8c77a8be24e97f5d6a90bd", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9868c2117ea13a7581a3c041def6d2b5a8", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f8a365345d39dc031c7d62570120b6de", "name": "super_up_admin", "path": "super_up_admin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a80f9e9721ce8be7e6085af527cd33f5", "name": "apps", "path": "apps", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9811613e8772c1d09d3f9aa77a5b91d701", "name": "flutter", "path": "flutter", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987d87415cdceb601b164bee42d4f493fa", "name": "source2", "path": "source2", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980754444308800f9a94c2eff9bbe302b1", "name": "Desktop", "path": "Desktop", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c6e2b0e8d932e4e365cbcca15f58bc8b", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9828469cd4e1210451919c083a8978cc0b", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9877ec55e95a78d93bf0633833c7a0a67f", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f17c7b83c0ba9dacc8fa6f37a3d409b2", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988da3292beac7f2cdf6f42eafa431435c", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980f944f1b2048325b28495312ef4e4b47", "name": "..", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/image_cropper-8.0.2/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e985290a4c5528fd7d71b6d85221ec5e55e", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/image_cropper-8.0.2/ios/image_cropper.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98958317bc1e3472db19941810402125cb", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/image_cropper-8.0.2/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e984620b9b4d5a2fef89cdd67b233cb8eb6", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e984e2c9f27333b76f43315482afa5a3995", "path": "image_cropper.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e986f17bebca4811dafb049166c64170460", "path": "image_cropper-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98848d1893247bab9e8b2d3c0e5ab19223", "path": "image_cropper-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b9b98cbbabb1c987c7a776f13b809dec", "path": "image_cropper-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a0256d549f02e0f77def65116ae1a8d9", "path": "image_cropper-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e989e4bd4463fa8c6ba81939db73bc91dda", "path": "image_cropper.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98461d04bac1d0e9528ee5b1a4cd551b24", "path": "image_cropper.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98e8f3961c3500ccb5aa12d2b0128da264", "name": "Support Files", "path": "../../../../Pods/Target Support Files/image_cropper", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982c96395550c65d13b64acdf35d3eb1a1", "name": "image_cropper", "path": "../.symlinks/plugins/image_cropper/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e9808eac876ab1a23b945cae95e92638995", "path": "../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+1/ios/image_picker_ios/Sources/image_picker_ios/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98e611ae5912f6dcbeaf9070e2d1c3d468", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b7461f15bf92e057754ac69eae96d4d4", "name": "image_picker_ios", "path": "image_picker_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98169ded1100073f769d7f842e8ae566f7", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985060e727fcb03970bd7c97bc4d5ea93a", "name": "image_picker_ios", "path": "image_picker_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988f8553cb088b113fd3b5bfe8cc1b28e1", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b17eed11e50a37968dadf567ad0f0afa", "name": "image_picker_ios", "path": "image_picker_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981e7774f5e2a16994ad8d8d617863b4fe", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98545bbfd62d694556c16ea01ca57451b3", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98494836f5459ea1fc464bba27edaf7389", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9875d64ec60196f42ba7bec6a326b32820", "name": "super_up_admin", "path": "super_up_admin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984e801b7b81ebe5ffedbffbdf4141e027", "name": "apps", "path": "apps", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d7c735d5fc955cdbd8410a92612c0904", "name": "flutter", "path": "flutter", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b5b6118b65fa39929b3244ab17093c97", "name": "source2", "path": "source2", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989a85977692093ddaaea641da4a698239", "name": "Desktop", "path": "Desktop", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985824a50880763af0b1f33446227d79b6", "name": "..", "path": ".", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98883bb72095a15e75868f0dd813ddf732", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+1/ios/image_picker_ios/Sources/image_picker_ios/FLTImagePickerImageUtil.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e987fc015f6a0643f872b07bba16306264c", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+1/ios/image_picker_ios/Sources/image_picker_ios/FLTImagePickerMetaDataUtil.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98bcd55e0f996ac78bb2436034f2f12f84", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+1/ios/image_picker_ios/Sources/image_picker_ios/FLTImagePickerPhotoAssetUtil.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b1e235ab4de7ed02d2b94d2b7f743872", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+1/ios/image_picker_ios/Sources/image_picker_ios/FLTImagePickerPlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e985a98f151522803a3121ac6170e209076", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+1/ios/image_picker_ios/Sources/image_picker_ios/FLTPHPickerSaveImageToPathOperation.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98fae945697b8cfc2cf121026049e11900", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+1/ios/image_picker_ios/Sources/image_picker_ios/messages.g.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981bb4fd4e93764d3714b25a75c9942628", "path": "../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+1/ios/image_picker_ios/Sources/image_picker_ios/include/image_picker_ios-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9887c2d83173a6d80a43bb8089946f2bd8", "path": "../../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+1/ios/image_picker_ios/Sources/image_picker_ios/include/image_picker_ios/FLTImagePickerImageUtil.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986ca7e8c4bb696f0c7b2d597d2c021d00", "path": "../../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+1/ios/image_picker_ios/Sources/image_picker_ios/include/image_picker_ios/FLTImagePickerMetaDataUtil.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984d3b0c4cd2954d19a7d5812064ca5342", "path": "../../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+1/ios/image_picker_ios/Sources/image_picker_ios/include/image_picker_ios/FLTImagePickerPhotoAssetUtil.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988f6cbbe4e93d641fa08bcbce7d42bb24", "path": "../../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+1/ios/image_picker_ios/Sources/image_picker_ios/include/image_picker_ios/FLTImagePickerPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f25d2453e1de1eecf318caaa80cd90c7", "path": "../../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+1/ios/image_picker_ios/Sources/image_picker_ios/include/image_picker_ios/FLTImagePickerPlugin_Test.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f0a68076822f7c3dc3101571160117df", "path": "../../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+1/ios/image_picker_ios/Sources/image_picker_ios/include/image_picker_ios/FLTPHPickerSaveImageToPathOperation.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98954adcb2a201d19d4ef386ea80494b09", "path": "../../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+1/ios/image_picker_ios/Sources/image_picker_ios/include/image_picker_ios/messages.g.h", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98f1cac4a387ddeff9a8b4ee6a3660d772", "name": "image_picker_ios", "path": "image_picker_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ff2d594581e0fcecc6828111d7cf78ba", "name": "include", "path": "include", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98bc2e129ff149828e308d47bc1ece6f2c", "name": "image_picker_ios", "path": "image_picker_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ee61edaaecd12757eef7ab18afe321c0", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984294a78dd6414d6f63f8ff820732370d", "name": "image_picker_ios", "path": "image_picker_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98383347b477f4eeb99d6636a1e95f7102", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986b934833d79f1b82c66bce852bb041d9", "name": "image_picker_ios", "path": "image_picker_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9866d0775c8b52a2a19f2918a1ef832ea8", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98670c776ad00194c6fb152b391a9517ca", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e5bc4d991961888cc07107cb563cda63", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988966bcd5459c94163284bb5aa99cba7b", "name": "super_up_admin", "path": "super_up_admin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98230b58b804901ce9e26dab417d15d89b", "name": "apps", "path": "apps", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98214679ab3871413fe3ac1d11ea7580fd", "name": "flutter", "path": "flutter", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b3c3b9c556e504a38aae4be1c07a048c", "name": "source2", "path": "source2", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983857cbcfc0a4228b221366adb990cf4d", "name": "Desktop", "path": "Desktop", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ceb3f30136f6542fe5a819f3ddcf4158", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a760f2d2c090b346ab82a926b276d6e6", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989ecee3487156dc2fe75229b0525b4b7c", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ab8afc0b512c1a6fb91d05517af0bbd9", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c3105463c4c35578aec4dde57babcf09", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a0f43266a92a869754612e876a4d049c", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982d6a8bbf40b59dfdda6ebd38aa9456d8", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986656dcf98ed74638afe1ee5f908ff087", "name": "..", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+1/ios/image_picker_ios/Sources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98d2e6afe4f341eb7f90f78ae4a0e931e4", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+1/ios/image_picker_ios.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98e4a72b9bc2056e424fdaa9473a6ab75e", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+1/ios/image_picker_ios/Sources/image_picker_ios/include/ImagePickerPlugin.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e985d62530caa524e6f7ef1a44ad3462cbf", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+1/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98f7a86b1b11f4a6b9b4ad4f73f1230afc", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98b4582a953ac97e294f114e932c125837", "path": "image_picker_ios.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98067db1a3e6f1f61a3bc0cfe902791776", "path": "image_picker_ios-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98179b6b0fb68f5b794b777a62ada2b52b", "path": "image_picker_ios-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98dc475a301fa0ad31614385afc5698393", "path": "image_picker_ios-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9883b840464a0e7f4ea46052902b7704b7", "path": "image_picker_ios.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e981739f1c93d6489e62a51e06e1b8037f2", "path": "image_picker_ios.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9842cee23bd03d015f53691441ea8b5348", "path": "ResourceBundle-image_picker_ios_privacy-image_picker_ios-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98e0d1cf9848174b6cd44ebab80a16da53", "name": "Support Files", "path": "../../../../Pods/Target Support Files/image_picker_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98765a6065ab5c8cfa91c35171507b58cb", "name": "image_picker_ios", "path": "../.symlinks/plugins/image_picker_ios/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98e5282cce46a1de9b2b458f158865eb29", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/just_audio-0.10.4/darwin/just_audio/Sources/just_audio/AudioPlayer.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98eae962982d838a63e95340febed0bffb", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/just_audio-0.10.4/darwin/just_audio/Sources/just_audio/AudioSource.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e982a82407f287565074a9c125131c4f75c", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/just_audio-0.10.4/darwin/just_audio/Sources/just_audio/BetterEventChannel.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98e02dfcfe7ee38d67e96d597642a2e832", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/just_audio-0.10.4/darwin/just_audio/Sources/just_audio/ClippingAudioSource.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f60197eb507a4fd1055a051bcd24a4a0", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/just_audio-0.10.4/darwin/just_audio/Sources/just_audio/ConcatenatingAudioSource.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9818ca7322faa48de9aed159f5d5931178", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/just_audio-0.10.4/darwin/just_audio/Sources/just_audio/IndexedAudioSource.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f89afe2f4c129bafcb2ae217b6c9ffe1", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/just_audio-0.10.4/darwin/just_audio/Sources/just_audio/IndexedPlayerItem.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98dbaf711fe7dc12ba493945cd1ada3d27", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/just_audio-0.10.4/darwin/just_audio/Sources/just_audio/JustAudioPlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9850366249360cab9c2d1bc6ce50678312", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/just_audio-0.10.4/darwin/just_audio/Sources/just_audio/LoadControl.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e981353cac1821bb12f9e7c23961dafeb07", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/just_audio-0.10.4/darwin/just_audio/Sources/just_audio/LoopingAudioSource.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9803905b066bd0cd0eb7b7a2c21e648b94", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/just_audio-0.10.4/darwin/just_audio/Sources/just_audio/UriAudioSource.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f68c5fdfa6a514d5f73f425c0dcaef17", "path": "../../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/just_audio-0.10.4/darwin/just_audio/Sources/just_audio/include/just_audio/AudioPlayer.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9893c79e521584df042bdd543d509da2e3", "path": "../../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/just_audio-0.10.4/darwin/just_audio/Sources/just_audio/include/just_audio/AudioSource.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983accfc180553b4915c165587643d93e5", "path": "../../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/just_audio-0.10.4/darwin/just_audio/Sources/just_audio/include/just_audio/BetterEventChannel.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ecbbbbd845a355188aece2078b926841", "path": "../../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/just_audio-0.10.4/darwin/just_audio/Sources/just_audio/include/just_audio/ClippingAudioSource.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f62b1559b6bd821c442834d495846439", "path": "../../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/just_audio-0.10.4/darwin/just_audio/Sources/just_audio/include/just_audio/ConcatenatingAudioSource.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98517be8c45a035e4bb96619d383e9675a", "path": "../../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/just_audio-0.10.4/darwin/just_audio/Sources/just_audio/include/just_audio/IndexedAudioSource.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982713e1cbf819e3637ea98f0995262783", "path": "../../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/just_audio-0.10.4/darwin/just_audio/Sources/just_audio/include/just_audio/IndexedPlayerItem.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98eb954d60f1b09dc02ac6a7a4737b93c2", "path": "../../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/just_audio-0.10.4/darwin/just_audio/Sources/just_audio/include/just_audio/JustAudioPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d52977f60ce27c54dac72362487fdae0", "path": "../../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/just_audio-0.10.4/darwin/just_audio/Sources/just_audio/include/just_audio/LoadControl.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f0af8b8e55483791169f14e5bb821d1b", "path": "../../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/just_audio-0.10.4/darwin/just_audio/Sources/just_audio/include/just_audio/LoopingAudioSource.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988860ca5701d838334e32fc6dde6e56b6", "path": "../../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/just_audio-0.10.4/darwin/just_audio/Sources/just_audio/include/just_audio/UriAudioSource.h", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98918f27f48213faf63feaac346cb4e313", "name": "just_audio", "path": "just_audio", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983759e47221935ac03a184ff06e3d76b0", "name": "include", "path": "include", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9888d734043c9d9e430ceda29c9bae342e", "name": "just_audio", "path": "just_audio", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9829c869b689427530ccce2300b841221e", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d2cfc3c31492e6af6c972d8196f5a27d", "name": "just_audio", "path": "just_audio", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e3c69e4ead66ae32967e990e9e1fbd9a", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985da3c04d82991f595e3482688f3c5c58", "name": "just_audio", "path": "just_audio", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9851ceaf7e14f8d6d170e0c41617f71a83", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98990198fb0f6fbcc36bcd64854b185f56", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9865d17fca72fe9cc41e3bdd6fd4894192", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98fbc3a5de1ba5e88c0326c493172696c4", "name": "super_up_admin", "path": "super_up_admin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98db1d8443f1edffbeb94a8b712f7f90dc", "name": "apps", "path": "apps", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98849a9138029fb7c2f0e5029e3d71bb75", "name": "flutter", "path": "flutter", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989cbe6233b8aad3bb862490b96978e97d", "name": "source2", "path": "source2", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b6acd61c427362efc41d59199e5b794f", "name": "Desktop", "path": "Desktop", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d032111f2e94e20580a7edda0fe0ed30", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987827844280e31f3fc6234756905cbd9a", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981b8293477fd29f7376105ac3250206a3", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98bffff696c721eeeaa7b1df16372b200b", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98158eb9a349f472b037184f50306d86d4", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98cf95b36ae1c0eb6b5badb7b1b6fb3373", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ecf7aab88aaec1118db99116827d663f", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980ca0cfaaf6f0618fd30fbf427fdbfc03", "name": "..", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/just_audio-0.10.4/darwin/just_audio/Sources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98133a89e58babda440687d822075096f3", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/just_audio-0.10.4/darwin/just_audio.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98342211bf281fdc50ac3dc006c122dcf1", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/just_audio-0.10.4/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e982d8a9076a36a66585eddc562412d173f", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98dd9180192cfdba81e5fd82b5daf78415", "path": "just_audio.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f874fd1eb624d681910ca4b801ce3987", "path": "just_audio-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98d1737a16afa4e7ff07b5ff439fb75022", "path": "just_audio-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d779c45ce32ab89517ccd4df083de227", "path": "just_audio-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98bf6c6e152de6913ed87ae46c82c99292", "path": "just_audio-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9800e11b606a421fe340afd27cd552c33b", "path": "just_audio.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9845959a4e7823b4e91a5e15c06999deef", "path": "just_audio.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98406a66b863ae4e3cb77f6aeed03490ff", "name": "Support Files", "path": "../../../../Pods/Target Support Files/just_audio", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989476579eac15b1c10a8921b590ad07a9", "name": "just_audio", "path": "../.symlinks/plugins/just_audio/darwin", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ddbf7f3c17cd9d95eca2535cb8bd3db0", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/map_launcher-3.5.0/ios/Classes/MapLauncherPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98d37671b7f4c5b77ac27353bef040a139", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/map_launcher-3.5.0/ios/Classes/MapLauncherPlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98a42610844665af17aeb8274d9dd1c891", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/map_launcher-3.5.0/ios/Classes/SwiftMapLauncherPlugin.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9888220f8a882d3d07539f20e4bfb3e39f", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e980bf0e9f5d8f6cc941663385abdc0c0ee", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/map_launcher-3.5.0/ios/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98767cfcc60862ff9a16e45c57996c5c62", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984094188100f4c42d3d19cd9bdd0d2733", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a71862a4be3c73aed3b133c522a088ce", "name": "map_launcher", "path": "map_launcher", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d234eb5ff0113e51174fa7388a04c9c4", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98300df0b3dc8fb7a258efeb5e5f402b0b", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9866391b3cef831b667e481b31f8717a32", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982f7fcd07a69f22661de8bf2c15f9ae0c", "name": "super_up_admin", "path": "super_up_admin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d6e708accf93dcf647b83038a445a229", "name": "apps", "path": "apps", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9897bf2d0cb77244f380857484c60c34ed", "name": "flutter", "path": "flutter", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9896108648453fe5c6520bee4672f533b0", "name": "source2", "path": "source2", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98913d8e2fbd53bb50d471db3ef1c94cf2", "name": "Desktop", "path": "Desktop", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985398e10abb6ac5b0f77177b967d68303", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983b3f4820b8e285d34ce7ca0e8f86ac57", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9809a7edf0f1b16df2c9acaa1e24f31af2", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9843ee78159f27eaee6f8ff64106ac3bd0", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9816a27876625a9804eb82f5d1e1868c37", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98159a9b224d7156f4ed73073dca874fb2", "name": "..", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/map_launcher-3.5.0/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e9821f7929e55f2a82b1acc0371882a6e96", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/map_launcher-3.5.0/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98dda2ce4d9936309490b9249c48f7ff8a", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/map_launcher-3.5.0/ios/map_launcher.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98a21b3521bde08630bd5f47b836cb419d", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e984819e49bba0aa1c8b15149c4a4640cdb", "path": "map_launcher.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e988e75fdeeefa66fbe9f4ce7012f5107f2", "path": "map_launcher-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9801a206c23b2f28f897b7d88d298703f1", "path": "map_launcher-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98afe00900a198a683e4bd6c95b79ef19a", "path": "map_launcher-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982a090f21daf5dd83f14a72d3cbc29b95", "path": "map_launcher-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e980e3a09f738a32c36f7106f82d016f435", "path": "map_launcher.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e981c095581d9e3842c885d3bea41a8cd8f", "path": "map_launcher.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98ad5ce8ae878b54eca69fe9ebbe16261a", "path": "ResourceBundle-map_launcher_privacy-map_launcher-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98c359309b5fbc36005460c17666fa2c08", "name": "Support Files", "path": "../../../../Pods/Target Support Files/map_launcher", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98dda31b315565d474e3917b80ab59d7f0", "name": "map_launcher", "path": "../.symlinks/plugins/map_launcher/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b585126439b3ff010ed25ae7f30e982b", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/package_info_plus-8.1.0/ios/package_info_plus/Sources/package_info_plus/FPPPackageInfoPlusPlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98c6d692539682b5266dc48b51413b93df", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/package_info_plus-8.1.0/ios/package_info_plus/Sources/package_info_plus/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}, {"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9803ea184d2fcfde26555ba49c2305dd37", "path": "../../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/package_info_plus-8.1.0/ios/package_info_plus/Sources/package_info_plus/include/package_info_plus/FPPPackageInfoPlusPlugin.h", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98191dd52f559b9ca0e31719df6eab19a0", "name": "package_info_plus", "path": "package_info_plus", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985cfb01b83bc025d01bec1b9f8463dfee", "name": "include", "path": "include", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c145ed8886d498b3eb85d85a3ae37aa2", "name": "package_info_plus", "path": "package_info_plus", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c3c81830c21ed6592c44686a5f1278de", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d0280842995ad1fafc797155ada700b5", "name": "package_info_plus", "path": "package_info_plus", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9804922d254810731830a34302ca9047bc", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9847655b1c80262867003cd0109274e3ec", "name": "package_info_plus", "path": "package_info_plus", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f97207ad521a232db3faf67c053667b7", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980fdb41a21ac7655e4aec9850f4d064ad", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c2b5981cc90761ba70fc82ada3073b03", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981f6f83f86f51f1117ee4f5c82715985e", "name": "super_up_admin", "path": "super_up_admin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f3f5e7d46cab39562284736ccb7bb5c8", "name": "apps", "path": "apps", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986bd13b07e03a87e24c44725fe654b2b1", "name": "flutter", "path": "flutter", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d33b1d7803bddb08226afbd76d89c273", "name": "source2", "path": "source2", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98618d7560c2168955f3bf3644b5cf98d6", "name": "Desktop", "path": "Desktop", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9890fe847269014f26cea8d04ae62f1844", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981c2228d4bffa093e48f1a1e98c272c73", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98bafe360465c5b4a83e2b1af4fb9c88c9", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a304d570cba3c2ade1aba942e3aa2cbb", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980c880a347cb658969839e2f206ce048d", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c97b0e6510e4add3eb414fe6267e7ca9", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9899c299b360e15c3b7d00317a59b7d459", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983907bc66a4ae468181d61d26972a35a4", "name": "..", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/package_info_plus-8.1.0/ios/package_info_plus/Sources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98e20d503fca6ea3ed78b326c1e8161ba2", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/package_info_plus-8.1.0/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e989e977bd85319ec7e03f8cae8f4ed6e9d", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/package_info_plus-8.1.0/ios/package_info_plus.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e986cb38d7f0be6d7b61a9ec943da9694bb", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e984d5e5b62955a106b7997108ff23894e4", "path": "package_info_plus.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98646a10c6b8f94e8d557521ab1c931f87", "path": "package_info_plus-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e986a5fb001e453198dacb98a7811fb1c19", "path": "package_info_plus-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98cd2525579c1056bfba66d4c7eba3ff46", "path": "package_info_plus-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986483fdabddf58be18dd2865ce306f769", "path": "package_info_plus-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9864e3c9cf8e0c0f94353a5e01251b9fcc", "path": "package_info_plus.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9865f31421fc16ff205bd6526e2aa3422c", "path": "package_info_plus.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e987d04b2e5199433b8a7477753913fab29", "path": "ResourceBundle-package_info_plus_privacy-package_info_plus-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98642caef33b4a53763f5da971397e901e", "name": "Support Files", "path": "../../../../Pods/Target Support Files/package_info_plus", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98fbe491d174832c54baa5f3ead0e14f80", "name": "package_info_plus", "path": "../.symlinks/plugins/package_info_plus/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e9830e27537f10b1aa2bc1b9f28e413ca98", "path": "../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.0/darwin/path_provider_foundation/Sources/path_provider_foundation/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e986c1c9fc18a95dc4499ee6fb0dc5014f7", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983c3f28282538abd7b2b11d51e5ca63b5", "name": "path_provider_foundation", "path": "path_provider_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9807cea5ca90eb88afbcd0e2a525e160a7", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988d70ebd5fa9a72e554b3cc389a815912", "name": "path_provider_foundation", "path": "path_provider_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ffb5bc3066a90ea3ce509847860a73c5", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987bcb90bfdc8b457a7c770204fa16e65e", "name": "path_provider_foundation", "path": "path_provider_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98dc8ab6144d97a570bf8fc67065954a26", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981651b127d87d1ac81be0ecb010c11734", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c1eaf55c37854d82bf8300a5361d9a9e", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989820073b578979cd17ea6a0252bebeba", "name": "super_up_admin", "path": "super_up_admin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b907d19b69fa9577d2dd10fc24ba8508", "name": "apps", "path": "apps", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98359e4a59526900820079aa1f28e28465", "name": "flutter", "path": "flutter", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98effac53362774cb8d31fee259703172f", "name": "source2", "path": "source2", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c1a19b43b738da7b9ea3f3f2431705a5", "name": "Desktop", "path": "Desktop", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f9d7abe2a27a649a143762bec89f927d", "name": "..", "path": ".", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98887ebfebc844954d5a646e49c499f0ce", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.0/darwin/path_provider_foundation/Sources/path_provider_foundation/messages.g.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98bed6f1c1481af3c8f0df8ae0034659b1", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.0/darwin/path_provider_foundation/Sources/path_provider_foundation/PathProviderPlugin.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98ddc9673cf970e76cae1e3db802a5bada", "name": "path_provider_foundation", "path": "path_provider_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e65b29e47e827b67ab9b2af206448de2", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98dba147ecb2f79b181dbd2344b24364eb", "name": "path_provider_foundation", "path": "path_provider_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984dfbafe692c0fcfd71aff502a998fb61", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a10bf5d0921ec34a011ae075a2499da2", "name": "path_provider_foundation", "path": "path_provider_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98aa2cfb560f8eb5971117702f1d20b6e3", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98334915e305cd0e5a01869e638ba45d00", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98113c6a953767793f412c05bbf3b39411", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983dc22fc91fe332022cfce0144b5d1352", "name": "super_up_admin", "path": "super_up_admin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986c968adbd3c4a9e1fe66d737ade2719d", "name": "apps", "path": "apps", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984ddfa0f9fcbfcd38df1d3c65cee7dad7", "name": "flutter", "path": "flutter", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98237897358f62198c135a805b110e4882", "name": "source2", "path": "source2", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9880e43326530b67315c5482e463d653b5", "name": "Desktop", "path": "Desktop", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98fa2e240ab3e3ce8b19d6a7f0728cb300", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d0d500ff90d67d4b42d304b5f750e877", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98fa1668a048887ce9adcf3d95ec343676", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982bc1cb64024b1ceb4e0ea0116898529c", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987464db420aeca44eee6fd3b7a5e7fa99", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98823518fc9e2e8e92ee0cfdef4ef58021", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e6bf2dbb27bffa872df8966cf563f748", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983087024800a8059dc7aa0541f07cf9c1", "name": "..", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.0/darwin/path_provider_foundation/Sources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98fdc85909a435b73384c8d1963e3e59b6", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.0/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98b102d457f6f09d426c36b3775ba9c7db", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.0/darwin/path_provider_foundation.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98519f9f7e6625b8f2312826dc1847dbdb", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e986af4fc013a609103b3aaa56891f715f9", "path": "path_provider_foundation.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98607a75d485db0673985f6d2e63931ef7", "path": "path_provider_foundation-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e988ff79893a059ca1950fce901f44892dc", "path": "path_provider_foundation-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a99191c7233473acfa45274dbc58fc0e", "path": "path_provider_foundation-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98fc8388751627135d5f74ac73575ded1a", "path": "path_provider_foundation-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98d2db78532802c7ae27f661a37cc44ca4", "path": "path_provider_foundation.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e983b32de299231d846dbdd4425114111e4", "path": "path_provider_foundation.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98ba9e3e45bee80aa17497a65caee10dea", "path": "ResourceBundle-path_provider_foundation_privacy-path_provider_foundation-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98a6953cc5b60b14fa79623486b8c4804d", "name": "Support Files", "path": "../../../../Pods/Target Support Files/path_provider_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f7ba82fb3b2a129d4b7faca942a8f7f6", "name": "path_provider_foundation", "path": "../.symlinks/plugins/path_provider_foundation/darwin", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981b994616f102b96638d7b5761b9378db", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Classes/PermissionHandlerEnums.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9889ace77738a9ddeafe36016c3207cf1d", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Classes/PermissionHandlerPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9835d07a90d27b2ad99ed709c237f05a4c", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Classes/PermissionHandlerPlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982ed52fc05c3c3701e88638391c09d47b", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Classes/PermissionManager.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b41d6be2a236902c2c9c07f6cb8760e9", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Classes/PermissionManager.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98fea5bba478d9257d548e15dde83abf3c", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Classes/strategies/AppTrackingTransparencyPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e984451b500332c8ab38fc9c8251bc83145", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Classes/strategies/AppTrackingTransparencyPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9859507a63dbb4cee149548fadfa85a0b9", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Classes/strategies/AssistantPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e980eb254a89bdd529a277ad57c0a8d1773", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Classes/strategies/AssistantPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98bf845ceaef672618f1dcc892f1baee00", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Classes/strategies/AudioVideoPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9894b7328dfe3bfd7b4cbf850ca967c08f", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Classes/strategies/AudioVideoPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98eccf103729e694154a9d647e269b9a78", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Classes/strategies/BackgroundRefreshStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b57896ec6c796409ade96943ddfb6afd", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Classes/strategies/BackgroundRefreshStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9844399f359d75181aaca443641033e5cc", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Classes/strategies/BluetoothPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e984ec35de7db19782b3bdf0c0f9130708f", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Classes/strategies/BluetoothPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b0e0509ded3295987cc0053016f1e681", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Classes/strategies/ContactPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98446c7780dc464ba35edac55e19ec7fe3", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Classes/strategies/ContactPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983a51b2e86a23a7cd6d11313acc126444", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Classes/strategies/CriticalAlertsPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e987611904ef5114e36713e5021cb5e9fc1", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Classes/strategies/CriticalAlertsPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d2f30ec24f02d7da99f7e42acf00abdd", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Classes/strategies/EventPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98dba8aabbf16a9982e4100cfcb763fe61", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Classes/strategies/EventPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9848d90addee7a13e042579efe95deb0d8", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Classes/strategies/LocationPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98c3284421d76385ee44c722265cef1ee6", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Classes/strategies/LocationPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985d854b902156950a112ebc5f5283f6c0", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Classes/strategies/MediaLibraryPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e981ff90846986ee0a8fe68de692399d566", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Classes/strategies/MediaLibraryPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983ad49a3c15a2e6574cc5996cc59f8e5c", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Classes/strategies/NotificationPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98613dfd933694f265f12fdbde71d919f3", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Classes/strategies/NotificationPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98dd1faaf8baebe8ba32e6e1c43c91a30a", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Classes/strategies/PermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f8b0380333421c3b949a2c31a2966840", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Classes/strategies/PhonePermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e989b19269a3143bb4e28dde3f5d7e90ed7", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Classes/strategies/PhonePermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987514c8d723991f5ca67a500f6f59758b", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Classes/strategies/PhotoPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b9e433390dc4b0d5bda2b0b14e3efe08", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Classes/strategies/PhotoPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9824a2244448858af9f86cf77580b1f6f0", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Classes/strategies/SensorPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e980c4aea538b6a4c132c03e1b060fdfb63", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Classes/strategies/SensorPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c040d4f618178184a41a2d9976cc6e01", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Classes/strategies/SpeechPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98e9fc26698967f0170bb22a6976c3ee2a", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Classes/strategies/SpeechPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98247ee767bac0ef8c15de655964d540a3", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Classes/strategies/StoragePermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e989ed63dc4152975053fe396ecbb33eafc", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Classes/strategies/StoragePermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98fe82be57dafdcae1f07449391aad63ca", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Classes/strategies/UnknownPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e983db989ed24e906ae8c5f1c945989caa6", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Classes/strategies/UnknownPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e985ab8fdf70ed5f1c52b1fe3ff1010c1d7", "name": "strategies", "path": "strategies", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9814825db1345022637284e76a0ef7bd72", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Classes/util/Codec.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e985e7b32356a758f588809f0433c6af871", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Classes/util/Codec.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98a612b40a77a90741febe3398598d6eb9", "name": "util", "path": "util", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9822935fb17e391e2da7c96e57eb19ca7e", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98ecac04d3421288927e90b527194eecbb", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98eeee2bc15fa5a84c834eab54c95bfebb", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f195ac1f8945992a357344a689573ef1", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ce6037e0573b91a66f088a3931fafdd8", "name": "permission_handler_apple", "path": "permission_handler_apple", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98781df73b2a2b5a05a3d07582924b839c", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9802fcb4d93e708141386d875efd6dc3d5", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986044f4ee8a0d16c57523805b430249fe", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9818f5a9f51fe5404ea17bb40cab0a3cf6", "name": "super_up_admin", "path": "super_up_admin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98890308633ec72a799b4f9a840a685355", "name": "apps", "path": "apps", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b30aa000c34f929a2c655c7fcab54470", "name": "flutter", "path": "flutter", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987935724974857c5700ef121dde311475", "name": "source2", "path": "source2", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9880335ac038bf4687df48b2ee2189edba", "name": "Desktop", "path": "Desktop", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9864ceef203b34aa71337e6ed289be1fd7", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9859769ec9d96d600990e7667adcbcda56", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988e80c11bba5c73b915d292d2f7431bcc", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98cc0c6161dffc99b6a3127cbbd9f56190", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a4e689efd4f2c23712d5a4bf99451bce", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988558b44de223082d0fe003c0d8e1e5ba", "name": "..", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98819a899c503c7b70ed64550cdd77d5e3", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98d20531130eafe493db8fa074d380f675", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/permission_handler_apple.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98adc3892165b92d544f5f0a2cceec9586", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e988ecf91f991607c8b72590e1f765a8b79", "path": "permission_handler_apple.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98aac807dc81c51b84ac60edc2e0ffee80", "path": "permission_handler_apple-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98c638d98b80b00f3523e611093c74d975", "path": "permission_handler_apple-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987654159e8d97a2a2a0101ebda1cfd7e0", "path": "permission_handler_apple-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98bc1b86f89138bcdfe6ff5d70b4a88b2b", "path": "permission_handler_apple-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98571841afd6eb898191a0ad2dc1384e19", "path": "permission_handler_apple.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9807c92c08ffc15422f5a693aca183a35e", "path": "permission_handler_apple.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98dce1ed873fad109770e634aa10c0b47e", "path": "ResourceBundle-permission_handler_apple_privacy-permission_handler_apple-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e983b6a3645614e8df698639b84f99a1f7d", "name": "Support Files", "path": "../../../../Pods/Target Support Files/permission_handler_apple", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980bcfecae62910c63486bc24e5d185758", "name": "permission_handler_apple", "path": "../.symlinks/plugins/permission_handler_apple/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981c0859f284615776af418bab11676791", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/photo_manager-3.6.1/ios/Classes/PhotoManagerPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9881709cbf3235204f6c03a317fcf32423", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/photo_manager-3.6.1/ios/Classes/PhotoManagerPlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98237e1a8f9793e62960817076e9e86115", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/photo_manager-3.6.1/ios/Classes/PMConverter.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9885d4246cb1a0cb6cfec3173db118fcf6", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/photo_manager-3.6.1/ios/Classes/PMConverter.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981d43cf754f506d07e7b96d7ea0b666e6", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/photo_manager-3.6.1/ios/Classes/PMImport.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989551105e1a95248e276c672cbf1dbd3d", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/photo_manager-3.6.1/ios/Classes/PMNotificationManager.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e983270643537e78879f5987db618ed1992", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/photo_manager-3.6.1/ios/Classes/PMNotificationManager.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988ffb670a2a0d7d95200623265ef63e1f", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/photo_manager-3.6.1/ios/Classes/PMPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f1eccba4172e344b6e06b4bec78d0786", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/photo_manager-3.6.1/ios/Classes/PMPlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b7ec5744e5cccc2fc94f5a568ab52350", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/photo_manager-3.6.1/ios/Classes/PMProgressHandler.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ea546f29880840a281ecda8216584ea1", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/photo_manager-3.6.1/ios/Classes/PMProgressHandler.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f0531bc591a814f05e63732991ab5d23", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/photo_manager-3.6.1/ios/Classes/ResultHandler.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98149782a6e406fa08f098d83479e59ca7", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/photo_manager-3.6.1/ios/Classes/ResultHandler.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980840f18a4bfa593dd94e67695dbf044a", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/photo_manager-3.6.1/ios/Classes/core/AssetEntity.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e983496668f6be758a618558c8c102a6226", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/photo_manager-3.6.1/ios/Classes/core/AssetEntity.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981ebfa5eb65fbe91b3ea9c0b7a1fd7041", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/photo_manager-3.6.1/ios/Classes/core/NSString+PM_COMMON.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b3d2d508ea1d82d3cfce43e10e763d17", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/photo_manager-3.6.1/ios/Classes/core/NSString+PM_COMMON.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98bfa531afcfe43313267fcef8c07f351c", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/photo_manager-3.6.1/ios/Classes/core/PHAsset+PM_COMMON.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98afc9e4549366d502ef926e09c2bb46ce", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/photo_manager-3.6.1/ios/Classes/core/PHAsset+PM_COMMON.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9858cab52bd9903ead1c4d66cc01ced346", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/photo_manager-3.6.1/ios/Classes/core/PHAssetCollection+PM_COMMON.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98934d0d5211dd91dc9f54eed9484bb916", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/photo_manager-3.6.1/ios/Classes/core/PHAssetCollection+PM_COMMON.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986c6129317fa25fa56f63e3ea1ea5d52b", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/photo_manager-3.6.1/ios/Classes/core/PHAssetResource+PM_COMMON.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f6d247a3c7ccf24ab4c6588593ad6a85", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/photo_manager-3.6.1/ios/Classes/core/PHAssetResource+PM_COMMON.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986fe7d761e4693793b9254e28ca8c1f89", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/photo_manager-3.6.1/ios/Classes/core/PMAssetPathEntity.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f3ffb45a56b5488c1e09007edd2ff80e", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/photo_manager-3.6.1/ios/Classes/core/PMAssetPathEntity.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983aaf24c0140d1eeb213182cfdf2d0575", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/photo_manager-3.6.1/ios/Classes/core/PMBaseFilter.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98752693e1025605ad3b3642f2ed91f8c5", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/photo_manager-3.6.1/ios/Classes/core/PMCacheContainer.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98e892b635a61b8ac8d6d5c1866edde378", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/photo_manager-3.6.1/ios/Classes/core/PMCacheContainer.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98064e1d7b5672a85edcfbb69919694e6d", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/photo_manager-3.6.1/ios/Classes/core/PMConvertProtocol.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98bcd6248edfcf50796a1856ec771e7ac5", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/photo_manager-3.6.1/ios/Classes/core/PMConvertUtils.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ca3897099a069c1b308eba29c8def0e8", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/photo_manager-3.6.1/ios/Classes/core/PMConvertUtils.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98fb674aa98c3bea7ab019e04acae87d94", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/photo_manager-3.6.1/ios/Classes/core/PMFileHelper.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98652cecd85132d3d42cfc6bbc3e2a2e67", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/photo_manager-3.6.1/ios/Classes/core/PMFileHelper.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98267d613b686f8e90b446338bd3e8e197", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/photo_manager-3.6.1/ios/Classes/core/PMFilterOption.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98e9141bb0b7e61bb80f1ff2adc8392358", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/photo_manager-3.6.1/ios/Classes/core/PMFilterOption.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ddd2d89cc5a2b7314c3fc7bf13294484", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/photo_manager-3.6.1/ios/Classes/core/PMFolderUtils.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98a06916546af028fc85ae81aa93119c5f", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/photo_manager-3.6.1/ios/Classes/core/PMFolderUtils.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ae16d131ae39904cc2b04cdf829038a2", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/photo_manager-3.6.1/ios/Classes/core/PMImageUtil.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e982d84ffd29ec576b4a32c5eedc306199d", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/photo_manager-3.6.1/ios/Classes/core/PMImageUtil.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987fb249b655368e3bdb7e6a4019b6d1c9", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/photo_manager-3.6.1/ios/Classes/core/PMLogUtils.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e987b20a6ccb5a33875b21b860b0dad636e", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/photo_manager-3.6.1/ios/Classes/core/PMLogUtils.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9884511de53146198d6473eb6bc6c5cee7", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/photo_manager-3.6.1/ios/Classes/core/PMManager.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e986562b68e77a8bc040fadce9b2e16a1b7", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/photo_manager-3.6.1/ios/Classes/core/PMManager.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b77ff5eab8be34d9705851ec3daf377d", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/photo_manager-3.6.1/ios/Classes/core/PMMD5Utils.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9873105cd40b28f82f796c307a8afb47af", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/photo_manager-3.6.1/ios/Classes/core/PMMD5Utils.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98eef24a22b07542486c48ae9e8d24d54d", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/photo_manager-3.6.1/ios/Classes/core/PMPathFilterOption.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98e7037d7860c8ef87b1e571d8d799f48b", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/photo_manager-3.6.1/ios/Classes/core/PMPathFilterOption.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b5091167e5f8740abe0cbae90304630b", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/photo_manager-3.6.1/ios/Classes/core/PMProgressHandlerProtocol.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9812bdf6358fee6c1f1d89e814bdf9a2e0", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/photo_manager-3.6.1/ios/Classes/core/PMRequestTypeUtils.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f021b54d0c11926943338842e0646659", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/photo_manager-3.6.1/ios/Classes/core/PMRequestTypeUtils.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988650730f704b0b2df245424e05a06b21", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/photo_manager-3.6.1/ios/Classes/core/PMResultHandler.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9816b7460a2f6a45108873f0f0a55e4103", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/photo_manager-3.6.1/ios/Classes/core/PMThumbLoadOption.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e986c49fbd1844c1e9be5cfa498c2b37e77", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/photo_manager-3.6.1/ios/Classes/core/PMThumbLoadOption.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98553823561766d9846afe634055a64e70", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/photo_manager-3.6.1/ios/Classes/core/Reply.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ecfb9c45ff8dc86fb0d4c8d675b83092", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/photo_manager-3.6.1/ios/Classes/core/Reply.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98c5dec9d0831e19f457a8578fd137daeb", "name": "core", "path": "core", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ad8557baeeed4e60bb1e8eb610d462e7", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98dc6752dffd0e99e0d1fef3e32b271c0b", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/photo_manager-3.6.1/ios/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e986418c62c332730c4de12380317c8f3c9", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9878a197b510cf292971d592758cbf4a60", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a0486f0a588700c8f5f03e3fcad3017d", "name": "photo_manager", "path": "photo_manager", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c381b1573fbd8d3866fd4668c46e91e0", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f4ede2113ceb6a3f42e91c979316bf72", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a613cea5017718eab12ea8852dbb371d", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982e267d40d70df03d7c7a796d06516994", "name": "super_up_admin", "path": "super_up_admin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983e697a8b9056642e29acd9b6878405b0", "name": "apps", "path": "apps", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984b92437d4ff246a962d88bbafb0827cb", "name": "flutter", "path": "flutter", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981c8e834684eb25bbb31522fa565e090e", "name": "source2", "path": "source2", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a5f712a45fab7b36672424fb4b3075f6", "name": "Desktop", "path": "Desktop", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9880686660fa8f520ad668de4c083a74a5", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9833ef4f0b4b0c2681da366018deed03ef", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98eca6f76fef1be014b51a4db109c5fda6", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98bae117d1301fd216add3e66474e5140d", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9836792b501853587e77abb1698e5abf10", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9808d4db6b1f9ffa6f8a4fad8217845466", "name": "..", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/photo_manager-3.6.1/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98a4c86de13df3efb8e58f94988da8d960", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/photo_manager-3.6.1/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e9805d7840ed2a12736460ff4513a3be263", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/photo_manager-3.6.1/ios/photo_manager.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e989ea09effef19ed5366b5ee5ad88fe300", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98c3070ad7583128ce1a1616a59221e6c1", "path": "photo_manager.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f7221c811f1bb8342db5cda332893940", "path": "photo_manager-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9871f17c43cca1ef609b46654bdb75f2bb", "path": "photo_manager-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98175bfcdd8168c78031feacb39a4f73dc", "path": "photo_manager-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98cbdc2d5e40318534a6fa9ef1f7e6836a", "path": "photo_manager-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98488068341154f61f5d0bc36b7eaee863", "path": "photo_manager.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e981e3fd56aeee9fe2911f687a9cd639d1e", "path": "photo_manager.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e989425b62648dd45d33a6d5fc75e01f520", "path": "ResourceBundle-photo_manager_privacy-photo_manager-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98aa3c40da54252f487ccd952bafad0317", "name": "Support Files", "path": "../../../../Pods/Target Support Files/photo_manager", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9893dc3e42d1c687f91ad36dfea26584d2", "name": "photo_manager", "path": "../.symlinks/plugins/photo_manager/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98f717ccb7de3a422e4d95fac2da2a6a90", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/sensors_plus-5.0.1/ios/Classes/FPPSensorsPlusPlugin.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98e9235619ec4ea7020c2888f8e293c8a0", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/sensors_plus-5.0.1/ios/Classes/FPPStreamHandlerPlus.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e986bb3a01f3e6ae120423ce2423b4f7314", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e87ce1e7009eba07a66744e1a1a55067", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981775614e0a36431a1bb7e35b77de654a", "name": "sensors_plus", "path": "sensors_plus", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98465f0d6ed2f789b2a7cb1193615dca6a", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a99cd1d7360be322886fdbc2d2aedb1e", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987a82fae3b5a7887a553f02a6657520c9", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9873484f73e3405b77e90b910f7dc39375", "name": "super_up_admin", "path": "super_up_admin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98dbeb87b6cd73c9a8be0aab4eb75e15c3", "name": "apps", "path": "apps", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9888ca92cb5733e38aff100b655e9ad807", "name": "flutter", "path": "flutter", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98fb647c22bf0f5f345933475b3daf2b49", "name": "source2", "path": "source2", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988bf1d6528aabf15a47236e1717961d44", "name": "Desktop", "path": "Desktop", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982c5336b7bc86fa5026da411941e5eb3c", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98d1a63d954eec4d25b77854f05d631be4", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/sensors_plus-5.0.1/ios/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98c07e16e34bc27db41f1ec7d80ce2bf6e", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9883773efb6845b3f34a7c31a580e6a315", "name": "sensors_plus", "path": "sensors_plus", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9891b0e8975b28b3c50baa585d281754a8", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9852c61e394d0345b79365d92cf1cab11b", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987135bd4478ee4e2635a3b54c8fc0d51f", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9821e20e7aebb79eb37e7845f714e861da", "name": "super_up_admin", "path": "super_up_admin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98964cf3226278810b2fe49f080a952d99", "name": "apps", "path": "apps", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982f9bd2857d00e6c4aac2a33b692733ee", "name": "flutter", "path": "flutter", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98bcc5937960851b56c4229a884eb745ee", "name": "source2", "path": "source2", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9859a276e63589d1950684b6a44e435e5d", "name": "Desktop", "path": "../Desktop", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988634828af7a30a797657e010c60c9769", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ebf8d6fbf5bdeecb8cbabebc639c996a", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98043d39199c688fd90f9c57f6de3b59cb", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ef09c82180c91ca1489784a2d1738d8b", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98fc15a75baa30e0a4b334d30c2d1e289a", "name": "..", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/sensors_plus-5.0.1/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e989602ae0c1dab517783b6d37f3984866f", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/sensors_plus-5.0.1/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98d122555dedb13560f898f31b15eb9145", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/sensors_plus-5.0.1/ios/sensors_plus.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98e2cb7b196fb89272dd46a32bde2059ca", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e988302f7d060fc813bd9cf6b1ab44e0f8b", "path": "ResourceBundle-sensors_plus_privacy-sensors_plus-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e9895ee4826188080b1934450c57a49db78", "path": "sensors_plus.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98715e51093057b89d9caa802f98b8e646", "path": "sensors_plus-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9863a31a01bd43c19779edb4dad66a5e0a", "path": "sensors_plus-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ecef84a93f2cac35aa62cb80439f735b", "path": "sensors_plus-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c7651ca9a85b10dce7d28298fc644101", "path": "sensors_plus-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e988fb4b0b5ce7d25febbd83b6f5beebcf7", "path": "sensors_plus.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e985a088aeb425b4b3dd191b3057015f5d9", "path": "sensors_plus.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98b066fbfb8c5a19d349d7ae6584b0b952", "name": "Support Files", "path": "../../../../Pods/Target Support Files/sensors_plus", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984985a781338391573de45900da59252d", "name": "sensors_plus", "path": "../.symlinks/plugins/sensors_plus/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98bb41bc1e1269311869b7bb8a380d798c", "path": "../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.3/darwin/shared_preferences_foundation/Sources/shared_preferences_foundation/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98cf9688de1c2d12d8a69187bde04506e1", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9881ae2250520d02788de0a85e3ba958fe", "name": "shared_preferences_foundation", "path": "shared_preferences_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984e892870638a044984746907b8a6dc17", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985ccf75bd57ccc9f85091ab4fc8b601ff", "name": "shared_preferences_foundation", "path": "shared_preferences_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d9425f8590df51542da9033ec156c005", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987a21c151a5c6948394bc14082b4fdb38", "name": "shared_preferences_foundation", "path": "shared_preferences_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f1e752e1030c311dee21daf9eb2c4794", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9870146ec20c3a5596a8266c3fbe7c7ccc", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980fcb43a755d68bb99126735ad8dd8a6c", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986084b5d0be663754089df00851ff1dcc", "name": "super_up_admin", "path": "super_up_admin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d1f67c2b2feaf93180c62b9915d3ef06", "name": "apps", "path": "apps", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ba64bf32824e42815ca3fc9fb718e11f", "name": "flutter", "path": "flutter", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988129d476f25b830cf11e0116d4b06aae", "name": "source2", "path": "source2", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9809b29a6ce0dac4a4e94ba23e433a4803", "name": "Desktop", "path": "Desktop", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986e0b6a00204747356afd13602f4fdf2e", "name": "..", "path": ".", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98bdbde0facb1a14802135fa14af876658", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.3/darwin/shared_preferences_foundation/Sources/shared_preferences_foundation/messages.g.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98953eef84e58e79c728e2a9e7510d2ce2", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.3/darwin/shared_preferences_foundation/Sources/shared_preferences_foundation/SharedPreferencesPlugin.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9805e580af11ef6ebaa80a56e84d199507", "name": "shared_preferences_foundation", "path": "shared_preferences_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f5103006198ecef21d7e07af2afce127", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9858b9bee19ae555edaff8182b6155c0fd", "name": "shared_preferences_foundation", "path": "shared_preferences_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98be2588e46d178388ac2952fc68052ccb", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9880b19ee54d231872f892a2ab8a46d13b", "name": "shared_preferences_foundation", "path": "shared_preferences_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988bef58df7ab2fa1929a624958dda7577", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ef8598ad5a647f4561d7a8a2ce509153", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98318ce9d19c390910014a541c9019c35f", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9804ab5c12cbd9f56781cf9d50c9551942", "name": "super_up_admin", "path": "super_up_admin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a3d6f9fbfd5bf1d935de3ba2fda2e69d", "name": "apps", "path": "apps", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981fa2864df6a186d94c7d0021ff933392", "name": "flutter", "path": "flutter", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98949c929b2eb0026003e953024f8acece", "name": "source2", "path": "source2", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982982ccd78a207aa3aa943c8f1d9ef411", "name": "Desktop", "path": "Desktop", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9810979525f2cdb4b1f75222413454cf3b", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983c1f4edf8eddfd21360f305fea8c991e", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b477c934df005988ed89e2d710c18864", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98dc885c3ad86660b6736af51a2a4c6b26", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a528fd8374241178273dc3189569ab58", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c7aba33f2616d19047a958551e8e47bd", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9891a8bde8f8802953faa1da0194cf1f38", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98eae4fa5de770b92b42f76636b5348b9d", "name": "..", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.3/darwin/shared_preferences_foundation/Sources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e986f6e77b0444d8422e7319d47f8b9ce59", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.3/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e9852d7d49a678777c2b4892aa505448012", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.3/darwin/shared_preferences_foundation.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9875dd156a644452beae30bc5af043f48c", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98cc2dbd890d489de2f0a5c1383a5c0d8f", "path": "ResourceBundle-shared_preferences_foundation_privacy-shared_preferences_foundation-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e9879c791c863e67ed701c31d982d7bcc09", "path": "shared_preferences_foundation.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98c856951ed9b39400d9dd8a314cf9a40b", "path": "shared_preferences_foundation-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98c257604771ed53ce0977dfbd0c845663", "path": "shared_preferences_foundation-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9813fab68db5d7858570ccb1a05a0bd3db", "path": "shared_preferences_foundation-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f84879251c96de97bd82342ae3275f25", "path": "shared_preferences_foundation-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e980a2d22395c85bf38c9a8cb0f8ff1bfcd", "path": "shared_preferences_foundation.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98f1dfb0babc287eb4f240e38f6c72aabc", "path": "shared_preferences_foundation.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e985d991932f2653b01432f54fd73ab36a5", "name": "Support Files", "path": "../../../../Pods/Target Support Files/shared_preferences_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984293730e21a3f87d65843340e06f88eb", "name": "shared_preferences_foundation", "path": "../.symlinks/plugins/shared_preferences_foundation/darwin", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98c7056f68e12dc64a047d2054df53cbd4", "path": "../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1-1/darwin/sqflite_darwin/Sources/sqflite_darwin/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98e46ed3024c2761924b57d0854bfb5094", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b8d9f86b311eded666bad2bf5ff7d40d", "name": "sqflite_darwin", "path": "sqflite_darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98dfd53cfd97bbd4b9a65a29738b51e326", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c9bc74f87dee019e006b9ffe91d24609", "name": "sqflite_darwin", "path": "sqflite_darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c2100c42b3af80309214539a456fb8c4", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ee7fed77d142d4c2ec5429cfb465720b", "name": "sqflite_darwin", "path": "sqflite_darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98fa0bf3be00e707433f79c434f02d4cd6", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9879bc21a45afd9e8c13b3a923df94b01e", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985b6bea44ddd728dc6b78e59364438880", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98498160c83b7cb7a102b899ea045e2300", "name": "super_up_admin", "path": "super_up_admin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f28ba349f75ffb1b7e1a4f0ae0b10109", "name": "apps", "path": "apps", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9816cfc4597e5a2dab42d56ff2e87660fc", "name": "flutter", "path": "flutter", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9848afc8af593b9c6eedda05c92bb421ec", "name": "source2", "path": "source2", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98005274860a3d212f129baeccb30ae2c0", "name": "Desktop", "path": "Desktop", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98624746643e6f742eaeec85b8f6dd00ac", "name": "..", "path": ".", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98442e76af4910b07b192daff43aa11e43", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1-1/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteCursor.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98d745c4e288ee0b34493cfebfbd2a841a", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1-1/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteCursor.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9874199701782eb3ec7b2de0d8ab050510", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1-1/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinDatabase.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98210447ba6cde755bfd4843bce7372b39", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1-1/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinDatabase.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981b500e003aa6a8d62dca26c3ea3a6524", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1-1/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinDatabaseAdditions.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98a68e65587089fc85f50549a4af9122f4", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1-1/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinDatabaseAdditions.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981b4a7d4bd00ebaca2e7a10b73623b9dc", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1-1/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinDatabaseQueue.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98010b26720d4170c17a9023a90a3792f1", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1-1/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinDatabaseQueue.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9835ea6f90501b9386b26d12177fb50582", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1-1/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinDB.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98da2f101284b933358c48a80705ef239f", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1-1/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinImport.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984727f075092c9d353e7941b6800da9bd", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1-1/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinResultSet.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98c0b291a2e75647e772e30a8a4bee3cbe", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1-1/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinResultSet.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98491e90b0308a24a5a9f0b00735ccebff", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1-1/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDatabase.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9860b5f240df12084f56a59f43a2d226ac", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1-1/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDatabase.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98149b72778ff7ea5b928442b92e00973d", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1-1/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteImport.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9860f4fd27cc6db48eb75e57e413107986", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1-1/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteOperation.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e984814012181ebc0eb5d2434be569e3bf6", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1-1/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteOperation.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98646508f59397618e02da22842c7030e4", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1-1/darwin/sqflite_darwin/Sources/sqflite_darwin/SqflitePlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9832204c3a38bcfd7528217f074c708f70", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1-1/darwin/sqflite_darwin/Sources/sqflite_darwin/SqflitePlugin.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9856e6f669ec687b1cdd80d648ee7c05a8", "path": "../../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1-1/darwin/sqflite_darwin/Sources/sqflite_darwin/include/sqflite_darwin/SqfliteImportPublic.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d19156f35b86f6f8acc5ce4eff0a5ab2", "path": "../../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1-1/darwin/sqflite_darwin/Sources/sqflite_darwin/include/sqflite_darwin/SqflitePluginPublic.h", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e983bebc3c5a7298c747e9be9a4a743f9de", "name": "sqflite_darwin", "path": "sqflite_darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f8aebf8d1ebf2a8d50ad9f2990406adc", "name": "include", "path": "include", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ac0de3cce689507c6196a3105e23a6d1", "name": "sqflite_darwin", "path": "sqflite_darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ba4a9ee6e0fd11a4a961f90ddb68f231", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9859ab7f36ec45caf010fb907ac8b84633", "name": "sqflite_darwin", "path": "sqflite_darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9842d5b1093424a2ca49cc52965b7dfa78", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e53eff620edf04a99df6214c03311f38", "name": "sqflite_darwin", "path": "sqflite_darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984c6ff745b3858ac589ec7b4666533570", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983cc934ab4fcace425bce2c963d210926", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e156afc4e63f0a82551b39eb49e26001", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9870b773e81c7af0a388d747855e94e463", "name": "super_up_admin", "path": "super_up_admin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989782cbaad045df1e851c1a0a80528e02", "name": "apps", "path": "apps", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987bca5505abe11375e8cee75121169471", "name": "flutter", "path": "flutter", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98533e993b7d40ffc43d0daf58a21ec37b", "name": "source2", "path": "source2", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ccf2aa61581682b21e2a64748cb7efad", "name": "Desktop", "path": "Desktop", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98982025a4ed86316c1243f9d7a9b94c12", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9800097019837581073234b291ce4d595f", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b850641340292316fb3a315c8a7aa58b", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ebfcda321525e0dae5ecb67d56e87a9e", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9810efbb4da56e0f00285c24da18e3be7f", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ef11b852cfa35fffd77930a22612702c", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98679ffc4397ea4bf7584105bfbe5724cb", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ec99c79d82784734ced6199fed730c5d", "name": "..", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1-1/darwin/sqflite_darwin/Sources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98d2c2e3bcbe1b1c8b5faf34f44c6eeba0", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1-1/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "net.daringfireball.markdown", "guid": "bfdfe7dc352907fc980b868725387e982255c0cd1c8a01364c4eb9051791485d", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1-1/darwin/README.md", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98effc60a14ad076b7b76244aafbf7535c", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1-1/darwin/sqflite_darwin.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e981a42df39fc7154a63b2316c1c3436666", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98bc487af870c6e19e96e8f2a98e906c42", "path": "ResourceBundle-sqflite_darwin_privacy-sqflite_darwin-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e983c2d561945d994275689715493329af8", "path": "sqflite_darwin.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9850b3b93b14e9ecfca7aab85810e33591", "path": "sqflite_darwin-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e981b5d2ce359200db2146f7b3614a5b12c", "path": "sqflite_darwin-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9852a9c57fa84003f71f0fb7b1ff4406c5", "path": "sqflite_darwin-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985cc95fbaab2afbad03489470d20639f5", "path": "sqflite_darwin-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98ef6fcd6a730a8cdc8f5d24af8ff345f2", "path": "sqflite_darwin.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e987e3ab93897b9f4b9d408eb05892e103b", "path": "sqflite_darwin.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98566f4648db993731b2374ce3cbc96ddd", "name": "Support Files", "path": "../../../../Pods/Target Support Files/sqflite_darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983a144a6ad258c85233d029c4c25ddaf9", "name": "sqflite_darwin", "path": "../.symlinks/plugins/sqflite_darwin/darwin", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98516b4ba6cea6e73df24a25e50de99c8f", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqlite3_flutter_libs-0.5.26/ios/Classes/Sqlite3FlutterLibsPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f710ae39e0db3307768ce6931a3e4c36", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqlite3_flutter_libs-0.5.26/ios/Classes/Sqlite3FlutterLibsPlugin.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98f35a411e386e1a653bff2bd59e90b3e3", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98154393a4cee47b87bda664109218826d", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b8812d793a397f4e99c9923f5e85a26e", "name": "sqlite3_flutter_libs", "path": "sqlite3_flutter_libs", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98713995392b5e428ce41fb51994c2e5e5", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989a990b9a5577876274a0d2a751e24cd1", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985b610e5b5880f8911c960c5854f5d840", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98014a474aab716c1d92157c75aa39cbe2", "name": "super_up_admin", "path": "super_up_admin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9861f5f0b8725d74537b142f1203cd3355", "name": "apps", "path": "apps", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986df611dc4726a84bca93ca9d7d2c6696", "name": "flutter", "path": "flutter", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9854da8089253a9514da6135df98c7cffa", "name": "source2", "path": "source2", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982f8264bfd284293149a5e1f28b5a39d5", "name": "Desktop", "path": "Desktop", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9878ba247064c5f7ad950fe51037da9186", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982b983d769756ad9e14a0ef0d2e5c6234", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98880da70c09534c00de3c20476a4e5748", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ae9038eef7501d630282d783f4fad27c", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988bc5fa5df22ae108c906787f8fdaa17d", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980baba273ee72e236c7fc6615e61de67e", "name": "..", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/sqlite3_flutter_libs-0.5.26/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e983aa1811e8937bd8b85c5bfb4e3335773", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/sqlite3_flutter_libs-0.5.26/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98464074581ed7fc7db2594798591a26e5", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/sqlite3_flutter_libs-0.5.26/ios/sqlite3_flutter_libs.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e980eeb20cbc7b09cbad0d1204db9e9a80d", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98e4e17e8abe10f467075f04388b035dd2", "path": "sqlite3_flutter_libs.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98fd2f0e9b71caa855a9b76932f60feb1a", "path": "sqlite3_flutter_libs-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98a06e9899b74b60cc2f2b202c3b4346fc", "path": "sqlite3_flutter_libs-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983a9b4663e7fc8901498436baf424a451", "path": "sqlite3_flutter_libs-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988f15fb3613346f51c1159567689355d8", "path": "sqlite3_flutter_libs-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98418eee6642c3f6034e9cd73f6e67828b", "path": "sqlite3_flutter_libs.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e989c1ad7a61f77ae909c943f64a8d91cc2", "path": "sqlite3_flutter_libs.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98de672f9d7156f9c31bd352b12fd7f7c7", "name": "Support Files", "path": "../../../../Pods/Target Support Files/sqlite3_flutter_libs", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ff6d13ce4e13358c2ee02b11ad9dafd7", "name": "sqlite3_flutter_libs", "path": "../.symlinks/plugins/sqlite3_flutter_libs/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98968657535d076c0b0bd39708a29aca70", "path": "../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.1/ios/url_launcher_ios/Sources/url_launcher_ios/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9853794e6abc3c9dafd94529dc06d9b935", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987018dbc5ff342dfde901e209ebc4dd3f", "name": "url_launcher_ios", "path": "url_launcher_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98af9a8543abb661d11bf9209828b12478", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98cb1f7a130649bc5ff5a10a23cc473393", "name": "url_launcher_ios", "path": "url_launcher_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a94e163075c0b895acfed6955c9c8058", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e2dc3eff67067bf005f57e5264a8257e", "name": "url_launcher_ios", "path": "url_launcher_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b3e02258c142ff013ec1481ff35a2eba", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9867f23e43b67c31d5ccca245f19d89073", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987f286e09ef14f14048e7e7384a01b76c", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9857573e55ef3e152a8663fbffcea2947b", "name": "super_up_admin", "path": "super_up_admin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98198220262a1f6a62a94cd37a3d5caccf", "name": "apps", "path": "apps", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e48112f3e7b4d4a653e400b8125db953", "name": "flutter", "path": "flutter", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986d59dc32892df61b385aad1cdb50bf46", "name": "source2", "path": "source2", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981568177172956df8306ac0fcd63cc419", "name": "Desktop", "path": "Desktop", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e54a23c3047d9bab17cca720d0f1ea66", "name": "..", "path": ".", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e988ca19f6f5e2acacf205ae7010ff53d0b", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.1/ios/url_launcher_ios/Sources/url_launcher_ios/Launcher.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e980613dd65f06b3b115feee82761b257e1", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.1/ios/url_launcher_ios/Sources/url_launcher_ios/messages.g.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98f5e326d4182bc7ad26b52e2029640a7d", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.1/ios/url_launcher_ios/Sources/url_launcher_ios/URLLauncherPlugin.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98983def0fe989ecf4d6519697b69e4176", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.1/ios/url_launcher_ios/Sources/url_launcher_ios/URLLaunchSession.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e989606814c851e0ec7c987857aac00a211", "name": "url_launcher_ios", "path": "url_launcher_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987e1e20bbc6b590d9174bb0db7e4359b2", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9804f17dc34dd13844896392d8b40d2474", "name": "url_launcher_ios", "path": "url_launcher_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d94094476d28e222760285a7a17282dd", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9897e46a396c1a97e50ccd357376841b10", "name": "url_launcher_ios", "path": "url_launcher_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9857a2f0311137088e7044679ab554b8e9", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a08bea582dac82b22e75913bdcb16605", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f5afc776d80fecf776ec6a7e793de492", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984e8c5d03ae6f2ac107958d64350774c7", "name": "super_up_admin", "path": "super_up_admin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986038821f4495c700ce0e6db8e15932fc", "name": "apps", "path": "apps", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9844da2c3827201a1e3ac847faa3bd9ba4", "name": "flutter", "path": "flutter", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988aeed1bf4517bf336f056936c47f8948", "name": "source2", "path": "source2", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983dcd4e26a78fa0fd81e69e3b12d3dc36", "name": "Desktop", "path": "Desktop", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98252f1999472b62a1bb197bb03bd0d0ad", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f004301506b6e12cec70b24c8097d304", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c580cd8d9f323f13992148cc72076d60", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985f73f1809bbd5289b6404a62416373c1", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987f3a993d2e432a413cc1945d7265b19c", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b6b8fffdc5989541bb634c88a676b735", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982af0072e098bf8ed9de5722bba20947f", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985506127091a0686f891eb61249a293aa", "name": "..", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.1/ios/url_launcher_ios/Sources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e985a674bfdd64f4a924564a575822bf304", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.1/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e987b4c0e7b1877a0543534351e6540ce0f", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.1/ios/url_launcher_ios.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98233e9321dd0f4161e49eb77cc7e1643f", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e985699b91d4bb2bcdee17b305a705949c4", "path": "ResourceBundle-url_launcher_ios_privacy-url_launcher_ios-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98c89b7305b2913badad6f7f3010fd691d", "path": "url_launcher_ios.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e987b7321a6c8c5969d028ca9c408e8f62a", "path": "url_launcher_ios-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98e1711c0c90b40c6caedfe9dd7e4e615b", "path": "url_launcher_ios-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a5d4bc8ef0aacaedd1fe1a93da01a4fe", "path": "url_launcher_ios-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e5a0cb2f730c343f1226ee6d1ce127e7", "path": "url_launcher_ios-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e983c65acda529e4c6c60a67a9ab2e40c7a", "path": "url_launcher_ios.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98df450d9202e469580256a6ca5765b5be", "path": "url_launcher_ios.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9815c12451854b5bbbcb703417d3d493a4", "name": "Support Files", "path": "../../../../Pods/Target Support Files/url_launcher_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988ade79a4b296c60b35d6334824b3464b", "name": "url_launcher_ios", "path": "../.symlinks/plugins/url_launcher_ios/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e988bc9a2141b689852ff1885da4b297b5a", "path": "../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.6.2/darwin/video_player_avfoundation/Sources/video_player_avfoundation/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e987935a8dd91aced06927be94597c774fe", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986e386fbcc7e870927eb3144c27cefb91", "name": "video_player_avfoundation", "path": "video_player_avfoundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98978e663792dd1e4a5082b18664d532fb", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98be8637c4da8281d9b58aa5e317d10c8f", "name": "video_player_avfoundation", "path": "video_player_avfoundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ca0122e12d295da5472994f41baffc14", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a2717042e48caa7883bd645d3768627c", "name": "video_player_avfoundation", "path": "video_player_avfoundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b888d274911942dd5aac07de66508d64", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986f05a1939cd2aa3f4c64979b40d5fef8", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9885f77f133c908ce7cf885089b8d63664", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98320aa40e4fd62ef99f4a08aa3a68f952", "name": "super_up_admin", "path": "super_up_admin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9893ba9727e3704a0b5c94ac63454ad68b", "name": "apps", "path": "apps", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f3de0491f1a354a44a775e5134747f79", "name": "flutter", "path": "flutter", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985f9d02efcab754e6e7392d30eea80c8a", "name": "source2", "path": "source2", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f6803f054d74112d5203de60bbbe6c31", "name": "Desktop", "path": "Desktop", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980687d1a9bccde9ce8e90bc4e64b2949a", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98cdc509970b9dc6880be8df2a1acc9ef9", "name": "..", "path": ".pub-cache", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98a884250b276c16190d3580cc718c3878", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.6.2/darwin/video_player_avfoundation/Sources/video_player_avfoundation/AVAssetTrackUtils.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e980c5e0d54d705ef0912bf1af78845ddd3", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.6.2/darwin/video_player_avfoundation/Sources/video_player_avfoundation/FVPVideoPlayerPlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98c50c1ac6c1f5c8646f2f86d412285ecc", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.6.2/darwin/video_player_avfoundation/Sources/video_player_avfoundation/messages.g.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c0fc9b0bb52e078b820ed7cfc6cd6d69", "path": "../../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.6.2/darwin/video_player_avfoundation/Sources/video_player_avfoundation/include/video_player_avfoundation/AVAssetTrackUtils.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ad442f68b654611e41021c57b39ad3c1", "path": "../../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.6.2/darwin/video_player_avfoundation/Sources/video_player_avfoundation/include/video_player_avfoundation/FVPDisplayLink.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98715ebbb28841924e44981c0ff92e6d48", "path": "../../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.6.2/darwin/video_player_avfoundation/Sources/video_player_avfoundation/include/video_player_avfoundation/FVPVideoPlayerPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986655b878da9184a9f683fa1a14496b71", "path": "../../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.6.2/darwin/video_player_avfoundation/Sources/video_player_avfoundation/include/video_player_avfoundation/FVPVideoPlayerPlugin_Test.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986581410352e100951869b5429e4216e2", "path": "../../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.6.2/darwin/video_player_avfoundation/Sources/video_player_avfoundation/include/video_player_avfoundation/messages.g.h", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98135e544fbd02f411835d636af36495bc", "name": "video_player_avfoundation", "path": "video_player_avfoundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e1c2174fe9832816a059b48de866fd9c", "name": "include", "path": "include", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9818d78362e8f8e9bfbc44412d86637369", "name": "video_player_avfoundation", "path": "video_player_avfoundation", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98e53f52e3d8d67f3dabda0fa140a7ecd6", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.6.2/darwin/video_player_avfoundation/Sources/video_player_avfoundation_ios/FVPDisplayLink.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98b6eb4f8f8b16214d9f9f14bec40eb0e6", "name": "video_player_avfoundation_ios", "path": "video_player_avfoundation_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98db6274c5930e6d65ee917c68799829ed", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986d09e8e2e89281b974d2a7ef27bf06f0", "name": "video_player_avfoundation", "path": "video_player_avfoundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98bab74d6a081c939141c54438d25ff882", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b9cd5b499f14b23dbb126c8714cd4270", "name": "video_player_avfoundation", "path": "video_player_avfoundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98073b393cf0d3ac9967e6f9f20d478eba", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987eee756251432a5db8db5550f9b9c0f2", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98975f906ce56458de34cb4003bad9ee94", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980bbbb01bd3f663cd04f4ab194525caee", "name": "super_up_admin", "path": "super_up_admin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984612ba0583edf16946c402a0f3b82eb2", "name": "apps", "path": "apps", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9827751b0cbde9ab32f7607c6aa85013ab", "name": "flutter", "path": "flutter", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9876be2514acfc313a6e7b5f687c93fdb5", "name": "source2", "path": "source2", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980f803e274afb9f1c0eff6d672cd20001", "name": "Desktop", "path": "Desktop", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9882d5e27047e3ea2aa9323d793260fdd6", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989d0ddaa2aa68ae6429040f8cf2765d9a", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989c9fb40946588636c58b4d3ae8ab3fc7", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981e9882e8885f8c2394f3d3b6b80639ce", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ad7eb87fe301443c5be3ad1920f6e2e4", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e5dd49eee49a2322d097ef8a5fef3d09", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f1ea21937cae20e783c50733254b723f", "name": "..", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.6.2/darwin/video_player_avfoundation", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e984e1fd9565ddd079d52bc59e1cb1519e5", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.6.2/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e986c72e964a6065e24a4049474d07523f9", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.6.2/darwin/video_player_avfoundation.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98ee4948d5539bdcc2ab238edae75e234e", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e983e49fd8d68301160f32af9f5be010463", "path": "ResourceBundle-video_player_avfoundation_privacy-video_player_avfoundation-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e9859f877b2db0927022186198ec99ecad3", "path": "video_player_avfoundation.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98dbf77c4d7c8b03d4413ca4f944761c20", "path": "video_player_avfoundation-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9846010cfd88307ed5b42f537ecd78279b", "path": "video_player_avfoundation-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c80aa818e7c3cc7110c0888bf984500f", "path": "video_player_avfoundation-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b7390e665b9e955e36e85a7517a85217", "path": "video_player_avfoundation-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98c3453f8f9378882d9c58a70ae5fffdb1", "path": "video_player_avfoundation.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98e035d7aad63c8c6291aa0cc47216822c", "path": "video_player_avfoundation.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98aa71f6dfcd23f9ed9d6e742c563e9971", "name": "Support Files", "path": "../../../../Pods/Target Support Files/video_player_avfoundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988a07b63b5b0794961465b25d7f46334b", "name": "video_player_avfoundation", "path": "../.symlinks/plugins/video_player_avfoundation/darwin", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98aecb510a50befda28d78c4615e6995f4", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/wakelock_plus-1.2.8/ios/Classes/messages.g.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9843bdb0d3bfc81180efce6bfcbd491491", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/wakelock_plus-1.2.8/ios/Classes/messages.g.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981a113bd5442f51d078cf4c747a32dd5a", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/wakelock_plus-1.2.8/ios/Classes/UIApplication+idleTimerLock.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9816e3ba3af9d5c42c2ceaecec3b434684", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/wakelock_plus-1.2.8/ios/Classes/UIApplication+idleTimerLock.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987260a3a5d723b1181a58e4cbe796ae6d", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/wakelock_plus-1.2.8/ios/Classes/WakelockPlusPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98865ab9b42b1d40644c687f104eedf6b5", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/wakelock_plus-1.2.8/ios/Classes/WakelockPlusPlugin.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e984c376a699f4ffc8ff251b3bac0d79317", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98fac8f9aca1d7acb50394f875300ee8e8", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/wakelock_plus-1.2.8/ios/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9862c705444444e7b355c511b96498422f", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e72f380f954a6214088914a3fa5ecab1", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98594606e2f990bccd4338be2c791558f5", "name": "wakelock_plus", "path": "wakelock_plus", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98fabe56011892d34b8e52c708a2699153", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985db8725f0ce533a0e7ea0744a987d4ff", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c35d067182f95b617b04c7ede8765ee8", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9808e005e7d29e140e11e859629844b90a", "name": "super_up_admin", "path": "super_up_admin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98346d4477e2d2bad376a19ada70fc6824", "name": "apps", "path": "apps", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985d548062d96e8b6badad525f1c678e62", "name": "flutter", "path": "flutter", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988d1aaa3b456a9e3c43b311cb3a20b6fb", "name": "source2", "path": "source2", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c248f1429f4ea6d067f2c8261d784b28", "name": "Desktop", "path": "Desktop", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98247284e6509074ba0c8943d8f0ca36a3", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98de566dd8842411ad0698735746785eb1", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989dd791ea83c98907e96a3d449014fe43", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982baaf9c400f20396ab2598454e65fa63", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980a7cfa3d9eaf37ce365f7d502620130c", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98520e2e3b7cb1093e7bdb59482d90c040", "name": "..", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/wakelock_plus-1.2.8/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e980015b1074050bf853d377f44834f5799", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/wakelock_plus-1.2.8/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98d3c323586499cf7d006dc0025a52ad86", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/wakelock_plus-1.2.8/ios/wakelock_plus.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9856f19e66b16c4f703b19786f36df27ab", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98c39e440326811512eb0cf11132e51e5f", "path": "ResourceBundle-thermal-wakelock_plus-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e986676952de1b61869f317964fb55997e7", "path": "wakelock_plus.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e987651fd72625b58c69114cdbd7ec34e8f", "path": "wakelock_plus-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98b1cac44b50d6c8e5fdbfe9fa3ebea2e2", "path": "wakelock_plus-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f7dfb63880f7b91966be37f31aba39b1", "path": "wakelock_plus-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9867cf3d5abf6557da210a5218641007c5", "path": "wakelock_plus-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98345a271a0994c145f04ce6321f356433", "path": "wakelock_plus.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98eab4076ac19090c24c3f4d1277e30fc2", "path": "wakelock_plus.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e986852eba9c1cc4dc79585dd31e260d963", "name": "Support Files", "path": "../../../../Pods/Target Support Files/wakelock_plus", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9834fe37dd3f4b5ffcae12cbe4fccf316e", "name": "wakelock_plus", "path": "../.symlinks/plugins/wakelock_plus/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e9893858e0c138068f38583952768317fad", "path": "../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9824e3f2e3f25f8318f4114c318f311f99", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981645ae3f72365292963848976d671432", "name": "webview_flutter_wkwebview", "path": "webview_flutter_wkwebview", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98677a30cf10860c7b1ca60611ac579575", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98961ae5215995bb34797eb79857562ad2", "name": "webview_flutter_wkwebview", "path": "webview_flutter_wkwebview", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c40cb276f500083a884591419b490fdb", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ca8125f7c4651b9797e752e8d46f7526", "name": "webview_flutter_wkwebview", "path": "webview_flutter_wkwebview", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980dfc5f313c59255b905634f2dcaebf7e", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b9cee3a3e997f2398f4023f82472a3d2", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987cf941e5ac6942fd6f38a7be73ecf432", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d422f1c55b3bf3e758c62813cdd76c6e", "name": "super_up_admin", "path": "super_up_admin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9888557538700c8858b62ddc7f3e640c59", "name": "apps", "path": "apps", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f7c56213bf1c78541d56b1ea3d57e05e", "name": "flutter", "path": "flutter", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9846746287ecb6c1bbb4a6bbe6cda184f9", "name": "source2", "path": "source2", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9844a2949528d6ef13b74c79e751871f5d", "name": "Desktop", "path": "Desktop", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98bea025ea2776f977a10aec07f3c75e88", "name": "..", "path": ".", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e980917917eabb23854d63a3bd890ba08d6", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/FLTWebViewFlutterPlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98c2cfa5cf9ec420b70399764e87f7297b", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/FWFDataConverters.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98daf861cc49221b631ae9a216c66b1024", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/FWFGeneratedWebKitApis.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9887e032c16df4450182a68bebcb801270", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/FWFHTTPCookieStoreHostApi.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98a78f1bb5c6720e745dd215727be<PERSON>bad", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/FWFInstanceManager.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9802f4577f86d7289ade66a2fde78ffc61", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/FWFNavigationDelegateHostApi.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98bc578da39232921f1ebdcc642a7129c5", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/FWFObjectHostApi.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9821954d62eb4268e8181ad8b1df07d4e5", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/FWFPreferencesHostApi.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e985f392c2bb80ef4882ee33b53513c78bd", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/FWFScriptMessageHandlerHostApi.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9834317b453b2fbec321ac8760a14c7477", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/FWFScrollViewDelegateHostApi.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98a780802e71677b07b6b70ccf98d57973", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/FWFScrollViewHostApi.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98e1270ee150959fb4365152febe3a34a4", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/FWFUIDelegateHostApi.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ffc294aabacdc96fe486fbf1d5f1cb8f", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/FWFUIViewHostApi.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b23952f92057c3f9e0e83e52e7645e74", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/FWFURLAuthenticationChallengeHostApi.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9893c890f221056b260645fd09305b167e", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/FWFURLCredentialHostApi.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e989d849944492d2f7a95da5a67820142dc", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/FWFURLHostApi.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e982b0aeea0f7391c53c45b0438db64a28f", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/FWFURLProtectionSpaceHostApi.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98105c3214bfca34a1af9092ce12de1207", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/FWFUserContentControllerHostApi.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9803a8cbed580b6214b7ba7fa11b2b6133", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/FWFWebsiteDataStoreHostApi.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9886db50782343da3caaca9dd3e9085ad9", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/FWFWebViewConfigurationHostApi.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98a24402f78a011c4cc8f75246b3291702", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/FWFWebViewFlutterWKWebViewExternalAPI.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98fc7d01f04bf5f44b25afa14dff779aec", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/FWFWebViewHostApi.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9852b3cf89cef22f71b8cbccf90cc1de4b", "path": "../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/include/webview-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c99900d4af8dfdc2084faa0f40c9d8d9", "path": "../../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/include/webview_flutter_wkwebview/FLTWebViewFlutterPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9892dbd39438e307cb66dc8b599cd0b8ba", "path": "../../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/include/webview_flutter_wkwebview/FWFDataConverters.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985282e8cfc9bc6781f39792157652d734", "path": "../../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/include/webview_flutter_wkwebview/FWFGeneratedWebKitApis.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986d291e934bd26ae1f1dcbb7029f505c1", "path": "../../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/include/webview_flutter_wkwebview/FWFHTTPCookieStoreHostApi.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9884ed99866d416a464d6d48b182d8cb55", "path": "../../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/include/webview_flutter_wkwebview/FWFInstanceManager.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985e817027e7bd09991b0d3ffa27b3071e", "path": "../../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/include/webview_flutter_wkwebview/FWFInstanceManager_Test.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9831e232d2754f2a5ea17ffc0bf89395aa", "path": "../../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/include/webview_flutter_wkwebview/FWFNavigationDelegateHostApi.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b1c8751d24eb2e9869bad13aa927e8bc", "path": "../../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/include/webview_flutter_wkwebview/FWFObjectHostApi.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98fb5b4a6347ec73e75980c6518476a21e", "path": "../../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/include/webview_flutter_wkwebview/FWFPreferencesHostApi.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982146710c022d8d75e5a2f24dd0b84194", "path": "../../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/include/webview_flutter_wkwebview/FWFScriptMessageHandlerHostApi.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989e33526ca5bf11c19fa2dabd9cc1afdd", "path": "../../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/include/webview_flutter_wkwebview/FWFScrollViewDelegateHostApi.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98656e72474a6f2c89ec80054ac9f6b72d", "path": "../../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/include/webview_flutter_wkwebview/FWFScrollViewHostApi.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9890054a1acc04ba660158e5bb29142b74", "path": "../../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/include/webview_flutter_wkwebview/FWFUIDelegateHostApi.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9842adc67f8b5e11c0e90d80bf4b72dca7", "path": "../../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/include/webview_flutter_wkwebview/FWFUIViewHostApi.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98da74c3f7b14cf5722ca119d47af9cd9b", "path": "../../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/include/webview_flutter_wkwebview/FWFURLAuthenticationChallengeHostApi.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98bad5a9325809c8b34e8fddcbfce6b21a", "path": "../../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/include/webview_flutter_wkwebview/FWFURLCredentialHostApi.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ec7c607646f65770284d839a67789060", "path": "../../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/include/webview_flutter_wkwebview/FWFURLHostApi.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98978a6825c0c0f90863be3584b61ddbf7", "path": "../../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/include/webview_flutter_wkwebview/FWFURLProtectionSpaceHostApi.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98da69663f4871ae20c80f3b028cf036b9", "path": "../../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/include/webview_flutter_wkwebview/FWFUserContentControllerHostApi.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98fbce6e90feebc824196f8170a6e75d4e", "path": "../../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/include/webview_flutter_wkwebview/FWFWebsiteDataStoreHostApi.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988f2ce56f78a212f1a6b0794ffdf00cb4", "path": "../../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/include/webview_flutter_wkwebview/FWFWebViewConfigurationHostApi.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989a6f9b8d7d2fe76fdf674f0c7d058dc2", "path": "../../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/include/webview_flutter_wkwebview/FWFWebViewFlutterWKWebViewExternalAPI.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98fe15e5506db4a4b1baef33aba888f640", "path": "../../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/include/webview_flutter_wkwebview/FWFWebViewHostApi.h", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e986cb5f1db78a5d4053864e794dadd4234", "name": "webview_flutter_wkwebview", "path": "webview_flutter_wkwebview", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98874141e7e364fc4fc0af0ec13c553afd", "name": "include", "path": "include", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988acca3d3bb03969e552f1219a258f375", "name": "webview_flutter_wkwebview", "path": "webview_flutter_wkwebview", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98494e619a83787963cf1d5dc7a586ef1a", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983616920ee58ca345c74e7a50a6643017", "name": "webview_flutter_wkwebview", "path": "webview_flutter_wkwebview", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f86777da40004fd0013095174fb380db", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985abf254a7a29b1501b04279875f2bca4", "name": "webview_flutter_wkwebview", "path": "webview_flutter_wkwebview", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f74dbe7cdc1d8be4c64faf37670ffdf8", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98599f90298c8baae344c4fcc57772566d", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980765f9a3103625e60e3e7879fddb1a66", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985d84453af25912cbe71c1ee1da929568", "name": "super_up_admin", "path": "super_up_admin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98646b947d447c674edc4c8fcba2c7eac0", "name": "apps", "path": "apps", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9837a759b98ddfa9805eb0a8e7d56ab6fe", "name": "flutter", "path": "flutter", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982fa32e7679ab421811d73b9be2165062", "name": "source2", "path": "source2", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e6b05d7af8bd7eae4407598f47c5aa2d", "name": "Desktop", "path": "Desktop", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9872ee6fc70bb9087cd5644a5c7583b9e6", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983dd6e21581ca8dd171622b3a9688fa76", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ccb8583cf3b1d42d0669e9c56f646e4e", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b623c2f3b5a5ebb1b2d0abc92d7b7395", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987a58a3e7fd86a965e7826589093989a2", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ecfbdcb17ced298f026cb6ce0526a2d0", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980ff04ffb67bec8365769758f3a48ef8f", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d1e45817b0696c52612a6171ef6b1f9f", "name": "..", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.0/darwin/webview_flutter_wkwebview/Sources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e985c2429930b0c440b36e5ebe57fd24f1d", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/include/FlutterWebView.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98f9c81f40c294d2480b05972dcd7f3b35", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.0/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e984972f285cd4496779d717e0408daa299", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.0/darwin/webview_flutter_wkwebview.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e988ecb1f9e1dc00b8ebcf93947646f7c79", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98ffbb547dc1496f9664c5e1c6a178f85a", "path": "ResourceBundle-webview_flutter_wkwebview_privacy-webview_flutter_wkwebview-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e987c3f6f97d91ab97ff589c9a8b3c7b332", "path": "webview_flutter_wkwebview.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e983acbec55909cd22acd58f15ff4b110b9", "path": "webview_flutter_wkwebview-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e989c1536057d0dd9be88c77c7e1af71999", "path": "webview_flutter_wkwebview-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9856c044f0ccb353fb7c133067f3a639e1", "path": "webview_flutter_wkwebview-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98019775dab6a7cbf6d21ab7b0409d0183", "path": "webview_flutter_wkwebview.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e988c50ab2cd4ebcec00cc6f5661e9e7842", "path": "webview_flutter_wkwebview.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98ce43a9e55a46f9e2fe607114af169a6d", "name": "Support Files", "path": "../../../../Pods/Target Support Files/webview_flutter_wkwebview", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98538888e94e67b2e3ab3532f17fa677be", "name": "webview_flutter_wkwebview", "path": "../.symlinks/plugins/webview_flutter_wkwebview/darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b0b104f62fcff7cb46b59d1c833bad54", "name": "Development Pods", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "wrapper.framework", "guid": "bfdfe7dc352907fc980b868725387e9807daa3f9265322026266a25d0045b5bb", "path": "Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/AVFoundation.framework", "sourceTree": "DEVELOPER_DIR", "type": "file"}, {"fileType": "wrapper.framework", "guid": "bfdfe7dc352907fc980b868725387e98d7e2dd08eff3fdc559a3f5fed1d9957c", "path": "Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/AVKit.framework", "sourceTree": "DEVELOPER_DIR", "type": "file"}, {"fileType": "wrapper.framework", "guid": "bfdfe7dc352907fc980b868725387e987b9e1c3b6563a543b6fbe5f0759aef8f", "path": "Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/Foundation.framework", "sourceTree": "DEVELOPER_DIR", "type": "file"}, {"fileType": "wrapper.framework", "guid": "bfdfe7dc352907fc980b868725387e9884489dd7a344f146541488ed3c691894", "path": "Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/ImageIO.framework", "sourceTree": "DEVELOPER_DIR", "type": "file"}, {"fileType": "wrapper.framework", "guid": "bfdfe7dc352907fc980b868725387e9812cc64573a371c51e6cdc1c4fd688b8c", "path": "Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/Photos.framework", "sourceTree": "DEVELOPER_DIR", "type": "file"}, {"fileType": "wrapper.framework", "guid": "bfdfe7dc352907fc980b868725387e98e4312a17bfed5f03cd67f84d23bdd541", "path": "Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/PhotosUI.framework", "sourceTree": "DEVELOPER_DIR", "type": "file"}, {"fileType": "wrapper.framework", "guid": "bfdfe7dc352907fc980b868725387e989d11e2d400847835a96dea825dfa8835", "path": "Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/UIKit.framework", "sourceTree": "DEVELOPER_DIR", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98fa56320d8a9b34b0484b7104e1ad68f0", "name": "iOS", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9818a9377e250b9d121296a9203d9c1d90", "name": "Frameworks", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98d2e8341bccd923a9c9d0fd9f2f634900", "path": "Sources/CryptoSwift/CS_BigInt/Addition.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9873626ca1d7db792bd72f32bf56bddb99", "path": "Sources/CryptoSwift/AEAD/AEAD.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e985288ab910931c9c667ede70121a4d3fe", "path": "Sources/CryptoSwift/AEAD/AEADChaCha20Poly1305.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98587ab8fa1b29225290138156d47a41ef", "path": "Sources/CryptoSwift/AEAD/AEADXChaCha20Poly1305.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98c2bb4899b1fb62c492acca0886d75a3a", "path": "Sources/CryptoSwift/AES.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e987db43e0892fe795cdadc6bc91b4cb5e9", "path": "Sources/CryptoSwift/Foundation/AES+Foundation.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e983f3b8183b11ef1ff4cd0e62922888c9a", "path": "Sources/CryptoSwift/AES.Cryptors.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98c2051048df0f82716f4db093332fbd0d", "path": "Sources/CryptoSwift/Array+Extension.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98f51579b4952615a222521961069a0636", "path": "Sources/CryptoSwift/Foundation/Array+Foundation.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98b67a37835b3e33130634494cf59019be", "path": "Sources/CryptoSwift/ASN1/ASN1.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9889d762487bcbbe9059cbaaa75d7fc661", "path": "Sources/CryptoSwift/ASN1/ASN1Decoder.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98da6077e6ecbcc3629a2a8c123f20615f", "path": "Sources/CryptoSwift/ASN1/ASN1Encoder.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98c202d37c2b7cec25920cd66f2901bdf5", "path": "Sources/CryptoSwift/ASN1/ASN1Scanner.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e981ee1ac6ee33d17fb29cc0c0abed7ef98", "path": "Sources/CryptoSwift/Authenticator.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e987114cd276701604326a570147b2d1383", "path": "Sources/CryptoSwift/BatchedCollection.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98dbf8dc85d46c557ae936178f1557dc93", "path": "Sources/CryptoSwift/CS_BigInt/BigInt.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98b5b2d92b18651f467d1e95e3d2e9c7bb", "path": "Sources/CryptoSwift/CS_BigInt/BigUInt.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98a5580e0f31b7043850afd1aa6310696e", "path": "Sources/CryptoSwift/Bit.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98fd3092fbc0bab3f497f56359f71ab3ab", "path": "Sources/CryptoSwift/CS_BigInt/BitwiseOps.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9869c02585b75fcc793652c297794a1553", "path": "Sources/CryptoSwift/BlockCipher.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98355c08379a6d5f11ed70b27313b868a4", "path": "Sources/CryptoSwift/BlockDecryptor.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98c85f72028966688eec51e08828fce105", "path": "Sources/CryptoSwift/BlockEncryptor.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98835f6806afa839f5126162d59b246882", "path": "Sources/CryptoSwift/BlockMode/BlockMode.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98018da1dd742b3d4f9123fdc47d9c19a7", "path": "Sources/CryptoSwift/BlockMode/BlockModeOptions.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9872da7930e87b6656ba502e839811dde8", "path": "Sources/CryptoSwift/Blowfish.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98c8a01885d46c7786c2fc07c4cb3e47a9", "path": "Sources/CryptoSwift/Foundation/Blowfish+Foundation.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e980774fac549c49c9fe1ab64e6b6bdc02a", "path": "Sources/CryptoSwift/BlockMode/CBC.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9852802ac0de410868bb2c1484ea3b9bf8", "path": "Sources/CryptoSwift/CBCMAC.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98571678cb691f0936f385869ff7ea9b8d", "path": "Sources/CryptoSwift/BlockMode/CCM.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98d4aa3557ae39ace5b32544872767aab0", "path": "Sources/CryptoSwift/BlockMode/CFB.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98ffea9fabccfc0f8df418bc7b74586026", "path": "Sources/CryptoSwift/ChaCha20.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e988228cf9477d362224495e6b8a39711c2", "path": "Sources/CryptoSwift/Foundation/ChaCha20+Foundation.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9870f6a2606110b6b216c57586a01c3d48", "path": "Sources/CryptoSwift/Checksum.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98a5870b91d70601611dd3297fb8b10ff6", "path": "Sources/CryptoSwift/Cipher.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9832edbb5b809dbb4b5dce34a61c1fff3d", "path": "Sources/CryptoSwift/BlockMode/CipherModeWorker.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9820db85671147fabf49e5b994931c3729", "path": "Sources/CryptoSwift/CMAC.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e985580e2629b7867f1a7ce40b32e4a73bf", "path": "Sources/CryptoSwift/CS_BigInt/Codable.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e980f5200b05fe4301f40a70398e1ffc1be", "path": "Sources/CryptoSwift/Collection+Extension.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98bf4fb629d9630f2fbd3b8be296db0c57", "path": "Sources/CryptoSwift/CompactMap.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e987e236ebaa4530e30045ee17a32dca984", "path": "Sources/CryptoSwift/CS_BigInt/Comparable.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e985b7b4da2d5181c4600d8cda75ba2de8b", "path": "Sources/CryptoSwift/Cryptor.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9876a216612a19f9aa46a5bab07d176d13", "path": "Sources/CryptoSwift/Cryptors.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98588504779e2fe1d8ad3a9f0be0d66672", "path": "Sources/CryptoSwift/CS_BigInt/CS.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e980e3c9f2a1d617c9a9635052cae7a814d", "path": "Sources/CryptoSwift/BlockMode/CTR.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98d1591dda31283ec368dd760c619eb8c4", "path": "Sources/CryptoSwift/Foundation/Data+Extension.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98b85eae6508099b376c48dbf3ba8a7c8b", "path": "Sources/CryptoSwift/CS_BigInt/DataConversion.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e985cfb0e5eec7672ddb0faa9fb62df1dcd", "path": "Sources/CryptoSwift/PEM/DER.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98e5e1f5ad7a4998550413111646dec930", "path": "Sources/CryptoSwift/Digest.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9811e7ec8af5f6082910c0cc6c1318ec8a", "path": "Sources/CryptoSwift/DigestType.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9844af19c7984d5d08f48e8279cafffb53", "path": "Sources/CryptoSwift/CS_BigInt/Division.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e988019da1964ace2332ab2c6315710b4f1", "path": "Sources/CryptoSwift/BlockMode/ECB.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e983601571671502130dc90d8685c71103c", "path": "Sources/CryptoSwift/CS_BigInt/Exponentiation.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e982f8b3556b7565e0d2a8d29a0c9f3e1c3", "path": "Sources/CryptoSwift/CS_BigInt/FloatingPointConversion.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98446bb3ef9f4f5f603b9768bb4c67692e", "path": "Sources/CryptoSwift/CS_BigInt/GCD.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9828502a48d332f3d29279d4ce45864c1b", "path": "Sources/CryptoSwift/BlockMode/GCM.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9819502de4f4b5cb124536438a0fbeefbb", "path": "Sources/CryptoSwift/Generics.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98ec226d20dd1df7a9221b223e72e735f1", "path": "Sources/CryptoSwift/CS_BigInt/Hashable.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98abe6a685ed2910490d2dca85ddaaa08a", "path": "Sources/CryptoSwift/HKDF.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e987f2da8b792497fbcdf9621a336434c38", "path": "Sources/CryptoSwift/HMAC.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e981b8637ed48739c2ced8c69e5e11542bb", "path": "Sources/CryptoSwift/Foundation/HMAC+Foundation.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98b0d7269b1470bed499b8abbbb1920b6b", "path": "Sources/CryptoSwift/Int+Extension.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98e0407735ca95d0038cd2e008cf64517f", "path": "Sources/CryptoSwift/CS_BigInt/IntegerConversion.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98d0865df3be4be23944a38b5c5e66c66a", "path": "Sources/CryptoSwift/ISO10126Padding.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98a2984701de4d4cb207e239abad0cc0b6", "path": "Sources/CryptoSwift/ISO78164Padding.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98d3679ae6d8a91b7fb437b0afdafc1696", "path": "Sources/CryptoSwift/MD5.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e981841708938ed1cc6bc58d1409c75b3a5", "path": "Sources/CryptoSwift/CS_BigInt/Multiplication.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98234e0de4a5b32cc8ca136b1acd839b21", "path": "Sources/CryptoSwift/NoPadding.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e984a24286e5f1921ddd28234e72ebad69d", "path": "Sources/CryptoSwift/BlockMode/OCB.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9842eabdcb1068c82f78f72fb6c9e7b4d6", "path": "Sources/CryptoSwift/BlockMode/OFB.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98b4e1b60495fb47c95b49b8e03b896e66", "path": "Sources/CryptoSwift/Operators.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e987c6ef087cd260242abba3d5ae6b687ca", "path": "Sources/CryptoSwift/Padding.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98adde406d4b7e929d01cc1d7e3e765004", "path": "Sources/CryptoSwift/PKCS/PBKDF1.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98398fc34b69a8ea00cea8c8c3bdcc16ac", "path": "Sources/CryptoSwift/PKCS/PBKDF2.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9867716073458f7173d3afc35ec1c319e6", "path": "Sources/CryptoSwift/BlockMode/PCBC.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9874279f4ccd8fc672180f7c2feae0126c", "path": "Sources/CryptoSwift/PKCS/PKCS1v15.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e989b1b15f6ecdaedfb98dc20049b2a4ea7", "path": "Sources/CryptoSwift/PKCS/PKCS5.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98981a9abd4ada47a0c82d6c42578bf7ba", "path": "Sources/CryptoSwift/PKCS/PKCS7.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9854417317d76d3af109cb82b8bf819d4e", "path": "Sources/CryptoSwift/PKCS/PKCS7Padding.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e980a0bad50f9289cf94e5150036eef71ee", "path": "Sources/CryptoSwift/Poly1305.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98c519c96358a4e7d5a2c0a9179e034837", "path": "Sources/CryptoSwift/CS_BigInt/PrimeTest.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98a7b5876a1749c27a3508e04f87e7b72c", "path": "Sources/CryptoSwift/Rabbit.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98fb9e4e05ae5ef3a24a452bd4bc347339", "path": "Sources/CryptoSwift/Foundation/Rabbit+Foundation.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98347a92b23042792fa8d1766ddd6c1b8b", "path": "Sources/CryptoSwift/CS_BigInt/Random.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e980b971fe5570a8c529aed506eea94d485", "path": "Sources/CryptoSwift/RSA/RSA.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98a55057ca6cf873b59811c9e335ebc6ea", "path": "Sources/CryptoSwift/RSA/RSA+Cipher.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98381b0a64a635373ed98874a09f71e6ee", "path": "Sources/CryptoSwift/RSA/RSA+Signature.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9860f88c69ac5193244f61e8889f426aab", "path": "Sources/CryptoSwift/Scrypt.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98ab7cec924c8cadaea2fdde72ff31b754", "path": "Sources/CryptoSwift/SecureBytes.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9836b4974de7852e66ea00a344c0411810", "path": "Sources/CryptoSwift/SHA1.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98d377f590ec8c5191d700f5c8070f669f", "path": "Sources/CryptoSwift/SHA2.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98678575ba98af668cf6618736c5f96510", "path": "Sources/CryptoSwift/SHA3.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e986401ef34f2a460f06109f85072fba9bf", "path": "Sources/CryptoSwift/CS_BigInt/Shifts.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98c5caf0968174d2d032859f3d9bce451a", "path": "Sources/CryptoSwift/Signature.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98830268e0897a6ed4baae288d501b7a62", "path": "Sources/CryptoSwift/CS_BigInt/SquareRoot.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98aaeb219ef1450d433d5b28765bee5104", "path": "Sources/CryptoSwift/StreamDecryptor.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e981f6d99d9d7114dfddbc407a34b751ce5", "path": "Sources/CryptoSwift/StreamEncryptor.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98174b8e88ba8f7bf22c2c79389178529a", "path": "Sources/CryptoSwift/CS_BigInt/Strideable.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98ce01eddc219171115ba7d76a504800ab", "path": "Sources/CryptoSwift/String+Extension.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98afce499e4e3683714086df35f6d8a72a", "path": "Sources/CryptoSwift/Foundation/String+FoundationExtension.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9842b521995599c7cf12688e2714f18bdf", "path": "Sources/CryptoSwift/CS_BigInt/StringConversion.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98b7a532999d6bf9e9839de812cd20c8f7", "path": "Sources/CryptoSwift/CS_BigInt/Subtraction.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98bf902b4cadab455bd109491f48ffea04", "path": "Sources/CryptoSwift/UInt128.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98cc42ef65986ff91bc1a4f46f33e65a82", "path": "Sources/CryptoSwift/UInt16+Extension.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e988e28d43dfe94a9c08642daab1c207f73", "path": "Sources/CryptoSwift/UInt32+Extension.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98f909a8c3f395877676f0580587d106ce", "path": "Sources/CryptoSwift/UInt64+Extension.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e989893468c913caef889b0a32527973a6d", "path": "Sources/CryptoSwift/UInt8+Extension.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98db1272537a243467fa499807f7352096", "path": "Sources/CryptoSwift/Updatable.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e980fadef6a7beefb18c3dd6a91a74ebb66", "path": "Sources/CryptoSwift/Utils.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9802bf59ee27a73e4231684ec9fea2a132", "path": "Sources/CryptoSwift/Foundation/Utils+Foundation.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e987a64ab7cb2fe93778d55fa3632bcc8e2", "path": "Sources/CryptoSwift/CS_BigInt/WordsAndBits.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98b0eeabd7cbe3e49d1298dc1d77bd66f9", "path": "Sources/CryptoSwift/XChaCha20.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9894078cbad2e6b74efc19f46d30bad332", "path": "Sources/CryptoSwift/Foundation/XChaCha20+Foundation.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9869c96ff27e92836c7c68ca85776ee7b3", "path": "Sources/CryptoSwift/ZeroPadding.swift", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98d3b8bc693717a677f71b5c2b23c94c47", "path": "Sources/CryptoSwift/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98516fa27403461d9a795e23619e05fc24", "name": "Resources", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e989715df749acd89c0793a74b9a8dcc512", "path": "CryptoSwift.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9893812d4a35e7911bb310f96cadd2544f", "path": "CryptoSwift-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9876143767a91889006342982cb20b003f", "path": "CryptoSwift-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98fa0cdcb8868415ec1b968a9abb480be2", "path": "CryptoSwift-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989b90ddca7d89044bbed7141eb3b39034", "path": "CryptoSwift-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98375cd7b152354ea3a4e9b5edc53d6e17", "path": "CryptoSwift.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98a8eea0be4019c2db671e7ccc2e790e68", "path": "CryptoSwift.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98dd2092e2d719217b04d0ed9f18c59c4a", "path": "ResourceBundle-CryptoSwift-CryptoSwift-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9862491bd50fc1c499561e6d34c0ed5cf5", "name": "Support Files", "path": "../Target Support Files/CryptoSwift", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98371ad0ec44242e3293c24c936109fbcd", "name": "CryptoSwift", "path": "CryptoSwift", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98a82faf25a177f166d2308c3bca0cc15e", "path": "Sources/DKImagePickerController/View/Cell/DKAssetGroupCellItemProtocol.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98a290dbb85ab952e3712b6b4588cb4f9c", "path": "Sources/DKImagePickerController/View/Cell/DKAssetGroupDetailBaseCell.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98e1c63fd8192e8b6ed5eb482fffee7859", "path": "Sources/DKImagePickerController/View/Cell/DKAssetGroupDetailCameraCell.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e981b2a71b04f927b8dfb17f7fa2a94ca61", "path": "Sources/DKImagePickerController/View/Cell/DKAssetGroupDetailImageCell.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98fe06819b105414666129d55307b81e7a", "path": "Sources/DKImagePickerController/View/DKAssetGroupDetailVC.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98782ddec98ff792f417bd1c281ea88fc7", "path": "Sources/DKImagePickerController/View/Cell/DKAssetGroupDetailVideoCell.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e983c09bcc9c6cf13b6c3b55449c53c3ca4", "path": "Sources/DKImagePickerController/View/DKAssetGroupGridLayout.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98680757dbaf600859e7b4511bf025f3b7", "path": "Sources/DKImagePickerController/View/DKAssetGroupListVC.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98e169b4a70053c1b1cce9164bef32d552", "path": "Sources/DKImagePickerController/DKImageAssetExporter.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98dc4cf0993323dfbba6f1ae6218a30411", "path": "Sources/DKImagePickerController/DKImageExtensionController.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98ee156588d570001afe4f7b03c0145332", "path": "Sources/DKImagePickerController/DKImagePickerController.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98e2213d5dd9a4003ade0d03df408ca0d4", "path": "Sources/DKImagePickerController/DKImagePickerControllerBaseUIDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98e1678320b14347ca57aef0235b3d9d37", "path": "Sources/DKImagePickerController/View/DKPermissionView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9811181119f9e460ea01b650f5c4465eac", "path": "Sources/DKImagePickerController/DKPopoverViewController.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e983c57cb9316b89a27177303a39bbdd02e", "name": "Core", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98619e30572a4ccc9821a8de3c28d94a6a", "path": "Sources/DKImageDataManager/Model/DKAsset.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e985b10165f0cdbf958810c54c556857e36", "path": "Sources/DKImageDataManager/Model/DKAsset+Export.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98bb1408e9a46c1e6f6c65326e4d90fc44", "path": "Sources/DKImageDataManager/Model/DKAsset+Fetch.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98bc49ff6a0190e24081e08c4f89b36932", "path": "Sources/DKImageDataManager/Model/DKAssetGroup.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98b84b04f265ddcc10a6be14e81b2e3569", "path": "Sources/DKImageDataManager/DKImageBaseManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98b9a5ebfb94e4b6232fbd3b2e50a72bc0", "path": "Sources/DKImageDataManager/DKImageDataManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e981cf2ed403fa1c6c7ad0cc1f03b5afc3d", "path": "Sources/DKImageDataManager/DKImageGroupDataManager.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e980554df295872f1808ad2c053ed1ff0ca", "name": "ImageDataManager", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98a3fd39ba22b91a8560b33803167d0e81", "path": "Sources/Extensions/DKImageExtensionGallery.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e984a8d586f0e362b9f442016000b5c0560", "name": "PhotoGallery", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e985eccefeb8cf91923ba0ebca37b29facf", "path": "Sources/DKImagePickerController/Resource/DKImagePickerControllerResource.swift", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e9803504529b1d7a0a1b20c299fea3b7059", "path": "Sources/DKImagePickerController/Resource/Resources/ar.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e986727ea14cc13b7531bab7fa9c7af0946", "path": "Sources/DKImagePickerController/Resource/Resources/Base.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e9894cf9ca724075cf387d5398cc025a5d2", "path": "Sources/DKImagePickerController/Resource/Resources/da.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e9810829eb70ddc7152194b0af63b813dbc", "path": "Sources/DKImagePickerController/Resource/Resources/de.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e98615f85bdb260151e60e0c5596dfc3c84", "path": "Sources/DKImagePickerController/Resource/Resources/en.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e9879fbc671b49bc0939f33f99e055ae15e", "path": "Sources/DKImagePickerController/Resource/Resources/es.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e987b1dbb7263bf4bf0a54365d80a38b57a", "path": "Sources/DKImagePickerController/Resource/Resources/fr.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e98c05da326d966d4ac2e2d29f9931794da", "path": "Sources/DKImagePickerController/Resource/Resources/hu.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder.assetcatalog", "guid": "bfdfe7dc352907fc980b868725387e98cbd0bba593cb75ba7eeab1f88a2f4533", "path": "Sources/DKImagePickerController/Resource/Resources/Images.xcassets", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e98e42224b1bb1b9015ccdeaf360de002f9", "path": "Sources/DKImagePickerController/Resource/Resources/it.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e98a80efda97433915d3efdaf3f2792825e", "path": "Sources/DKImagePickerController/Resource/Resources/ja.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e986218c9a4927d18c71c4260577c3e0f05", "path": "Sources/DKImagePickerController/Resource/Resources/ko.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e9862bc79353145ecc797b8fce7fd090d20", "path": "Sources/DKImagePickerController/Resource/Resources/nb-NO.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e98a9b007d8d184cdacfae29729aa6436e0", "path": "Sources/DKImagePickerController/Resource/Resources/nl.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e9853e1d269bb51d2703351871f3f292022", "path": "Sources/DKImagePickerController/Resource/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e98984085a135172b0a98067cf8bb813529", "path": "Sources/DKImagePickerController/Resource/Resources/pt_BR.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e98ffdc6c68b78f6f9e3b27d7712f2b64e4", "path": "Sources/DKImagePickerController/Resource/Resources/ru.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e9841a230320ff21a47500bb8800da435fd", "path": "Sources/DKImagePickerController/Resource/Resources/tr.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e98cbf91af7c3996f6ce6773ef990089818", "path": "Sources/DKImagePickerController/Resource/Resources/ur.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e98f36dfd4c3cb60cf0310ec0d27a9eb9d7", "path": "Sources/DKImagePickerController/Resource/Resources/vi.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e98bfee4931e90f79863956014df595a5d1", "path": "Sources/DKImagePickerController/Resource/Resources/zh-Hans.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e984135bc73a067fc50661fd6629f09552f", "path": "Sources/DKImagePickerController/Resource/Resources/zh-Hant.lproj", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98a399bc0c80e699edbd3c85a382d0c386", "name": "Resources", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985807d45b29181bba245ce980a002b5f5", "name": "Resource", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e9854af31982983fbb8f641054235d04df9", "path": "DKImagePickerController.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e986be2e6105857193e2919fd446680e403", "path": "DKImagePickerController-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e984e4f5191b33f76bdd80b757f1dcb08bb", "path": "DKImagePickerController-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9809b85be58a84bf7b2bd05c8add50ff8f", "path": "DKImagePickerController-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98fe2eb753aa2b12abbb6f27f3af0b83a3", "path": "DKImagePickerController-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98e31842ca4e4884354b8264c89bd84755", "path": "DKImagePickerController.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98389f74c7c2b371987edcc1324814663a", "path": "DKImagePickerController.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98988aff097672c584a6a44f631df45e2b", "path": "ResourceBundle-DKImagePickerController-DKImagePickerController-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98939f003a368f1af9a9fd9591b3b5efe1", "name": "Support Files", "path": "../Target Support Files/DKImagePickerController", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98abdac1376a82df8ad472ea6f19ee2c86", "name": "DKImagePickerController", "path": "DKImagePickerController", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e983b367a280097480e9ab884d6c3e57892", "path": "DKPhotoGallery/DKPhotoGallery.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98e67af78675df0ae442b208a635e91a25", "path": "DKPhotoGallery/DKPhotoGalleryContentVC.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98c2899dbb35caf7cc02c837ab0a6fedd1", "path": "DKPhotoGallery/Transition/DKPhotoGalleryInteractiveTransition.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98e901ae46e0035d119202ec7e0da2f212", "path": "DKPhotoGallery/DKPhotoGalleryScrollView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98f316ab953a7d70e2864d29558ec1e8bb", "path": "DKPhotoGallery/Transition/DKPhotoGalleryTransitionController.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98902ec8fd685629d96280967080a71a7a", "path": "DKPhotoGallery/Transition/DKPhotoGalleryTransitionDismiss.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98b9c17a2eab1ad8b8b5b25ad4252219be", "path": "DKPhotoGallery/Transition/DKPhotoGalleryTransitionPresent.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98bc036c912b9cebe73cc22df786d0883c", "path": "DKPhotoGallery/DKPhotoIncrementalIndicator.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9852e2cbcb71dda2e4ec3b55257e14d608", "path": "DKPhotoGallery/DKPhotoPreviewFactory.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e984f1707e75b10cf321f5072efef4813e3", "name": "Core", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9844d62d0930bb6d8f71b8ba9a9aa0c2d9", "path": "DKPhotoGallery/DKPhotoGalleryItem.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e988018b479902edc6e92aff7271fccbcf1", "name": "Model", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9843f5887c8626864e9238c93f1aee7311", "path": "DKPhotoGallery/Preview/PDFPreview/DKPDFView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e986e0563cf9b2220ad7b833271a1a5bc5d", "path": "DKPhotoGallery/Preview/ImagePreview/DKPhotoBaseImagePreviewVC.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9891dc9f9fc0e1d02859e7ad62cbb47693", "path": "DKPhotoGallery/Preview/DKPhotoBasePreviewVC.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98fdf4b431752bd0494b16258819c65915", "path": "DKPhotoGallery/Preview/DKPhotoContentAnimationView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9810ebb1978f26bce155b57c02c14f6e43", "path": "DKPhotoGallery/Preview/ImagePreview/DKPhotoImageDownloader.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98f33a1b753ba65aac293a1248fac5f83a", "path": "DKPhotoGallery/Preview/ImagePreview/DKPhotoImagePreviewVC.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98e1fa4ab68bd18c3914da059d75ca497a", "path": "DKPhotoGallery/Preview/ImagePreview/DKPhotoImageUtility.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98aa1ea858e5a53298e2cb68c0371cd0a7", "path": "DKPhotoGallery/Preview/ImagePreview/DKPhotoImageView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e987f7a1c5cd5c3054b72295719f4ccc5e9", "path": "DKPhotoGallery/Preview/PDFPreview/DKPhotoPDFPreviewVC.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e984faeba857b9036a5d66238b5ad86639b", "path": "DKPhotoGallery/Preview/PlayerPreview/DKPhotoPlayerPreviewVC.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e987265dcad7624456ee921763f8b03c3c3", "path": "DKPhotoGallery/Preview/DKPhotoProgressIndicator.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98ef6739a25b8df8918749c25af4377b38", "path": "DKPhotoGallery/Preview/DKPhotoProgressIndicatorProtocol.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98b69db237e2ae875caa926e00fc5b2662", "path": "DKPhotoGallery/Preview/QRCode/DKPhotoQRCodeResultVC.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9810e1515e0cd31dd5d16772ecd55d5775", "path": "DKPhotoGallery/Preview/QRCode/DKPhotoWebVC.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98819f6fe7922a733a880f1f91579c88bb", "path": "DKPhotoGallery/Preview/PlayerPreview/DKPlayerView.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e989876bd23058e749627e0265340f4834d", "name": "Preview", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98f6ff2497de281044bd322872e230eeec", "path": "DKPhotoGallery/Resource/DKPhotoGalleryResource.swift", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e98b3f89c9218058dc2907b88ce1ba80584", "path": "DKPhotoGallery/Resource/Resources/Base.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e987b6d585ddc461e5a9e2fe266635b7ad4", "path": "DKPhotoGallery/Resource/Resources/en.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder.assetcatalog", "guid": "bfdfe7dc352907fc980b868725387e9872a15ac2eed60b15e32da0a58cb00ed0", "path": "DKPhotoGallery/Resource/Resources/Images.xcassets", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e9886356bc5b3512eb9abaf8e880b4c23f1", "path": "DKPhotoGallery/Resource/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e98147090153bf744519c3ac3b1a707a3df", "path": "DKPhotoGallery/Resource/Resources/zh-Hans.lproj", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e986b4c2f578579a2ecb7c086dd649998bc", "name": "Resources", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98020b08819295a42266c811d8fb5e02ee", "name": "Resource", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98a189b5129935f1bd5ac0ff2c76a1ecd5", "path": "DKPhotoGallery.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ab57399c5f81b2b099ab6abdc8037f9d", "path": "DKPhotoGallery-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98f9a66a8090fb44cfc8c5c86eaf0d8891", "path": "DKPhotoGallery-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9896c99ebbbf3b6d3a0dce90a14b5385e9", "path": "DKPhotoGallery-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9897b9de4da8a462806947faf56ff71efe", "path": "DKPhotoGallery-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98a5d2ac47362ca55ad606637695bcd401", "path": "DKPhotoGallery.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98e117f0feecd76786d1fa50d7caf0c7fc", "path": "DKPhotoGallery.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e981f71dca2e578488fa12251b2d4413f1b", "path": "ResourceBundle-DKPhotoGallery-DKPhotoGallery-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98fd49db498386bf9deb31834f8b8f51b7", "name": "Support Files", "path": "../Target Support Files/DKPhotoGallery", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98707172e3b94e0d781ce1c62a01f84a16", "name": "DKPhotoGallery", "path": "DKPhotoGallery", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e986da0ecf9b05eccb61c14363093c2b6a5", "path": "Sources/GoogleMobileAdsPlaceholder.swift", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "wrapper.xcframework", "guid": "bfdfe7dc352907fc980b868725387e98fb136c001a7390d608e197fb56f967a2", "path": "Frameworks/GoogleMobileAdsFramework/GoogleMobileAds.xcframework", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9879ddb73b26358257c20dcf8afe518a32", "name": "Frameworks", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e986f61502d26db32ba64d657700c923bfe", "path": "Frameworks/GoogleMobileAdsFramework/GoogleMobileAds.xcframework/ios-arm64/GoogleMobileAds.framework/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98e8f20695887d9a3430d67a61b55333f2", "name": "Resources", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98a9158a62b3f9bc6a08ce80b2383d67f2", "path": "Google-Mobile-Ads-SDK.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98dbba99798377663c99d53ffe267f9c09", "path": "Google-Mobile-Ads-SDK-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e988fca5c5e77ee66c7a75aa7084297b173", "path": "Google-Mobile-Ads-SDK-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e47bd81fea5cbd8fd2b955ec50c9b5aa", "path": "Google-Mobile-Ads-SDK-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980b45abe0c0aceffa7b3f745b0772c85e", "path": "Google-Mobile-Ads-SDK-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.sh", "guid": "bfdfe7dc352907fc980b868725387e980ea8a77cb0887d8b73871e372582ae75", "path": "Google-Mobile-Ads-SDK-xcframeworks.sh", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e987407c2b720750827fd35c2e7253342d5", "path": "Google-Mobile-Ads-SDK.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98e3067e9d45e2e1d0716b8710add45652", "path": "Google-Mobile-Ads-SDK.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98b5b3ab485a15fbf233552627650ad24a", "path": "ResourceBundle-GoogleMobileAdsResources-Google-Mobile-Ads-SDK-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9829925e562c4d246b0f8d167f906581e1", "name": "Support Files", "path": "../Target Support Files/Google-Mobile-Ads-SDK", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980124fb40ed09f7a5e772a36d5bb65b48", "name": "Google-Mobile-Ads-SDK", "path": "Google-Mobile-Ads-SDK", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "wrapper.xcframework", "guid": "bfdfe7dc352907fc980b868725387e98b7314bba833ba7f5db85c168013e594f", "path": "Frameworks/Release/UserMessagingPlatform.xcframework", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98dfce93ba3a45fbef8fa6b0f527c38e4a", "name": "Frameworks", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98a20aebbf1c8a5077f5bcc18ecdb60b40", "path": "Frameworks/Release/UserMessagingPlatform.xcframework/ios-arm64/UserMessagingPlatform.framework/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e984dfcb331960253dd7a3d659515064b40", "name": "Resources", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.sh", "guid": "bfdfe7dc352907fc980b868725387e986b24395e0d44186e3bd14d9c5a7f957d", "path": "GoogleUserMessagingPlatform-xcframeworks.sh", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98c8fc8feb78ea459a79b5125c427401ee", "path": "GoogleUserMessagingPlatform.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98f2e6711f09334e18432045f90c2f2eb7", "path": "GoogleUserMessagingPlatform.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98591f2e4ecd50bb37b558c5955d7a5917", "path": "ResourceBundle-UserMessagingPlatformResources-GoogleUserMessagingPlatform-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e982e7e62a60ebff454d19e83851491b1ad", "name": "Support Files", "path": "../Target Support Files/GoogleUserMessagingPlatform", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a0692280d65ea4511b017e745042a52e", "name": "GoogleUserMessagingPlatform", "path": "GoogleUserMessagingPlatform", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989052dae1c3defd97ecb349bbaf7469f2", "path": "SDWebImage/Private/NSBezierPath+SDRoundedCorners.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b71148084cb00a7c53ca77be023749b7", "path": "SDWebImage/Private/NSBezierPath+SDRoundedCorners.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a2bdf051910081d78affc987711335d2", "path": "SDWebImage/Core/NSButton+WebCache.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b822a1cbf0b7cf078521e0232f0d7e50", "path": "SDWebImage/Core/NSButton+WebCache.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e2e814dc6f1492cf50f431ad1719e5c9", "path": "SDWebImage/Core/NSData+ImageContentType.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9837c4d3af58e3cdb69e86e52f41f68a67", "path": "SDWebImage/Core/NSData+ImageContentType.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9874096081021be259b5d4c9737a9404d1", "path": "SDWebImage/Core/NSImage+Compatibility.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e984e44747907e8b39a5a8faa7a9cdacec6", "path": "SDWebImage/Core/NSImage+Compatibility.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b898d42397dd82fd00a361896dc4a542", "path": "SDWebImage/Core/SDAnimatedImage.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98e8284207eebdc9dde60750f73d9bb6d3", "path": "SDWebImage/Core/SDAnimatedImage.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98415b8bbb7472d45042db12af63b88ca0", "path": "SDWebImage/Core/SDAnimatedImagePlayer.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9837931606be7d3d64c060619e1214dcb0", "path": "SDWebImage/Core/SDAnimatedImagePlayer.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980a212f9f28297a756b226e61b272a31e", "path": "SDWebImage/Core/SDAnimatedImageRep.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9815a97c23e0240839a3f8466ce50807d2", "path": "SDWebImage/Core/SDAnimatedImageRep.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986d1963418f4f4db74cbf4f7ec7513ace", "path": "SDWebImage/Core/SDAnimatedImageView.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e989d2f5a86eac2dc4337e7d441d023d4f3", "path": "SDWebImage/Core/SDAnimatedImageView.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984e715e75ef6d396d0ac0fc5bcb7120f3", "path": "SDWebImage/Core/SDAnimatedImageView+WebCache.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e989844ab4e2734c71740ac493a4a727c1c", "path": "SDWebImage/Core/SDAnimatedImageView+WebCache.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f6ebbc690353b09d71f3c7f1e8a45049", "path": "SDWebImage/Private/SDAssociatedObject.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e981ec877de2b85338de4b88ae08e1d532b", "path": "SDWebImage/Private/SDAssociatedObject.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a5a7fcaa1718713e3d0f8820592cd392", "path": "SDWebImage/Private/SDAsyncBlockOperation.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9856ae961aed5e66be8b00ca6e47464822", "path": "SDWebImage/Private/SDAsyncBlockOperation.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f8c06b3a65e1be95e937d3566237686d", "path": "SDWebImage/Core/SDCallbackQueue.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98d387823476bf24ccb3dcd783a352b4e0", "path": "SDWebImage/Core/SDCallbackQueue.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98fbad9c3baa14dccf4a8220894ab0a2cc", "path": "SDWebImage/Private/SDDeviceHelper.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98a115b611ba1a9f33f064ff671d8628e3", "path": "SDWebImage/Private/SDDeviceHelper.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98de083e9f9de077a1288ea323ab50ffda", "path": "SDWebImage/Core/SDDiskCache.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9889bec74a6403ea8045b03b42ea6ff0af", "path": "SDWebImage/Core/SDDiskCache.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9873813a999270c3f5cb305a8e020cb817", "path": "SDWebImage/Private/SDDisplayLink.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98cebb5dc0de298cd8f59c6b381ba19e68", "path": "SDWebImage/Private/SDDisplayLink.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9892dabd2a617483baf589f566dafad598", "path": "SDWebImage/Private/SDFileAttributeHelper.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9892bca19519069c2324ac377d2316b2cb", "path": "SDWebImage/Private/SDFileAttributeHelper.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d5f5069698d4e11a99ccb905cb4abf8f", "path": "SDWebImage/Core/SDGraphicsImageRenderer.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e983189e221377c2c342a2ac8663f54d143", "path": "SDWebImage/Core/SDGraphicsImageRenderer.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9877ae22ba0c583b321f9d5c1ed5131957", "path": "SDWebImage/Core/SDImageAPNGCoder.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9806dfd93bbcfd0be1804f226ce5d927a3", "path": "SDWebImage/Core/SDImageAPNGCoder.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98cb667df3cefabaaefcbeef2918dd31da", "path": "SDWebImage/Private/SDImageAssetManager.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f468540af31070d2e80207cffb7ef848", "path": "SDWebImage/Private/SDImageAssetManager.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9837d196672092760f5040f37c1353c81b", "path": "SDWebImage/Core/SDImageAWebPCoder.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e987beda6d88f2bfe70fd2551f49ea517bd", "path": "SDWebImage/Core/SDImageAWebPCoder.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9825fe88038e5341c0fb5e5c1615a518c4", "path": "SDWebImage/Core/SDImageCache.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9843e68604ec7850776f59ae44f254e55d", "path": "SDWebImage/Core/SDImageCache.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a5a4cd3c7cac04ea71eb16b55991618f", "path": "SDWebImage/Core/SDImageCacheConfig.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ec4dda5c41ef9994f2db69240f6c6ed5", "path": "SDWebImage/Core/SDImageCacheConfig.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98beefb69a325a0041343e231483b76d4a", "path": "SDWebImage/Core/SDImageCacheDefine.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98fb3218dabd21cbd14eec40709698ed1a", "path": "SDWebImage/Core/SDImageCacheDefine.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e6a0db70889001613370c8595c4e3751", "path": "SDWebImage/Core/SDImageCachesManager.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9843154958d1a1e15657be84d7c07881d4", "path": "SDWebImage/Core/SDImageCachesManager.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989e5e834ae58f77d6b3cd34ad66e27ebe", "path": "SDWebImage/Private/SDImageCachesManagerOperation.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98bab17b33115e1f0367037b5bcb09768e", "path": "SDWebImage/Private/SDImageCachesManagerOperation.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a34c28c2751209ee1849c60cb1f947a4", "path": "SDWebImage/Core/SDImageCoder.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e989680536850397ea90ca6bcba0d53d703", "path": "SDWebImage/Core/SDImageCoder.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f9a1e8a34f15214f5a750baa187cb875", "path": "SDWebImage/Core/SDImageCoderHelper.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98014ce589e4f04c91b4fefae768440d20", "path": "SDWebImage/Core/SDImageCoderHelper.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980145dc15ac6833f003ba69e34bca580d", "path": "SDWebImage/Core/SDImageCodersManager.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ba45672cb30f45ddf41e32e2704eb91e", "path": "SDWebImage/Core/SDImageCodersManager.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988460534c62b6e83f2e50e8bcc28826d8", "path": "SDWebImage/Core/SDImageFrame.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98bd9209c7c6b84d34bca3f51db5340cb9", "path": "SDWebImage/Core/SDImageFrame.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9811f6738133e343eeb6a30299a316b60c", "path": "SDWebImage/Private/SDImageFramePool.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ec16d845b842cc03466d31f8511352c0", "path": "SDWebImage/Private/SDImageFramePool.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98cb794fc9f6a8353415ecb77c1fa07abd", "path": "SDWebImage/Core/SDImageGIFCoder.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f7c11d172720bb9e567a540ea709124c", "path": "SDWebImage/Core/SDImageGIFCoder.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98aabb183166b68f46cfe1501721658ac6", "path": "SDWebImage/Core/SDImageGraphics.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e984242060282e65590c18448884eb65a72", "path": "SDWebImage/Core/SDImageGraphics.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c634bb927c2dfeb4571e967e8742e29c", "path": "SDWebImage/Core/SDImageHEICCoder.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98950645916d64995444de4171891c9d8e", "path": "SDWebImage/Core/SDImageHEICCoder.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98feba32159e40eb5f0412e4f079716220", "path": "SDWebImage/Core/SDImageIOAnimatedCoder.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9872db56c3c07882bd8d186ff2372d82ef", "path": "SDWebImage/Core/SDImageIOAnimatedCoder.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9846a8170ce7c1c0e384a4f6c0f2e76ade", "path": "SDWebImage/Private/SDImageIOAnimatedCoderInternal.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ffaf114df5b3f08effaef22923ac35bf", "path": "SDWebImage/Core/SDImageIOCoder.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f5f77f484587c274d08ce35480009a8e", "path": "SDWebImage/Core/SDImageIOCoder.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984adf5686bdd19fd55e9340b52cc2ae08", "path": "SDWebImage/Core/SDImageLoader.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e989421a8091dbe71de8feda5f3c52668e2", "path": "SDWebImage/Core/SDImageLoader.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a5c95cbea03b45be7aa0f78b3558f167", "path": "SDWebImage/Core/SDImageLoadersManager.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f11ee316b93368f7226f6d18d4a9d910", "path": "SDWebImage/Core/SDImageLoadersManager.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b0d4bd00a5a853482e0730fc452ac449", "path": "SDWebImage/Core/SDImageTransformer.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98842c51df173ac424b2963febb094b56c", "path": "SDWebImage/Core/SDImageTransformer.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98957ce58cb5bf0fe8f02b3b39f98709ae", "path": "SDWebImage/Private/SDInternalMacros.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98543e3cbbbaccb62cd63ed1b12bf5b485", "path": "SDWebImage/Private/SDInternalMacros.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98637fdf7f8c13facdc2de282c2ba6847c", "path": "SDWebImage/Core/SDMemoryCache.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e981ee98ffb0e5837769f6df39ccbac1ba5", "path": "SDWebImage/Core/SDMemoryCache.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98bf754fb07b48664b74711dafb7dc6183", "path": "SDWebImage/Private/SDmetamacros.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987d9f899fab3704b779c234baaec466f3", "path": "SDWebImage/Private/SDWeakProxy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e981f297a8c09081060543062cd22b43750", "path": "SDWebImage/Private/SDWeakProxy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e96f65287108a120e867b397c4793acc", "path": "WebImage/SDWebImage.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981fbb4f003fee6cc3eda3210a947dadb6", "path": "SDWebImage/Core/SDWebImageCacheKeyFilter.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e984d0da35be806c27ed8e9ad3dc48994ab", "path": "SDWebImage/Core/SDWebImageCacheKeyFilter.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b2d9af4671afab086880f7d85b71de00", "path": "SDWebImage/Core/SDWebImageCacheSerializer.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98cc9a494fa80aecab13a9a235582e0f59", "path": "SDWebImage/Core/SDWebImageCacheSerializer.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9801230e9b0c54ae173449d2d9e2a87257", "path": "SDWebImage/Core/SDWebImageCompat.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98a44ead14eae1bf3984b87aa870a7a4af", "path": "SDWebImage/Core/SDWebImageCompat.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983ce40cea84870aaa288f50ba904caa93", "path": "SDWebImage/Core/SDWebImageDefine.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e987ad54b6286dba24fddd95ae914df73e7", "path": "SDWebImage/Core/SDWebImageDefine.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a5f35de0af656985e8c2889b115bd298", "path": "SDWebImage/Core/SDWebImageDownloader.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98625ee4b49218d35973a197a56a7f8f61", "path": "SDWebImage/Core/SDWebImageDownloader.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9859dbf899bb3f158a4defb4c6efb75de3", "path": "SDWebImage/Core/SDWebImageDownloaderConfig.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e985ddc5e7a68358949715f72afac649a60", "path": "SDWebImage/Core/SDWebImageDownloaderConfig.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9815d8ba67c4a7507e5b6803e53b834027", "path": "SDWebImage/Core/SDWebImageDownloaderDecryptor.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98006ae845051762eef383ada7432023ec", "path": "SDWebImage/Core/SDWebImageDownloaderDecryptor.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98689a0bc515053b60aaa4d01dc7055dd9", "path": "SDWebImage/Core/SDWebImageDownloaderOperation.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98396383f7d8c0b0c0de24355bb4a924ec", "path": "SDWebImage/Core/SDWebImageDownloaderOperation.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9817f7bf169d5f4ef0ed0c8b15eb5419de", "path": "SDWebImage/Core/SDWebImageDownloaderRequestModifier.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98c9f97a07fb2e1a6db3858a7ae5756b0c", "path": "SDWebImage/Core/SDWebImageDownloaderRequestModifier.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9806826537e3f186935f3e9725e5604c86", "path": "SDWebImage/Core/SDWebImageDownloaderResponseModifier.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9887497dd8578869e85a9863c8fdbc250b", "path": "SDWebImage/Core/SDWebImageDownloaderResponseModifier.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c631852647e4d867e47f13aee9df021b", "path": "SDWebImage/Core/SDWebImageError.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9811514ba2e154855a88641d64341eb57d", "path": "SDWebImage/Core/SDWebImageError.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989bee16a1554a1403d0db89cf2a2e3100", "path": "SDWebImage/Core/SDWebImageIndicator.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b3036a49590b2dbff6da295f19a70148", "path": "SDWebImage/Core/SDWebImageIndicator.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98eb9726d47dec2ee472b56cc2c162c639", "path": "SDWebImage/Core/SDWebImageManager.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98336ff2ac4e3f3b3c5a7a7fd48c91d193", "path": "SDWebImage/Core/SDWebImageManager.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a05f13e9c3b3903662f2881aab1ac778", "path": "SDWebImage/Core/SDWebImageOperation.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98518243164e4dd642abe2ee659a6f638a", "path": "SDWebImage/Core/SDWebImageOperation.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ee4a949dfe8a649dbd777f892ae6f8ab", "path": "SDWebImage/Core/SDWebImageOptionsProcessor.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e989151ebd4514b3abe567938cae53dd450", "path": "SDWebImage/Core/SDWebImageOptionsProcessor.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ce7ae7251e98a346aaed45b9ef5a5dbb", "path": "SDWebImage/Core/SDWebImagePrefetcher.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9890dfa3a2ade28f439be11f79e7b91cc2", "path": "SDWebImage/Core/SDWebImagePrefetcher.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e1ecab665e88f72c584e6e6e52a98aab", "path": "SDWebImage/Core/SDWebImageTransition.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98e3f712e91d9094b4ee5afc38f842a1ae", "path": "SDWebImage/Core/SDWebImageTransition.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98cf6c33556aaa304c51fa5aa9b835f6e7", "path": "SDWebImage/Private/SDWebImageTransitionInternal.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98972b32520bbda725bda33bae5d785cd2", "path": "SDWebImage/Core/UIButton+WebCache.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e985e605089c6fb639bede08c28c17361c0", "path": "SDWebImage/Core/UIButton+WebCache.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980cfb8bc5ccf480785519cd0578629a4f", "path": "SDWebImage/Private/UIColor+SDHexString.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9843a8be4c75ff9ac43c1a56e0565d81f3", "path": "SDWebImage/Private/UIColor+SDHexString.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98193cfd58a4a39a9775d3a381beb82cab", "path": "SDWebImage/Core/UIImage+ExtendedCacheData.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9804c086e137c64beb6472c203d89ce02a", "path": "SDWebImage/Core/UIImage+ExtendedCacheData.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e893622d805f4c8966e99ddf49a1d380", "path": "SDWebImage/Core/UIImage+ForceDecode.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e988d77dcd728391cebd4ce0630e014d94a", "path": "SDWebImage/Core/UIImage+ForceDecode.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a7f204ffb0661d6e58023b82fcc6ac40", "path": "SDWebImage/Core/UIImage+GIF.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98af27c89e753ef613eacdc7554d45ba08", "path": "SDWebImage/Core/UIImage+GIF.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ce7daf15c1659f5b37479235f286b5d9", "path": "SDWebImage/Core/UIImage+MemoryCacheCost.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9859d6c7a9c513f078fa1820ee341e6e4b", "path": "SDWebImage/Core/UIImage+MemoryCacheCost.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9888e9341e89bb9de522a865b974d728a3", "path": "SDWebImage/Core/UIImage+Metadata.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98e64ac659b3077137fe5ae5f33deaab07", "path": "SDWebImage/Core/UIImage+Metadata.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98bd5a07b421e8aef1d34c7d2181127626", "path": "SDWebImage/Core/UIImage+MultiFormat.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b66d8aff0562a39e6391da523eb79d9f", "path": "SDWebImage/Core/UIImage+MultiFormat.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9808ac2f613a0df2a3812ac2add62f843f", "path": "SDWebImage/Core/UIImage+Transform.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9825eda241d558548a91137c6d69e166ce", "path": "SDWebImage/Core/UIImage+Transform.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989ae13edeef2740a1a1a81d3fca9ca32b", "path": "SDWebImage/Core/UIImageView+HighlightedWebCache.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9837415dc3c9b33973832145828c7476ba", "path": "SDWebImage/Core/UIImageView+HighlightedWebCache.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e95976f04a95f6c546b631640c96a27b", "path": "SDWebImage/Core/UIImageView+WebCache.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98dbd467cd263a8ccf6d41bc81717519f9", "path": "SDWebImage/Core/UIImageView+WebCache.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a6df1d8cb09953d6f6c5d50534df96f8", "path": "SDWebImage/Core/UIView+WebCache.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e988d7d7d521bade7e1b1a3f4906991d9b2", "path": "SDWebImage/Core/UIView+WebCache.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980328ce3be1c54d2e049b5f4f1f7aaecd", "path": "SDWebImage/Core/UIView+WebCacheOperation.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e981d61f178b9b065e04305f5dbec475367", "path": "SDWebImage/Core/UIView+WebCacheOperation.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c6b4322d6248ec5ea7537356c1f032ec", "path": "SDWebImage/Core/UIView+WebCacheState.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e980167a72f263aee3e6acb7f0decfc57a1", "path": "SDWebImage/Core/UIView+WebCacheState.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e982a712292b080f94c75e0f1c3da058218", "path": "WebImage/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98833768e0adaef421cdf8a8a1f52eecad", "name": "Resources", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9827468482280be21033960b53aa3ebe5f", "name": "Core", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98f92b8112ae3e1624791e98139e35b672", "path": "ResourceBundle-SDWebImage-SDWebImage-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e980dd01c715aef232327e094c3237655cb", "path": "SDWebImage.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9890ec8c1865f5b81662732e78ee6f5fd9", "path": "SDWebImage-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9860046a46019549e3d78bb12c7988cac5", "path": "SDWebImage-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9821e765a4f57303adf706ee3fa78cd265", "path": "SDWebImage-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f9ad28e23ea3c102e389c7a763098fe2", "path": "SDWebImage-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9829b63a809ea9e4c0cf04c68991224488", "path": "SDWebImage.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98aaaea952b89d2aa1a3c74a93a161daaa", "path": "SDWebImage.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e980822a5c26e5a1dc2785ab4a96821ff12", "name": "Support Files", "path": "../Target Support Files/SDWebImage", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987ad14e3cdc7294565b41dbaa1ecdc9de", "name": "SDWebImage", "path": "SDWebImage", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e9888a6c8417151ca225733faba633a2747", "path": "sqlite-src-3470200/sqlite3.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982de07c14d6da2ab92de7645697ed191f", "path": "sqlite-src-3470200/sqlite3.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984884e9ef55442f8e56bd933508e3e715", "path": "sqlite-src-3470200/sqlite3ext.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9821452e3fb81cc06bdacd94534bc21551", "path": "sqlite-src-3470200/sqlite3session.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98fbedfe1801a2d870d3d91bcae5937593", "path": "sqlite-src-3470200/sqlite_cfg.h", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98cb25647ac17d270bc4028fb4744868d1", "name": "common", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98d3cc0266cb7b62bffb3f0cbe61ff7fee", "path": "sqlite3.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e985ec51774adce4193df800aa123779436", "path": "sqlite3-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e981a80b76fd2d7dcf866ff9a795b1f6ca8", "path": "sqlite3-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9849b47c2abdbf16836d644496a7c96c3a", "path": "sqlite3-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ca98938030f0f094bfff206570f618b2", "path": "sqlite3-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e986aac232603a5990df92fa151b189bdea", "path": "sqlite3.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98627274f1b5806612f6de316806ffa433", "path": "sqlite3.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98b8d1e842b35fde04d9f98313500bdbf1", "name": "Support Files", "path": "../Target Support Files/sqlite3", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b126aea5594db8ab0f2b9f64b0d9dde2", "name": "sqlite3", "path": "sqlite3", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9850f989e79940d0d227b52702b7012293", "path": "SwiftyGif/NSImage+SwiftyGif.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e984a050bdc3d8da68cbdb406488e41899f", "path": "SwiftyGif/NSImageView+SwiftyGif.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e987de65b7f9a1a2efaca970467fbda119a", "path": "SwiftyGif/ObjcAssociatedWeakObject.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c21bb640c6fa4eccffb6ce54c7f84f4b", "path": "SwiftyGif/SwiftyGif.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e981b7b6fbf55f37bd3eebc26728e7a9139", "path": "SwiftyGif/SwiftyGifManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98948fba78b0fabf59725a37ae2c066b1b", "path": "SwiftyGif/UIImage+SwiftyGif.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9870920bd4b03bcd3cad18a74732bb4cdf", "path": "SwiftyGif/UIImageView+SwiftyGif.swift", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e987c92e209ee6711768922966184edc175", "path": "SwiftyGif/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e980848adeaf569683ef204575453037c53", "name": "Resources", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e989df4b3b4aedecc104ede59a1192c048a", "path": "ResourceBundle-SwiftyGif-SwiftyGif-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e987d9b0a91642de2a929c78e0d1ad839ca", "path": "SwiftyGif.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9841f0011fe7237da94bd6a9d6eabc3c30", "path": "SwiftyGif-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98bf405697fc24402a48322e874d66708b", "path": "SwiftyGif-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c3f987dd350f9aaaf0dec2e99dc0e2cf", "path": "SwiftyGif-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a5f3444bd4a34024d2f2dea2984a9bf9", "path": "SwiftyGif-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98a6c702572fa8b168314fca3a2ce34bf5", "path": "SwiftyGif.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98ddbc566afcc2120f8343711513bad7cd", "path": "SwiftyGif.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98e07ee2afb5dbb344003e5dd295aef2a8", "name": "Support Files", "path": "../Target Support Files/SwiftyGif", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98992133051bf307dd2fbaf0d7fd89c262", "name": "SwiftyGif", "path": "SwiftyGif", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983b401beda4f1a0846161f832c6d16126", "path": "Objective-C/TOCropViewController/Models/TOActivityCroppedImageProvider.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e988fb552e6fe01eec5d2ffcec828e52f88", "path": "Objective-C/TOCropViewController/Models/TOActivityCroppedImageProvider.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981470b9d19a7857f9dbb74ca937256b4a", "path": "Objective-C/TOCropViewController/Views/TOCropOverlayView.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e983b1243c5f41f2364696e8339a5db812e", "path": "Objective-C/TOCropViewController/Views/TOCropOverlayView.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98cc29a439c01eb3ebedc5ff04d7f946eb", "path": "Objective-C/TOCropViewController/Models/TOCroppedImageAttributes.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ec2f429c2889928e143c9ff224032053", "path": "Objective-C/TOCropViewController/Models/TOCroppedImageAttributes.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986cfcedc33e0d16f12fe9118d7d0f61c1", "path": "Objective-C/TOCropViewController/Views/TOCropScrollView.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e982bd13c3b9c14cdf3d319174c64d176d1", "path": "Objective-C/TOCropViewController/Views/TOCropScrollView.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e416b414d0da93a2d47486cc66267ed7", "path": "Objective-C/TOCropViewController/Views/TOCropToolbar.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e983af25933d0248efeac83203d768e4301", "path": "Objective-C/TOCropViewController/Views/TOCropToolbar.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981c756becda77c93af673304b88ad7926", "path": "Objective-C/TOCropViewController/Views/TOCropView.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b5598f492668bbcd1465c24be87bf9e4", "path": "Objective-C/TOCropViewController/Views/TOCropView.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a6e482120721f83ca821491348dc6e02", "path": "Objective-C/TOCropViewController/Constants/TOCropViewConstants.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b9b3dd2b271c3fe01971e2595ae14c2a", "path": "Objective-C/TOCropViewController/TOCropViewController.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e985bb509e6531cb65e3d5ae7051387fec2", "path": "Objective-C/TOCropViewController/TOCropViewController.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98434fff73958f0254a65bb59291c514c9", "path": "Objective-C/TOCropViewController/Models/TOCropViewControllerTransitioning.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98a4a23e7a7be8bc8b3186238c915ec642", "path": "Objective-C/TOCropViewController/Models/TOCropViewControllerTransitioning.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9818d623b6cf1c6bf48cd1c122b133611b", "path": "Objective-C/TOCropViewController/Categories/UIImage+CropRotate.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98595ec6caf5b8cbe108657fb4033df9ad", "path": "Objective-C/TOCropViewController/Categories/UIImage+CropRotate.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e98977b86dbea041ec7bc1e387c6d2cbeba", "path": "Objective-C/TOCropViewController/Resources/ar.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e98f4d7deed2e662f1e30ae0e5974cc5e6f", "path": "Objective-C/TOCropViewController/Resources/Base.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e983352b5b1a881db9638340dec64442eaa", "path": "Objective-C/TOCropViewController/Resources/ca.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e98628f00ebdc480ab16b73b8e525674e59", "path": "Objective-C/TOCropViewController/Resources/cs.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e98c0a6bf2bb96b7946084bc40d7e05d753", "path": "Objective-C/TOCropViewController/Resources/da-DK.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e9834ffedc2ac056c69a29257393a7559d9", "path": "Objective-C/TOCropViewController/Resources/de.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e9847e29d5ee404c463c3d908ffe7b25855", "path": "Objective-C/TOCropViewController/Resources/en.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e985c2f626d046c9b5dd96a9f6a5eba0af9", "path": "Objective-C/TOCropViewController/Resources/es.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e98e0a85cab468a9f4dfbcb67593324a2d5", "path": "Objective-C/TOCropViewController/Resources/fa.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e98f7515bfa7359571234463c3051579a09", "path": "Objective-C/TOCropViewController/Resources/fa-IR.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e98a2e9f30849e8be058caf3f0e2eb4eb47", "path": "Objective-C/TOCropViewController/Resources/fi.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e989f4dfbf719a172fbc5007755bf274f95", "path": "Objective-C/TOCropViewController/Resources/fr.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e98eb81f03d7a8642385f91369384f770bb", "path": "Objective-C/TOCropViewController/Resources/hu.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e98b67740b05afb98bcf48b73e4339c8c5f", "path": "Objective-C/TOCropViewController/Resources/id.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e98a335ccc10f855483d27fb5a1bd771577", "path": "Objective-C/TOCropViewController/Resources/it.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e98e34f4e9eeee7cd054079dc76d50442c3", "path": "Objective-C/TOCropViewController/Resources/ja.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e980a58a6b23a7f0a71fba925a17740e367", "path": "Objective-C/TOCropViewController/Resources/ko.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e98d67a77251ef27fdf3572341bee8e9f04", "path": "Objective-C/TOCropViewController/Resources/ms.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e98ba1ad7a4a8612f58ef3795014bdff8b7", "path": "Objective-C/TOCropViewController/Resources/nl.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e9837d2ec36f7ce63a7e405503abd79e2a6", "path": "Objective-C/TOCropViewController/Resources/pl.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e983dac86c0169f2d1458d097e49eb25696", "path": "Objective-C/TOCropViewController/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e98e0c1c4026475fa1193b4536f8f098c72", "path": "Objective-C/TOCropViewController/Resources/pt.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e9882b1e5e1693936be00220a1a06e89e92", "path": "Objective-C/TOCropViewController/Resources/pt-BR.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e98a3ebb4ae588bdf95735ff0cca1466296", "path": "Objective-C/TOCropViewController/Resources/ro.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e9858e0bb5c3d16c493be8d42a9eb27e3d6", "path": "Objective-C/TOCropViewController/Resources/ru.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e98a23a99459cec25902127f0d6276a878a", "path": "Objective-C/TOCropViewController/Resources/sk.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e98a92db87d09d2560f1a3083b6cd8e688f", "path": "Objective-C/TOCropViewController/Resources/tr.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e981e78e89ffbadc59a27ac265835ad953b", "path": "Objective-C/TOCropViewController/Resources/uk.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e989bd3688393b80aa31186ef9e9d3bf6ca", "path": "Objective-C/TOCropViewController/Resources/vi.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e9802f1d3782e04b0ea3ea644ac5cf06c02", "path": "Objective-C/TOCropViewController/Resources/zh-Hans.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e98cd2862abab4595a5ac7b817d1c334c35", "path": "Objective-C/TOCropViewController/Resources/zh-Hant.lproj", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e986a0072625d41bbc46000c05e1d433814", "name": "Resources", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98026ddff0d33608fb2c9607729cb311d5", "path": "ResourceBundle-TOCropViewControllerBundle-TOCropViewController-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e9829b495b8d04867000d556fe108b4780c", "path": "TOCropViewController.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98cbe90b50ea4530d5680a347893f3dd85", "path": "TOCropViewController-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98469f46505770160a822f3626f410f70c", "path": "TOCropViewController-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9841fe71391a4439316a2ad8387a78e3ea", "path": "TOCropViewController-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981f190c0cf48651731d71d4afa45b75a4", "path": "TOCropViewController-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98d3f1b2f3af4439cae6db119fac199146", "path": "TOCropViewController.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98525d5782d48e9362cca38cc3af75bae7", "path": "TOCropViewController.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e988aeddc23ea1bc377a7138fb14a3da96f", "name": "Support Files", "path": "../Target Support Files/TOCropViewController", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c7dd1b90ae29b5dd4da95b7f77e7d24f", "name": "TOCropViewController", "path": "TOCropViewController", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c294b22be4220ff8dac9ff0b4a21f7ab", "name": "Pods", "path": "", "sourceTree": "<group>", "type": "group"}, {"guid": "bfdfe7dc352907fc980b868725387e983b83816ce7825ecc63008b864e0164c4", "name": "Products", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98a3f4680c874ecbcb5b5a6338bc13a426", "path": "Pods-Runner.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e9814181933c973297b0001d0e9ee64381d", "path": "Pods-Runner-acknowledgements.markdown", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9887440869a53d6020d968ddf6b9b30aa0", "path": "Pods-Runner-acknowledgements.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98fed0970e703d355c9aff813f280aafdc", "path": "Pods-Runner-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.sh", "guid": "bfdfe7dc352907fc980b868725387e9826e505869a67d81656859ded68178c1d", "path": "Pods-Runner-frameworks.sh", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98de02ac5005ab1eb052abf25c63731e95", "path": "Pods-Runner-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.sh", "guid": "bfdfe7dc352907fc980b868725387e98f6f4dcb116856f2da0fa97d3015cb1af", "path": "Pods-Runner-resources.sh", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980ec6fbca7264a6936f2adfc48dd7f5bc", "path": "Pods-Runner-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9850cc7fc2d23136fb4fac488d6c47df20", "path": "Pods-Runner.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98dc1a21852c7a085c7dd8f65cf0fa9907", "path": "Pods-Runner.profile.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98f17abf55d75f35efcaf45a1185b085b6", "path": "Pods-Runner.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98cf3c1972df678a5e36df75a46391500d", "name": "Pods-Runner", "path": "Target Support Files/Pods-Runner", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e984c4f55ec853c945e234980557a98aed8", "path": "Pods-RunnerTests.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98fc0f7e7242f459f81e455145932dcafd", "path": "Pods-RunnerTests-acknowledgements.markdown", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e985f8b68b152f46f18718da20c04e675cb", "path": "Pods-RunnerTests-acknowledgements.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98022654f1ff78dd844d694dba2439dab2", "path": "Pods-RunnerTests-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e989e5ad6b9a07953a12c7008a15bd9c99c", "path": "Pods-RunnerTests-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e5e8bcdff29e5f8321be18f7989b4bc7", "path": "Pods-RunnerTests-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98144cd18850e477837c238075d5256ffe", "path": "Pods-RunnerTests.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e981b663a2c82f0220040296818ba53477e", "path": "Pods-RunnerTests.profile.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98965b92d39d30a7872295adc2841cd1b1", "path": "Pods-RunnerTests.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9859551a2ccb1df711861b574920cd49bf", "name": "Pods-RunnerTests", "path": "Target Support Files/Pods-RunnerTests", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98dafc421ff02609f2772b356038eb9849", "name": "Targets Support Files", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98677e601b37074db53aff90e47c8f96d1", "name": "Pods", "path": "", "sourceTree": "<group>", "type": "group"}, "guid": "bfdfe7dc352907fc980b868725387e98", "path": "/Users/<USER>/Desktop/source2/flutter/apps/super_up_admin/ios/Pods/Pods.xcodeproj", "projectDirectory": "/Users/<USER>/Desktop/source2/flutter/apps/super_up_admin/ios/Pods", "targets": ["TARGET@v11_hash=9d7dd5b3b363b760394fcc1bba41489d", "TARGET@v11_hash=9aa758d9f157ee9e0857fe6afd8594e9", "TARGET@v11_hash=46beea541dd402ca385a71b998095fc2", "TARGET@v11_hash=eb92f0d63d22029acc385f30771abb7b", "TARGET@v11_hash=dfeaf1bc155a84305e7d23ae73d37684", "TARGET@v11_hash=ae88c34917bbe94de3ca28622960b223", "TARGET@v11_hash=0f91481ebaf63d8d488cc89dd86b24f5", "TARGET@v11_hash=79e0db54f8e76a46d94c7e83c1a5bce5", "TARGET@v11_hash=dbcf22817b7163e1c11a243229443086", "TARGET@v11_hash=6091fa782439b25eda4dfaca0b8c56d4", "TARGET@v11_hash=a37d3bc1e965a71585956e16bf065f8f", "TARGET@v11_hash=70ebcc957c562c18a55d3cf685805f71", "TARGET@v11_hash=8957958a4bbba5d49e88b0cdaac89b3d", "TARGET@v11_hash=ad55a01313180abd51aaebcd976f1e82", "TARGET@v11_hash=21112aa77b337e4670b8da96918b1802", "TARGET@v11_hash=29eb64c919213ef59916afd58299b94c", "TARGET@v11_hash=4a1d1654e6a3bbef1dfdd85dea8f13a1", "TARGET@v11_hash=ac8cb2224851cccfbe80f52c9c4e8111", "TARGET@v11_hash=29235e4446a1d7476a0a02ae008ff506", "TARGET@v11_hash=1ee27f190d069daa1f9d46ce99eb90a8", "TARGET@v11_hash=5350bce2bfeacf95d83394e4824032b0", "TARGET@v11_hash=cb15b8a27b97e9ce588b5236bdd2efdf", "TARGET@v11_hash=942ca55be2304161f777813ee704d9ec", "TARGET@v11_hash=56cc79decf278d4ef5cba3f604e92e52", "TARGET@v11_hash=200d70d5ca663608fa295700ac08a507", "TARGET@v11_hash=058bb9ba02e1e247554e5765b649faa5", "TARGET@v11_hash=69bdb07e1b6b0dfc0b1090f23b976c54", "TARGET@v11_hash=2609ef8eae2ea199abbfde086a4ad880", "TARGET@v11_hash=8fa33f45bce1107797c48b9f8ab1eaae", "TARGET@v11_hash=a31fd7b2c159b211a32282e3a8af1f45", "TARGET@v11_hash=10d10b65f9fcb640d2409566eded430f", "TARGET@v11_hash=577da628c326a9c2a5bc0680d954da22", "TARGET@v11_hash=e4d9c67d98977dd3bb9847705e6590a5", "TARGET@v11_hash=605d05d53f8b3e8e7cd4209c6769963e", "TARGET@v11_hash=563d4f904a107f3f52986e64d827ee26", "TARGET@v11_hash=44c6708c755f704dd93246359aeca90d", "TARGET@v11_hash=b4be256b9b6eef3758704fe1287f6ced", "TARGET@v11_hash=85ef89e8a405ec70b9a70ce94289e13e", "TARGET@v11_hash=e0636f6d86c414b5e8290b7bdf87a132", "TARGET@v11_hash=a26bd97adadaa6f1284ae4f628e22f43", "TARGET@v11_hash=d026a54f1683ccd9abed3c0fadf35ccc", "TARGET@v11_hash=5efadf21037392ab8302644033f61012", "TARGET@v11_hash=69b395ef1e1ddcb2264d17fcf85aee32", "TARGET@v11_hash=f9a97c984ac2d99e62f6d8618b2b7a4e", "TARGET@v11_hash=0b6619d8616b0ba07e555784e14dc8a2", "TARGET@v11_hash=b3d5e99358b3f573207b4944d9294279", "TARGET@v11_hash=b084e9f757610ba95f59a9d218ede3ec", "TARGET@v11_hash=ddd69e46b30e1fadf9bd96e29efeef00", "TARGET@v11_hash=abdb7ea2a0ccba0b7001ec638e2812c7", "TARGET@v11_hash=5843e8904431269bf70c8252cffa978e", "TARGET@v11_hash=9130500288fa565011940e769377802e", "TARGET@v11_hash=04fa184321b0496c23fba4dff58ce4bb", "TARGET@v11_hash=43e63abc9dd0b619137a4d5dade103e8", "TARGET@v11_hash=5f6d19cbb7d38fdcdfd46b511a3c7230", "TARGET@v11_hash=a44537432f3ebdfc45d3c1b7819e2855", "TARGET@v11_hash=78cdba644c2b038546cc75a3bad5e205", "TARGET@v11_hash=cae90ca6ec36ba007a4ea50857498840", "TARGET@v11_hash=ef89c8c23d0d7a54a8d9bf3b5ae34bfe", "TARGET@v11_hash=6e7425e4df9cfc3731819d4706b7a7b5", "TARGET@v11_hash=2a21bb399e7b82060a4a4de849f100d8", "TARGET@v11_hash=6f8655b85e20f9a277459de0a711013d", "TARGET@v11_hash=882566c2bcdc95250adcbed0e769326c", "TARGET@v11_hash=a9ab3a07314bb573ef0a9e61c18c2211", "TARGET@v11_hash=82f225beaac30f5e9f0fa237be56ac62", "TARGET@v11_hash=dcf7e9884557748a547116a11bed6c82", "TARGET@v11_hash=8f29ad2dfb90db887e83a1e157af888a", "TARGET@v11_hash=1311406c52b01521fd4c3d201904fa30", "TARGET@v11_hash=68b18eb636caecac70ab8e5299354245", "TARGET@v11_hash=bfed220bb98b981728c56f9b56a963cf", "TARGET@v11_hash=29db6c4c70376d3f1c340a18828a39c4"]}