{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98196e393fa3dda1ad9604be43e641d81a", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/background_downloader/background_downloader-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/background_downloader/background_downloader-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/background_downloader/background_downloader.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "background_downloader", "PRODUCT_NAME": "background_downloader", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98c0fffc2e22ee4e9d44bb0ea4672f4465", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9801c98d4106e43a96de9a55dbc28c768d", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/background_downloader/background_downloader-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/background_downloader/background_downloader-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/background_downloader/background_downloader.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "background_downloader", "PRODUCT_NAME": "background_downloader", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98332d32d6929512b3bfa1827ecea7b5c9", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9801c98d4106e43a96de9a55dbc28c768d", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/background_downloader/background_downloader-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/background_downloader/background_downloader-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/background_downloader/background_downloader.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "background_downloader", "PRODUCT_NAME": "background_downloader", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e981e19775af858787ad493debe3ff7f7d6", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e982e9161cd6c55d6844e747f9279a79646", "guid": "bfdfe7dc352907fc980b868725387e98c3a11ca938472218b0ba3e92b0c190d3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983b8380d9a549a218f6899e4f95664f5a", "guid": "bfdfe7dc352907fc980b868725387e985d3acf24de6c6d35b3d0e846a8baaa03", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98672bed4692398a6de82c108c73aeb7ae", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e987fa9e6419a90572ba5b39ab7ef3ccc38", "guid": "bfdfe7dc352907fc980b868725387e9842bf68574ba74d17ddb39e508ffa2cd7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9864c13f0a0e973703119f3ecf4936feab", "guid": "bfdfe7dc352907fc980b868725387e98d2d1143cd9576a049b29fbb3feb941ad"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98be2b752248ab0d8b9bf66144c14972f2", "guid": "bfdfe7dc352907fc980b868725387e98d45802caa65c37f575482f14150937b7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98db8e1c8d7fad8165de654f7e56a9185b", "guid": "bfdfe7dc352907fc980b868725387e982f2e6eee0076a83f2feb9f06f0159a7b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cb6bbc5f9ee004c16f202e61d53b5a18", "guid": "bfdfe7dc352907fc980b868725387e987d9272902cbc07bad7f4cabf7ea3b06c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bdbf5ee5cf5f10cc6f2b49183c6b364e", "guid": "bfdfe7dc352907fc980b868725387e9886904542efb7f3f46476d764393a5dcf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98118843db85d1de2e0ef32f249a35186b", "guid": "bfdfe7dc352907fc980b868725387e981565fcd5ade476c8ed0f1cb6f40987a5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e58ad9f4e826c530c87f64bd6454b973", "guid": "bfdfe7dc352907fc980b868725387e98cf527905db3c6a9552ece3a374199d5f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bc1f600517e68acbcc7e8340eb07c035", "guid": "bfdfe7dc352907fc980b868725387e980f92752158062e8e33447223bf9c1018"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981098a4afb6f3e63dddd3b01d0ca36eb8", "guid": "bfdfe7dc352907fc980b868725387e981153770fa7720f7ec51e991629f4ae04"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982ed158bcfb2aadfe6e2ebdf2efd95b62", "guid": "bfdfe7dc352907fc980b868725387e98fe5a82c8e979e6596cbbaf7afb962e76"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982e02a3e5df42967af1d269995151ead3", "guid": "bfdfe7dc352907fc980b868725387e988205a5fedf1e0f82b13d6a183eb73158"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9887ff3cae09b58b45128f3ca8bfb94c9f", "guid": "bfdfe7dc352907fc980b868725387e98909d6619cc34955b83a720c0b0e4fa7e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981e8a92da4b6b8b2637238f495798675f", "guid": "bfdfe7dc352907fc980b868725387e98b315eeac8edf5410e9e7e5c8e755a33f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e5af5f1b3ebecb8f5cd98d1c96528593", "guid": "bfdfe7dc352907fc980b868725387e98173b74730a8a96407d4d481b1ac65a4d"}], "guid": "bfdfe7dc352907fc980b868725387e98c811b776d5757de8791dca0ba3862fff", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e987b9e1c3b6563a543b6fbe5f0759aef8f", "guid": "bfdfe7dc352907fc980b868725387e98730fd069db8a2e9af5dbc41b8659e9db"}], "guid": "bfdfe7dc352907fc980b868725387e98bea28dab9865c035b0708a59c40a4828", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98f4c313e7a457e1a9a7882f9dd9775a49", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}], "guid": "bfdfe7dc352907fc980b868725387e98f1394fdd12b0ecc7d36309baf8eecbd0", "name": "background_downloader", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98c6de70f480b4066b608306ea736c7be1", "name": "background_downloader.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}