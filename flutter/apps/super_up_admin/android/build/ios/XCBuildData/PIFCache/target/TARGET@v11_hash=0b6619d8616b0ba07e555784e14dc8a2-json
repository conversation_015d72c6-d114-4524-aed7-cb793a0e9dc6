{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98488068341154f61f5d0bc36b7eaee863", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/photo_manager/photo_manager-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/photo_manager/photo_manager-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/photo_manager/photo_manager.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "photo_manager", "PRODUCT_NAME": "photo_manager", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984dabebdf6f54c881cf6ce3f503dbafc8", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e981e3fd56aeee9fe2911f687a9cd639d1e", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/photo_manager/photo_manager-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/photo_manager/photo_manager-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/photo_manager/photo_manager.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "photo_manager", "PRODUCT_NAME": "photo_manager", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9879370f9b6762ae3b167643fc360811ad", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e981e3fd56aeee9fe2911f687a9cd639d1e", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/photo_manager/photo_manager-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/photo_manager/photo_manager-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/photo_manager/photo_manager.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "photo_manager", "PRODUCT_NAME": "photo_manager", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98e020dca05c43366d73fdccc591333ac8", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e980840f18a4bfa593dd94e67695dbf044a", "guid": "bfdfe7dc352907fc980b868725387e986962b4fe0a88b78476ba26c93aa97f44", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981ebfa5eb65fbe91b3ea9c0b7a1fd7041", "guid": "bfdfe7dc352907fc980b868725387e98390c281cb2b06cb52f4205b017a373f4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bfa531afcfe43313267fcef8c07f351c", "guid": "bfdfe7dc352907fc980b868725387e9853e3f219e4ad51465d3628ba7b4c5545", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9858cab52bd9903ead1c4d66cc01ced346", "guid": "bfdfe7dc352907fc980b868725387e9868c765dbb77c98b5bfcfe18b020a1c08", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986c6129317fa25fa56f63e3ea1ea5d52b", "guid": "bfdfe7dc352907fc980b868725387e984b094716ae841d78ce9a5edd5b9e551b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cbdc2d5e40318534a6fa9ef1f7e6836a", "guid": "bfdfe7dc352907fc980b868725387e9887f0c60e1ef68bf38dd2a564f13f9f93", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981c0859f284615776af418bab11676791", "guid": "bfdfe7dc352907fc980b868725387e985c3babd673f55dd94749533df8d51832", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986fe7d761e4693793b9254e28ca8c1f89", "guid": "bfdfe7dc352907fc980b868725387e9881b398ac24a06d00af276e707dc36d71", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983aaf24c0140d1eeb213182cfdf2d0575", "guid": "bfdfe7dc352907fc980b868725387e989b88c4979fe2bf00ce168fd405492937", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98752693e1025605ad3b3642f2ed91f8c5", "guid": "bfdfe7dc352907fc980b868725387e98b3ee86f1bf5720e0716479bd8744044b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98237e1a8f9793e62960817076e9e86115", "guid": "bfdfe7dc352907fc980b868725387e9841421f6134bdb668e93eede2e59f4b05", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98064e1d7b5672a85edcfbb69919694e6d", "guid": "bfdfe7dc352907fc980b868725387e9850d3cdfced5702aab73e8b043c54b990", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bcd6248edfcf50796a1856ec771e7ac5", "guid": "bfdfe7dc352907fc980b868725387e98f6a390e89d285925cd05b1de918eda9a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fb674aa98c3bea7ab019e04acae87d94", "guid": "bfdfe7dc352907fc980b868725387e98bcc8e100857d0763ca1ee9ff41ea1073", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98267d613b686f8e90b446338bd3e8e197", "guid": "bfdfe7dc352907fc980b868725387e98f08a5d7869252cf51c2a53412fc2956d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ddd2d89cc5a2b7314c3fc7bf13294484", "guid": "bfdfe7dc352907fc980b868725387e988fbf03197226d1dcd7bf64b205568d0a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ae16d131ae39904cc2b04cdf829038a2", "guid": "bfdfe7dc352907fc980b868725387e987d26a26eb8136ad80e61fce25d819d0a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981d43cf754f506d07e7b96d7ea0b666e6", "guid": "bfdfe7dc352907fc980b868725387e98c0697d5e93fc555ef6b5754df82c7128", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987fb249b655368e3bdb7e6a4019b6d1c9", "guid": "bfdfe7dc352907fc980b868725387e98bd477e2434d144c5576c66fccbe1f864", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9884511de53146198d6473eb6bc6c5cee7", "guid": "bfdfe7dc352907fc980b868725387e982248a9f5a4df0ed3e95db1ea6d8f0524", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b77ff5eab8be34d9705851ec3daf377d", "guid": "bfdfe7dc352907fc980b868725387e98a2f7711b14a0362cc09077d8c4d8ddb9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989551105e1a95248e276c672cbf1dbd3d", "guid": "bfdfe7dc352907fc980b868725387e98a01c79cadc4c37264ddf4aa8c27d129d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eef24a22b07542486c48ae9e8d24d54d", "guid": "bfdfe7dc352907fc980b868725387e981b061e51feada1cc13bb7980ae985fe9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988ffb670a2a0d7d95200623265ef63e1f", "guid": "bfdfe7dc352907fc980b868725387e9844549f54574fd365b030a096028454ce", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b7ec5744e5cccc2fc94f5a568ab52350", "guid": "bfdfe7dc352907fc980b868725387e9801d90fcb1989c925c7f155b345babe81", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b5091167e5f8740abe0cbae90304630b", "guid": "bfdfe7dc352907fc980b868725387e981f17fc5c6bfff951ac3298e705fd28d3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9812bdf6358fee6c1f1d89e814bdf9a2e0", "guid": "bfdfe7dc352907fc980b868725387e98df34f894b94d687b3e499864e7b814e5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988650730f704b0b2df245424e05a06b21", "guid": "bfdfe7dc352907fc980b868725387e9895699b7a7a6f8bb88a1c5d40a8aab4a4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9816b7460a2f6a45108873f0f0a55e4103", "guid": "bfdfe7dc352907fc980b868725387e983576b22d0d5d501f803b48311fe94621", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98553823561766d9846afe634055a64e70", "guid": "bfdfe7dc352907fc980b868725387e98ede9f31f0f690df2df83ff32d713b889", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f0531bc591a814f05e63732991ab5d23", "guid": "bfdfe7dc352907fc980b868725387e98584dad247a90dc26f75071f1f60681a1", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e9850d0827f95b51d4b1cdee62dc0ff2ebb", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e983496668f6be758a618558c8c102a6226", "guid": "bfdfe7dc352907fc980b868725387e98fefb138cd2cd11a9aa712781fad39db9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b3d2d508ea1d82d3cfce43e10e763d17", "guid": "bfdfe7dc352907fc980b868725387e9830b1e43d76d7f779fe7ab6328200b1d8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98afc9e4549366d502ef926e09c2bb46ce", "guid": "bfdfe7dc352907fc980b868725387e98a57291a1ca22361a70cb07ba2dd1ba6e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98934d0d5211dd91dc9f54eed9484bb916", "guid": "bfdfe7dc352907fc980b868725387e9864ac135276efe0c98cd398c571f68b08"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f6d247a3c7ccf24ab4c6588593ad6a85", "guid": "bfdfe7dc352907fc980b868725387e98ede7572439bf920930dcfea2b2e7e843"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f7221c811f1bb8342db5cda332893940", "guid": "bfdfe7dc352907fc980b868725387e9848301141b51218205a2ff0abaef22a42"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9881709cbf3235204f6c03a317fcf32423", "guid": "bfdfe7dc352907fc980b868725387e98b0b32ea77fbb5f47ad8c22fe70b95f79"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f3ffb45a56b5488c1e09007edd2ff80e", "guid": "bfdfe7dc352907fc980b868725387e9884557875cfb2614f60e590d77899439e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e892b635a61b8ac8d6d5c1866edde378", "guid": "bfdfe7dc352907fc980b868725387e983ca45de3872457bcedb5b7773f894392"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9885d4246cb1a0cb6cfec3173db118fcf6", "guid": "bfdfe7dc352907fc980b868725387e98a6c410f272bf957b6219b197e63cad97"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ca3897099a069c1b308eba29c8def0e8", "guid": "bfdfe7dc352907fc980b868725387e981efa00f3c044321f1188dc225af10e74"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98652cecd85132d3d42cfc6bbc3e2a2e67", "guid": "bfdfe7dc352907fc980b868725387e98fc6e5d547cc910f62eccbd171385635f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e9141bb0b7e61bb80f1ff2adc8392358", "guid": "bfdfe7dc352907fc980b868725387e98c87b4bb21055c6b1784c7758f531affa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a06916546af028fc85ae81aa93119c5f", "guid": "bfdfe7dc352907fc980b868725387e98d4ae9db8af0d0257834cc631218e186a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982d84ffd29ec576b4a32c5eedc306199d", "guid": "bfdfe7dc352907fc980b868725387e98d74ec9e8c3301d0126e64f60db440a8c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987b20a6ccb5a33875b21b860b0dad636e", "guid": "bfdfe7dc352907fc980b868725387e98bf4f8abc1ac16c0647b3c52112fd176e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986562b68e77a8bc040fadce9b2e16a1b7", "guid": "bfdfe7dc352907fc980b868725387e98c89e606041897f9b245edb8610dc4fd8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9873105cd40b28f82f796c307a8afb47af", "guid": "bfdfe7dc352907fc980b868725387e98f8ff92091476dd8fa3a509b3352e3ee8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983270643537e78879f5987db618ed1992", "guid": "bfdfe7dc352907fc980b868725387e9807b96d986c69545c58d6b77805d61420"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e7037d7860c8ef87b1e571d8d799f48b", "guid": "bfdfe7dc352907fc980b868725387e9871abf3912fe01115cd1244ea7c463595"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f1eccba4172e344b6e06b4bec78d0786", "guid": "bfdfe7dc352907fc980b868725387e9868583f34c4a2837cab5414d51ce45681"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ea546f29880840a281ecda8216584ea1", "guid": "bfdfe7dc352907fc980b868725387e98a09ea62e4ea19b1272c01ecf1602b10a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f021b54d0c11926943338842e0646659", "guid": "bfdfe7dc352907fc980b868725387e981e3747e9fee993c4c5061092079b9781"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986c49fbd1844c1e9be5cfa498c2b37e77", "guid": "bfdfe7dc352907fc980b868725387e985c0233c9e02622b403b4ba1b3087ae07"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ecfb9c45ff8dc86fb0d4c8d675b83092", "guid": "bfdfe7dc352907fc980b868725387e98fba4e01bc0274fe3cfd2e845a30eca9a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98149782a6e406fa08f098d83479e59ca7", "guid": "bfdfe7dc352907fc980b868725387e9872c1e823cfe16d8cfb88936061b28e55"}], "guid": "bfdfe7dc352907fc980b868725387e98320e1412584a90fd41da063c83d2dd21", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e987b9e1c3b6563a543b6fbe5f0759aef8f", "guid": "bfdfe7dc352907fc980b868725387e98c66fe7d2ba435345befdf4d53ffe9324"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e4312a17bfed5f03cd67f84d23bdd541", "guid": "bfdfe7dc352907fc980b868725387e9837f82c7d5501ad000577f511c7066743"}], "guid": "bfdfe7dc352907fc980b868725387e986639f5be6da1c90ed3fe0682aa68698f", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98569e89e49d03cc7e5bfa84b901e57021", "targetReference": "bfdfe7dc352907fc980b868725387e98e6c2666f7221fa58caaa0668238bc4de"}], "guid": "bfdfe7dc352907fc980b868725387e9878595d290b1af8130556a3317b477895", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e98e6c2666f7221fa58caaa0668238bc4de", "name": "photo_manager-photo_manager_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98cd1e97b2c7c0c2a96b4035a4c62b427d", "name": "photo_manager", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9841b881a585d538cf3da17a18e8b8ed12", "name": "photo_manager.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}