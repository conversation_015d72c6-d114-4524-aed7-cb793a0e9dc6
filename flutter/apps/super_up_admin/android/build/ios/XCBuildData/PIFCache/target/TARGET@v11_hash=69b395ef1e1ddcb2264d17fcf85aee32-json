{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98571841afd6eb898191a0ad2dc1384e19", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98519c0f1aabf34eacbf6f755b813f1496", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9807c92c08ffc15422f5a693aca183a35e", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ec62137b0ee28ca4265944856877be27", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9807c92c08ffc15422f5a693aca183a35e", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9832ebf16d88dad2729e444c32094aa46a", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98fea5bba478d9257d548e15dde83abf3c", "guid": "bfdfe7dc352907fc980b868725387e98a67a774806c660b38554588c3e70208c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9859507a63dbb4cee149548fadfa85a0b9", "guid": "bfdfe7dc352907fc980b868725387e986d4a292733b7477b7e70d50202bd87c9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bf845ceaef672618f1dcc892f1baee00", "guid": "bfdfe7dc352907fc980b868725387e986e1ed2fc3cd6e250c3958bd302fa8256", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eccf103729e694154a9d647e269b9a78", "guid": "bfdfe7dc352907fc980b868725387e98fcc3a55b062df68507a54d707525e589", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9844399f359d75181aaca443641033e5cc", "guid": "bfdfe7dc352907fc980b868725387e982480db0e783895cfa485984aeb72f285", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9814825db1345022637284e76a0ef7bd72", "guid": "bfdfe7dc352907fc980b868725387e988a5188cf4b05e81308d1b3e25d98b7e5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b0e0509ded3295987cc0053016f1e681", "guid": "bfdfe7dc352907fc980b868725387e987d402e7f857f0e99ce45c1f9fa8e3d6f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983a51b2e86a23a7cd6d11313acc126444", "guid": "bfdfe7dc352907fc980b868725387e98b4eb92090387c3c534674098af8b5033", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d2f30ec24f02d7da99f7e42acf00abdd", "guid": "bfdfe7dc352907fc980b868725387e9834f624176587f64f35b86b3e18121fcd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9848d90addee7a13e042579efe95deb0d8", "guid": "bfdfe7dc352907fc980b868725387e98146160bf7174dcbfcd1865d77ed89c41", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985d854b902156950a112ebc5f5283f6c0", "guid": "bfdfe7dc352907fc980b868725387e987cb0333cf990092cc80a91c4692f0582", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983ad49a3c15a2e6574cc5996cc59f8e5c", "guid": "bfdfe7dc352907fc980b868725387e98d37b901671fd34565b5890da0ca6b4fe", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bc1b86f89138bcdfe6ff5d70b4a88b2b", "guid": "bfdfe7dc352907fc980b868725387e98dc02dbc796bf1a1c79ffc8c7bf3cea72", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981b994616f102b96638d7b5761b9378db", "guid": "bfdfe7dc352907fc980b868725387e98ba44df36af1df9332831d2092d3efb21", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9889ace77738a9ddeafe36016c3207cf1d", "guid": "bfdfe7dc352907fc980b868725387e98fc6140a531a161534916f056bee9e636", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982ed52fc05c3c3701e88638391c09d47b", "guid": "bfdfe7dc352907fc980b868725387e9809ae3a3dcb67382c2062b3cca4aae229", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dd1faaf8baebe8ba32e6e1c43c91a30a", "guid": "bfdfe7dc352907fc980b868725387e98aae46c5cda3ab5197514ff080cd7eb15", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f8b0380333421c3b949a2c31a2966840", "guid": "bfdfe7dc352907fc980b868725387e981aa1fa0dbddb7c8034efaaa9792fbcd0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987514c8d723991f5ca67a500f6f59758b", "guid": "bfdfe7dc352907fc980b868725387e98e7f2147ad56d5599b9d706364c882c55", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9824a2244448858af9f86cf77580b1f6f0", "guid": "bfdfe7dc352907fc980b868725387e9817fa3b71b856b7baeb5ee1aed4d551ee", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c040d4f618178184a41a2d9976cc6e01", "guid": "bfdfe7dc352907fc980b868725387e98705396f4c02cbf670031f602c00c90cf", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98247ee767bac0ef8c15de655964d540a3", "guid": "bfdfe7dc352907fc980b868725387e98d89de4d90a57ad7a6c052c6354683a53", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fe82be57dafdcae1f07449391aad63ca", "guid": "bfdfe7dc352907fc980b868725387e983957f48660c55503a74fe25b4ddc4515", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e986a03fa67e33d48dce94be2b8eb2259ec", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e984451b500332c8ab38fc9c8251bc83145", "guid": "bfdfe7dc352907fc980b868725387e98947625cddc1ad6435caf2173f88dd769"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980eb254a89bdd529a277ad57c0a8d1773", "guid": "bfdfe7dc352907fc980b868725387e983c48b0e9b89580963e238435ec2fbd97"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9894b7328dfe3bfd7b4cbf850ca967c08f", "guid": "bfdfe7dc352907fc980b868725387e984fe93666bbbad78b30b7f1433bb5cb57"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b57896ec6c796409ade96943ddfb6afd", "guid": "bfdfe7dc352907fc980b868725387e98e02a4ac08fe488b5806ee14610551744"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984ec35de7db19782b3bdf0c0f9130708f", "guid": "bfdfe7dc352907fc980b868725387e988be54daee34c2b0bd4a1184d117c8c2b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985e7b32356a758f588809f0433c6af871", "guid": "bfdfe7dc352907fc980b868725387e98797048cb4f31aa3b30618ab20cf959c5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98446c7780dc464ba35edac55e19ec7fe3", "guid": "bfdfe7dc352907fc980b868725387e9833f865fb7a587163e314770dc3b54a8e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987611904ef5114e36713e5021cb5e9fc1", "guid": "bfdfe7dc352907fc980b868725387e98c205991c536c9b33d3ce65a79dc0ebb8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dba8aabbf16a9982e4100cfcb763fe61", "guid": "bfdfe7dc352907fc980b868725387e98d246c95c474ce94cbf0eb04053b7fac3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c3284421d76385ee44c722265cef1ee6", "guid": "bfdfe7dc352907fc980b868725387e98f33e88ba1acdd8534bed899fb3c93d12"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981ff90846986ee0a8fe68de692399d566", "guid": "bfdfe7dc352907fc980b868725387e9836797d5ab636bda8f1009cfd96f57bb2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98613dfd933694f265f12fdbde71d919f3", "guid": "bfdfe7dc352907fc980b868725387e985d52413007766c328259068a45a1d07b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aac807dc81c51b84ac60edc2e0ffee80", "guid": "bfdfe7dc352907fc980b868725387e981850b344567e5543096f0a83d9eac0b2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9835d07a90d27b2ad99ed709c237f05a4c", "guid": "bfdfe7dc352907fc980b868725387e9837a11a319cf79d63c3be96abf834caa9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b41d6be2a236902c2c9c07f6cb8760e9", "guid": "bfdfe7dc352907fc980b868725387e98b310da8ea2dac493993cc270e2bec7e0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989b19269a3143bb4e28dde3f5d7e90ed7", "guid": "bfdfe7dc352907fc980b868725387e98a5f3beb5a7202ad99bfb3cb857803088"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b9e433390dc4b0d5bda2b0b14e3efe08", "guid": "bfdfe7dc352907fc980b868725387e98002446de0737dff341c144279e4f348f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980c4aea538b6a4c132c03e1b060fdfb63", "guid": "bfdfe7dc352907fc980b868725387e9820b736c55349ca805381759c3f9f70a2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e9fc26698967f0170bb22a6976c3ee2a", "guid": "bfdfe7dc352907fc980b868725387e98322fe4cf0722b77e93e450d769b8bfcf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989ed63dc4152975053fe396ecbb33eafc", "guid": "bfdfe7dc352907fc980b868725387e9812c797e90c0601aff7f4ca3629c609f1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983db989ed24e906ae8c5f1c945989caa6", "guid": "bfdfe7dc352907fc980b868725387e9863c71ce1daf0d3828fb0040f81a2cb50"}], "guid": "bfdfe7dc352907fc980b868725387e98be6229230a4715433df2f8e74fbafc5b", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e987b9e1c3b6563a543b6fbe5f0759aef8f", "guid": "bfdfe7dc352907fc980b868725387e980797c2152e50219ee4196549bb34f857"}], "guid": "bfdfe7dc352907fc980b868725387e984d290968aff9eafa4ed5b85c80a8c610", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98fa0d11ed0b4e1a85c13d68e37d1547e0", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e9802f35ab680609a626ebd2ddd692a3822", "name": "permission_handler_apple-permission_handler_apple_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98ef10255b706f98e1e88fae00855b0968", "name": "permission_handler_apple", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98f8f53f8ba4165e76c7481b24262177ed", "name": "permission_handler_apple.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}