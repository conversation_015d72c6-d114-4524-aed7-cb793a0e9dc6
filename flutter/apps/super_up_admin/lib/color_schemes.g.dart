import 'package:flutter/material.dart';

const lightColorScheme = ColorScheme(
  brightness: Brightness.light,
  primary: Color(0xFF715C00),
  onPrimary: Color(0xFFFFFFFF),
  primaryContainer: Color(0xFFFFE17A),
  onPrimaryContainer: Color(0xFF231B00),
  secondary: Color(0xFF685E40),
  onSecondary: Color(0xFFFFFFFF),
  secondaryContainer: Color(0xFFF0E2BB),
  onSecondaryContainer: Color(0xFF221B04),
  tertiary: Color(0xFF45664C),
  onTertiary: Color(0xFFFFFFFF),
  tertiaryContainer: Color(0xFFC6ECCB),
  onTertiaryContainer: Color(0xFF01210D),
  error: Color(0xFFBA1A1A),
  errorContainer: Color(0xFFFFDAD6),
  onError: Color(0xFFFFFFFF),
  onErrorContainer: Color(0xFF410002),
  surface: Color(0xFFFFFBFF),
  onSurface: Color(0xFF1D1B16),
  surfaceContainerHighest: Color(0xFFEAE2CF),
  onSurfaceVariant: Color(0xFF4B4639),
  outline: Color(0xFF7D7767),
  onInverseSurface: Color(0xFFF6F0E7),
  inverseSurface: Color(0xFF33302A),
  inversePrimary: Color(0xFFE5C449),
  shadow: Color(0xFF000000),
  surfaceTint: Color(0xFF715C00),
  outlineVariant: Color(0xFFCEC6B4),
  scrim: Color(0xFF000000),
);

const darkColorScheme = ColorScheme(
  brightness: Brightness.dark,
  primary: Color(0xFFE5C449),
  onPrimary: Color(0xFF3B2F00),
  primaryContainer: Color(0xFF554500),
  onPrimaryContainer: Color(0xFFFFE17A),
  secondary: Color(0xFFD3C6A1),
  onSecondary: Color(0xFF383016),
  secondaryContainer: Color(0xFF4F462A),
  onSecondaryContainer: Color(0xFFF0E2BB),
  tertiary: Color(0xFFABD0B0),
  onTertiary: Color(0xFF173721),
  tertiaryContainer: Color(0xFF2E4E36),
  onTertiaryContainer: Color(0xFFC6ECCB),
  error: Color(0xFFFFB4AB),
  errorContainer: Color(0xFF93000A),
  onError: Color(0xFF690005),
  onErrorContainer: Color(0xFFFFDAD6),
  surface: Color(0xFF1D1B16),
  onSurface: Color(0xFFE8E2D9),
  surfaceContainerHighest: Color(0xFF4B4639),
  onSurfaceVariant: Color(0xFFCEC6B4),
  outline: Color(0xFF979080),
  onInverseSurface: Color(0xFF1D1B16),
  inverseSurface: Color(0xFFE8E2D9),
  inversePrimary: Color(0xFF715C00),
  shadow: Color(0xFF000000),
  surfaceTint: Color(0xFFE5C449),
  outlineVariant: Color(0xFF4B4639),
  scrim: Color(0xFF000000),
);
