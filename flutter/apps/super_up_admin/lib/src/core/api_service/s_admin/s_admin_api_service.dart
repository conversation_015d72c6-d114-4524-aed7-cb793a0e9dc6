// Copyright 2023, the hate<PERSON>ragab project author.
// All rights reserved. Use of this source code is governed by a
// MIT license that can be found in the LICENSE file.

import 'package:chopper/chopper.dart';
import 'package:super_up_admin/src/core/models/dash_user/peer_user_info.dart';
import 'package:super_up_admin/src/core/models/gift/admin_gift.dart';
import 'package:super_up_core/super_up_core.dart';
import 'package:v_platform/v_platform.dart';
import 'package:http/http.dart' show MultipartFile;

import '../../models/admin.dart';

import '../interceptors.dart';
import 's_admin_api.dart';

class SAdminApiService {
  SAdminApiService._();

  static SAdminApi? _sAdminApi;

  Future<MainDashBoardModel> getDashboard() async {
    final res = await _sAdminApi!.dashboard();
    throwIfNotSuccess(res);
    return MainDashBoardModel.fromMap(extractDataFromResponse(res));
  }

  Future<AppConfigModel> getConfig() async {
    final res = await _sAdminApi!.config();
    throwIfNotSuccess(res);
    return AppConfigModel.fromMap(extractDataFromResponse(res));
  }

  Future<bool> login() async {
    final res = await _sAdminApi!.login();
    throwIfNotSuccess(res);
    return extractDataFromResponse(res)['isViewer'] ?? true;
  }

  Future<PaginateModel<DashUser>> getDashUsers(
    Map<String, dynamic> query,
  ) async {
    final res = await _sAdminApi!.getDashUsers(query);
    throwIfNotSuccess(res);
    final obj = extractDataFromResponse(res);
    return PaginateModel.fromCustomMap(
      map: obj,
      values: (obj['docs'] as List).map((e) => DashUser.fromMap(e)).toList(),
    );
  }

  Future<List<AdminNotificationsModel>> getNotifications() async {
    final res = await _sAdminApi!.getNotifications();
    throwIfNotSuccess(res);
    final obj = res.body['data'] as List;
    return obj.map((e) => AdminNotificationsModel.fromMap(e)).toList();
  }

  Future<bool> createNotifications({
    required String title,
    required String desc,
    VPlatformFile? img,
  }) async {
    final res = await _sAdminApi!.createNotifications(
        img == null
            ? null
            : await VPlatforms.getMultipartFile(
                source: img,
              ),
        [
          PartValue("title", title),
          PartValue(
            "content",
            desc,
          ),
        ]);
    throwIfNotSuccess(res);
    return true;
  }

  Future<PeerUserInfo> getUserInfo(String id) async {
    final res = await _sAdminApi!.getUserInfo(id);
    throwIfNotSuccess(res);
    final obj = extractDataFromResponse(res);
    return PeerUserInfo.fromMap(obj);
  }

  Future<bool> updateUserData(String id, Map<String, dynamic> body) async {
    final res = await _sAdminApi!.updateUserData(id, body);
    throwIfNotSuccess(res);
    return true;
  }

  Future<bool> updateConfig(Map<String, dynamic> data) async {
    final res = await _sAdminApi!.updateConfig(data);
    throwIfNotSuccess(res);
    return true;
  }

  Future<AdminGroupsResponse> getUserGroups(String userId,
      {Map<String, dynamic>? filter}) async {
    final res = await _sAdminApi!.getUserGroups(userId, filter ?? {});
    throwIfNotSuccess(res);
    final obj = extractDataFromResponse(res);
    return AdminGroupsResponse.fromMap(obj);
  }

  Future<bool> deleteGroup(String groupId) async {
    final res = await _sAdminApi!.deleteGroup(groupId);
    throwIfNotSuccess(res);
    return true;
  }

  Future<AdminGroupMembersResponse> getGroupMembers(String groupId,
      {Map<String, dynamic>? filter}) async {
    final res = await _sAdminApi!.getGroupMembers(groupId, filter ?? {});
    throwIfNotSuccess(res);
    final obj = extractDataFromResponse(res);
    return AdminGroupMembersResponse.fromMap(obj);
  }

  Future<AdminStoriesResponse> getUserStories(String userId,
      {Map<String, dynamic>? filter}) async {
    final res = await _sAdminApi!.getUserStories(userId, filter ?? {});
    throwIfNotSuccess(res);
    final obj = extractDataFromResponse(res);
    return AdminStoriesResponse.fromMap(obj);
  }

  Future<bool> deleteStory(String storyId) async {
    final res = await _sAdminApi!.deleteStory(storyId);
    throwIfNotSuccess(res);
    return true;
  }

  // Gift Management Methods
  Future<AdminGiftsResponse> getGifts({Map<String, dynamic>? filter}) async {
    final res = await _sAdminApi!.getGifts(filter ?? {});
    throwIfNotSuccess(res);
    final obj = extractDataFromResponse(res);
    return AdminGiftsResponse.fromMap(obj);
  }

  Future<AdminGift> getGiftById(String giftId) async {
    final res = await _sAdminApi!.getGiftById(giftId);
    throwIfNotSuccess(res);
    final obj = extractDataFromResponse(res);
    return AdminGift.fromMap(obj);
  }

  Future<AdminGift> createGift({
    required String name,
    String? description,
    required double price,
    bool? isActive,
    VPlatformFile? imageFile,
  }) async {
    final body = <PartValue>[
      PartValue('name', name),
      PartValue('price', price.toString()),
      if (description != null) PartValue('description', description),
      if (isActive != null) PartValue('isActive', isActive.toString()),
    ];

    MultipartFile? file;
    if (imageFile != null) {
      file = await VPlatforms.getMultipartFile(source: imageFile);
    }

    final res = await _sAdminApi!.createGift(file, body);
    throwIfNotSuccess(res);
    final obj = extractDataFromResponse(res);
    return AdminGift.fromMap(obj);
  }

  Future<AdminGift> updateGift({
    required String giftId,
    String? name,
    String? description,
    double? price,
    bool? isActive,
    VPlatformFile? imageFile,
  }) async {
    final body = <PartValue>[
      if (name != null) PartValue('name', name),
      if (price != null) PartValue('price', price.toString()),
      if (description != null) PartValue('description', description),
      if (isActive != null) PartValue('isActive', isActive.toString()),
    ];

    MultipartFile? file;
    if (imageFile != null) {
      file = await VPlatforms.getMultipartFile(source: imageFile);
    }

    final res = await _sAdminApi!.updateGift(giftId, file, body);
    throwIfNotSuccess(res);
    final obj = extractDataFromResponse(res);
    return AdminGift.fromMap(obj);
  }

  Future<bool> deleteGift(String giftId) async {
    final res = await _sAdminApi!.deleteGift(giftId);
    throwIfNotSuccess(res);
    return true;
  }

  static SAdminApiService init({
    Uri? baseUrl,
    String? accessToken,
    Map<String, String>? headers,
  }) {
    _sAdminApi ??= SAdminApi.create(
      accessToken: accessToken,
      headers: headers,
      baseUrl: baseUrl ?? SConstants.sApiBaseUrl,
    );
    return SAdminApiService._();
  }
}
