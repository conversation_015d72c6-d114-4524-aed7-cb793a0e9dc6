// Copyright 2023, the hate<PERSON><PERSON>b project author.
// All rights reserved. Use of this source code is governed by a
// MIT license that can be found in the LICENSE file.

class AdminStory {
  final String storyId;
  final String? content;
  final String storyType;
  final String storyPrivacy;
  final String? caption;
  final String? backgroundColor;
  final String? textColor;
  final String? textAlign;
  final String? fontType;
  final Map<String, dynamic>? attachment;
  final int views;
  final DateTime createdAt;
  final DateTime? expireAt;

  AdminStory({
    required this.storyId,
    this.content,
    required this.storyType,
    required this.storyPrivacy,
    this.caption,
    this.backgroundColor,
    this.textColor,
    this.textAlign,
    this.fontType,
    this.attachment,
    required this.views,
    required this.createdAt,
    this.expireAt,
  });

  factory AdminStory.fromMap(Map<String, dynamic> map) {
    return AdminStory(
      storyId: map['storyId'] ?? '',
      content: map['content'],
      storyType: map['storyType'] ?? '',
      storyPrivacy: map['storyPrivacy'] ?? '',
      caption: map['caption'],
      backgroundColor: map['backgroundColor'],
      textColor: map['textColor'],
      textAlign: map['textAlign'],
      fontType: map['fontType'],
      attachment: map['attachment'] != null 
          ? Map<String, dynamic>.from(map['attachment']) 
          : null,
      views: map['views'] ?? 0,
      createdAt: DateTime.parse(map['createdAt']),
      expireAt: map['expireAt'] != null ? DateTime.parse(map['expireAt']) : null,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'storyId': storyId,
      'content': content,
      'storyType': storyType,
      'storyPrivacy': storyPrivacy,
      'caption': caption,
      'backgroundColor': backgroundColor,
      'textColor': textColor,
      'textAlign': textAlign,
      'fontType': fontType,
      'attachment': attachment,
      'views': views,
      'createdAt': createdAt.toIso8601String(),
      'expireAt': expireAt?.toIso8601String(),
    };
  }

  bool get isExpired => expireAt != null && DateTime.now().isAfter(expireAt!);
  bool get hasMedia => attachment != null && attachment!.isNotEmpty;
  String get displayContent => content ?? caption ?? 'Media Story';
}

class AdminStoriesResponse {
  final List<AdminStory> stories;
  final int totalCount;

  AdminStoriesResponse({
    required this.stories,
    required this.totalCount,
  });

  factory AdminStoriesResponse.fromMap(Map<String, dynamic> map) {
    return AdminStoriesResponse(
      stories: List<AdminStory>.from(
        map['stories']?.map((x) => AdminStory.fromMap(x)) ?? [],
      ),
      totalCount: map['totalCount'] ?? 0,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'stories': stories.map((x) => x.toMap()).toList(),
      'totalCount': totalCount,
    };
  }
}
