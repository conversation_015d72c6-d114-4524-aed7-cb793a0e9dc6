// Copyright 2023, the hatem<PERSON>b project author.
// All rights reserved. Use of this source code is governed by a
// MIT license that can be found in the LICENSE file.

class AdminGroup {
  final String groupId;
  final String groupName;
  final String groupImage;
  final String creatorId;
  final String memberRole;
  final int memberCount;
  final DateTime createdAt;
  final DateTime joinedAt;

  AdminGroup({
    required this.groupId,
    required this.groupName,
    required this.groupImage,
    required this.creatorId,
    required this.memberRole,
    required this.memberCount,
    required this.createdAt,
    required this.joinedAt,
  });

  factory AdminGroup.fromMap(Map<String, dynamic> map) {
    return AdminGroup(
      groupId: map['groupId'] ?? '',
      groupName: map['groupName'] ?? '',
      groupImage: map['groupImage'] ?? '',
      creatorId: map['creatorId'] ?? '',
      memberRole: map['memberRole'] ?? '',
      memberCount: map['memberCount'] ?? 0,
      createdAt: DateTime.parse(map['createdAt']),
      joinedAt: DateTime.parse(map['joinedAt']),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'groupId': groupId,
      'groupName': groupName,
      'groupImage': groupImage,
      'creatorId': creatorId,
      'memberRole': memberRole,
      'memberCount': memberCount,
      'createdAt': createdAt.toIso8601String(),
      'joinedAt': joinedAt.toIso8601String(),
    };
  }
}

class AdminGroupsResponse {
  final List<AdminGroup> groups;
  final int totalCount;

  AdminGroupsResponse({
    required this.groups,
    required this.totalCount,
  });

  factory AdminGroupsResponse.fromMap(Map<String, dynamic> map) {
    return AdminGroupsResponse(
      groups: List<AdminGroup>.from(
        map['groups']?.map((x) => AdminGroup.fromMap(x)) ?? [],
      ),
      totalCount: map['totalCount'] ?? 0,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'groups': groups.map((x) => x.toMap()).toList(),
      'totalCount': totalCount,
    };
  }
}
