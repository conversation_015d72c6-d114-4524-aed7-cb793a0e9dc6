// Copyright 2023, the hate<PERSON>ragab project author.
// All rights reserved. Use of this source code is governed by a
// MIT license that can be found in the LICENSE file.

class AdminGroupMember {
  final String userId;
  final Map<String, dynamic> userData;
  final String role;
  final DateTime joinedAt;

  AdminGroupMember({
    required this.userId,
    required this.userData,
    required this.role,
    required this.joinedAt,
  });

  factory AdminGroupMember.fromMap(Map<String, dynamic> map) {
    return AdminGroupMember(
      userId: map['userId'] ?? '',
      userData: Map<String, dynamic>.from(map['userData'] ?? {}),
      role: map['role'] ?? '',
      joinedAt: DateTime.parse(map['joinedAt']),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'userId': userId,
      'userData': userData,
      'role': role,
      'joinedAt': joinedAt.toIso8601String(),
    };
  }

  String get fullName => userData['fullName'] ?? 'Unknown User';
  String get userImage => userData['userImage'] ?? '';
  bool get isVerified => userData['verifiedAt'] != null;
  String get registerStatus => userData['registerStatus'] ?? '';
  bool get isBanned => userData['banTo'] != null;
}

class AdminGroupMembersResponse {
  final List<AdminGroupMember> members;
  final int totalCount;

  AdminGroupMembersResponse({
    required this.members,
    required this.totalCount,
  });

  factory AdminGroupMembersResponse.fromMap(Map<String, dynamic> map) {
    return AdminGroupMembersResponse(
      members: List<AdminGroupMember>.from(
        map['members']?.map((x) => AdminGroupMember.fromMap(x)) ?? [],
      ),
      totalCount: map['totalCount'] ?? 0,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'members': members.map((x) => x.toMap()).toList(),
      'totalCount': totalCount,
    };
  }
}
