// Copyright 2023, the hate<PERSON><PERSON>b project author.
// All rights reserved. Use of this source code is governed by a
// MIT license that can be found in the LICENSE file.

class AdminGift {
  final String id;
  final String? name;
  final String? description;
  final String imageUrl;
  final double price;
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;

  AdminGift({
    required this.id,
    this.name,
    this.description,
    required this.imageUrl,
    required this.price,
    required this.isActive,
    required this.createdAt,
    required this.updatedAt,
  });

  factory AdminGift.fromMap(Map<String, dynamic> map) {
    return AdminGift(
      id: map['id'] ?? '',
      name: map['name'],
      description: map['description'],
      imageUrl: map['imageUrl'] ?? '',
      price: (map['price'] ?? 0).toDouble(),
      isActive: map['isActive'] ?? true,
      createdAt: DateTime.parse(map['createdAt']),
      updatedAt: DateTime.parse(map['updatedAt']),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'imageUrl': imageUrl,
      'price': price,
      'isActive': isActive,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  AdminGift copyWith({
    String? id,
    String? name,
    String? description,
    String? imageUrl,
    double? price,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return AdminGift(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      imageUrl: imageUrl ?? this.imageUrl,
      price: price ?? this.price,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}

class AdminGiftsResponse {
  final List<AdminGift> gifts;
  final int totalCount;

  AdminGiftsResponse({
    required this.gifts,
    required this.totalCount,
  });

  factory AdminGiftsResponse.fromMap(Map<String, dynamic> map) {
    return AdminGiftsResponse(
      gifts: List<AdminGift>.from(
        map['gifts']?.map((x) => AdminGift.fromMap(x)) ?? [],
      ),
      totalCount: map['totalCount'] ?? 0,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'gifts': gifts.map((x) => x.toMap()).toList(),
      'totalCount': totalCount,
    };
  }
}
