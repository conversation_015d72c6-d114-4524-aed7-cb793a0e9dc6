// Copyright 2023, the hate<PERSON><PERSON>b project author.
// All rights reserved. Use of this source code is governed by a
// MIT license that can be found in the LICENSE file.

import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:s_translation/generated/l10n.dart';
import 'package:super_up_admin/main.dart';
import 'package:super_up_admin/src/core/api_service/api_service.dart';
import 'package:super_up_core/super_up_core.dart';
import 'package:v_platform/v_platform.dart';

import '../../../../core/models/dash_user/peer_user_info.dart';
import '../../../../core/models/admin.dart';

class InfoTile {
  final String title;
  final String value;

  InfoTile(this.title, this.value);
}

class UserProfileController extends SLoadingController<PeerUserInfo> {
  final String userID;
  final _adminApiService = GetIt.I.get<SAdminApiService>();

  UserProfileController(this.userID)
      : super(SLoadingState(PeerUserInfo.init()));
  final messages = <InfoTile>[];
  final rooms = <InfoTile>[];
  final userGroups = <AdminGroup>[];
  final userStories = <AdminStory>[];

  @override
  void onClose() {}

  BuildContext get context => navigatorKey.currentState!.context;

  @override
  void onInit() {
    getData();
  }

  void getData() async {
    await vSafeApiCall<PeerUserInfo>(
      onLoading: () {
        setStateLoading();
      },
      request: () async {
        return await _adminApiService.getUserInfo(userID);
      },
      onSuccess: (response) {
        value.data = response;
        _setMessages();
        _setRoomCounter();
        _loadUserGroups();
        _loadUserStories();
        setStateSuccess();
      },
      onError: (exception, trace) {
        setStateError(exception);
      },
    );
  }

  void _setMessages() {
    messages.clear();
    messages.addAll(
      [
        InfoTile(
          S.of(context).totalMessages,
          value.data.messagesCounter.messages.toString(),
        ),
        InfoTile(
          S.of(context).allDeletedMessages,
          value.data.messagesCounter.allDeletedMessages.toString(),
        ),
        InfoTile(
          S.of(context).voiceCallMessages,
          value.data.messagesCounter.voiceMessages.toString(),
        ),
        InfoTile(
          S.of(context).videoCallMessages,
          value.data.messagesCounter.videoCallMessages.toString(),
        ),
        InfoTile(
          S.of(context).fileMessages,
          value.data.messagesCounter.fileMessages.toString(),
        ),
        InfoTile(
          S.of(context).infoMessages,
          value.data.messagesCounter.infoMessages.toString(),
        ),
        InfoTile(
          S.of(context).locationMessages,
          value.data.messagesCounter.locationMessages.toString(),
        ),
        InfoTile(
          S.of(context).imageMessages,
          value.data.messagesCounter.imageMessages.toString(),
        ),
        InfoTile(
          S.of(context).videoMessages,
          value.data.messagesCounter.videoMessages.toString(),
        ),
        InfoTile(
          S.of(context).voiceMessages,
          value.data.messagesCounter.voiceMessages.toString(),
        ),
      ],
    );
  }

  void _setRoomCounter() {
    rooms.clear();
    rooms.addAll(
      [
        InfoTile(
          S.of(context).totalRooms,
          value.data.roomCounter.total.toString(),
        ),
        InfoTile(
          S.of(context).directRooms,
          value.data.roomCounter.single.toString(),
        ),
        InfoTile(
          S.of(context).group,
          value.data.roomCounter.group.toString(),
        ),
        InfoTile(
          S.of(context).broadcast,
          value.data.roomCounter.broadcast.toString(),
        ),
      ],
    );
  }

  void blockUser(BuildContext context) async {
    final res = await VAppAlert.showAskYesNoDialog(
      context: context,
      title: S.of(context).userAction,
      content: S.of(context).areYouSure,
    );
    if (res != 1) return;
    await vSafeApiCall(
      onLoading: () {
        setStateLoading();
      },
      request: () async {
        if (value.data.user.banTo != null) {
          return await _adminApiService.updateUserData(userID, {
            "banTo": null,
          });
        }
        return await _adminApiService.updateUserData(userID, {
          "banTo": DateTime.now().toUtc().toIso8601String(),
        });
      },
      onSuccess: (response) {},
      onError: (exception, trace) {},
    );
    getData();
  }

  void deleteUser(BuildContext context) async {
    final res = await VAppAlert.showAskYesNoDialog(
      context: context,
      title: S.of(context).userAction,
      content: S.of(context).areYouSure,
    );
    if (res != 1) return;
    await vSafeApiCall(
      onLoading: () {
        setStateLoading();
      },
      request: () async {
        if (value.data.user.deletedAt != null) {
          return await _adminApiService.updateUserData(userID, {
            "deletedAt": null,
          });
        }
        return await _adminApiService.updateUserData(userID, {
          "deletedAt": DateTime.now().toUtc().toIso8601String(),
        });
      },
      onSuccess: (response) {},
      onError: (exception, trace) {},
    );
    getData();
  }

  primeUser(BuildContext context) async {
    final res = await VAppAlert.showAskYesNoDialog(
      context: context,
      title: S.of(context).userAction,
      content: S.of(context).areYouSure,
    );
    if (res != 1) return;
    await vSafeApiCall(
      onLoading: () {
        setStateLoading();
      },
      request: () async {
        if (value.data.user.isPrime) {
          return await _adminApiService.updateUserData(userID, {
            "isPrime": false,
          });
        }
        return await _adminApiService.updateUserData(userID, {
          "isPrime": true,
        });
      },
      onSuccess: (response) {},
      onError: (exception, trace) {
        print(exception);
        print(trace);
      },
    );
    getData();
  }

  hasBadgeUser(BuildContext context) async {
    final res = await VAppAlert.showAskYesNoDialog(
      context: context,
      title: S.of(context).userAction,
      content: S.of(context).areYouSure,
    );
    if (res != 1) return;
    await vSafeApiCall(
      onLoading: () {
        setStateLoading();
      },
      request: () async {
        if (value.data.user.hasBadge) {
          return await _adminApiService.updateUserData(userID, {
            "hasBadge": false,
          });
        }
        return await _adminApiService.updateUserData(userID, {
          "hasBadge": true,
        });
      },
      onSuccess: (response) {},
      onError: (exception, trace) {},
    );
    getData();
  }

  acceptUser(BuildContext context) async {
    final res = await VAppAlert.showAskYesNoDialog(
      context: context,
      title: S.of(context).userAction,
      content: S.of(context).areYouSure,
    );
    if (res != 1) return;
    await vSafeApiCall(
      onLoading: () {
        setStateLoading();
      },
      request: () async {
        return await _adminApiService.updateUserData(userID, {
          "registerStatus": "accepted",
        });
      },
      onSuccess: (response) {},
      onError: (exception, trace) {},
    );
    getData();
  }

  void _loadUserGroups() async {
    try {
      final response = await _adminApiService.getUserGroups(userID);
      userGroups.clear();
      userGroups.addAll(response.groups);
      update();
    } catch (e) {
      // Handle error silently for now
    }
  }

  void _loadUserStories() async {
    try {
      final response = await _adminApiService.getUserStories(userID);
      userStories.clear();
      userStories.addAll(response.stories);
      update();
    } catch (e) {
      // Handle error silently for now
    }
  }

  void deleteGroup(BuildContext context, String groupId) async {
    final res = await VAppAlert.showAskYesNoDialog(
      context: context,
      title: "Delete Group",
      content:
          "Are you sure you want to delete this group? This action cannot be undone.",
    );
    if (res != 1) return;

    await vSafeApiCall(
      onLoading: () {
        setStateLoading();
      },
      request: () async {
        return await _adminApiService.deleteGroup(groupId);
      },
      onSuccess: (response) {
        VAppAlert.showSuccessSnackBar(
          context: context,
          message: "Group deleted successfully",
        );
        _loadUserGroups(); // Refresh groups list
      },
      onError: (exception, trace) {
        VAppAlert.showErrorSnackBar(
          context: context,
          message: "Failed to delete group: $exception",
        );
      },
    );
  }

  void deleteStory(BuildContext context, String storyId) async {
    final res = await VAppAlert.showAskYesNoDialog(
      context: context,
      title: "Delete Story",
      content:
          "Are you sure you want to delete this story? This action cannot be undone.",
    );
    if (res != 1) return;

    await vSafeApiCall(
      onLoading: () {
        setStateLoading();
      },
      request: () async {
        return await _adminApiService.deleteStory(storyId);
      },
      onSuccess: (response) {
        VAppAlert.showSuccessSnackBar(
          context: context,
          message: "Story deleted successfully",
        );
        _loadUserStories(); // Refresh stories list
      },
      onError: (exception, trace) {
        VAppAlert.showErrorSnackBar(
          context: context,
          message: "Failed to delete story: $exception",
        );
      },
    );
  }

  void showGroupMembers(
      BuildContext context, String groupId, String groupName) async {
    try {
      final response = await _adminApiService.getGroupMembers(groupId);

      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: Text("$groupName Members"),
          content: SizedBox(
            width: double.maxFinite,
            height: 400,
            child: response.members.isEmpty
                ? const Center(child: Text("No members found"))
                : ListView.builder(
                    itemCount: response.members.length,
                    itemBuilder: (context, index) {
                      final member = response.members[index];
                      return ListTile(
                        leading: VCircleAvatar(
                          vFileSource: VPlatformFile.fromUrl(
                            networkUrl: member.userImage,
                          ),
                          radius: 20,
                        ),
                        title: Text(member.fullName),
                        subtitle: Text("Role: ${member.role}"),
                        trailing: member.isBanned
                            ? const Icon(Icons.block, color: Colors.red)
                            : member.isVerified
                                ? const Icon(Icons.verified, color: Colors.blue)
                                : null,
                      );
                    },
                  ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text("Close"),
            ),
          ],
        ),
      );
    } catch (e) {
      VAppAlert.showErrorSnackBar(
        context: context,
        message: "Failed to load group members: $e",
      );
    }
  }
}
