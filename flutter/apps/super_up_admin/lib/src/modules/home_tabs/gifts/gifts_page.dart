// Copyright 2023, the hatem<PERSON>b project author.
// All rights reserved. Use of this source code is governed by a
// MIT license that can be found in the LICENSE file.

import 'package:flutter/material.dart';
import 'package:super_up_admin/src/core/models/gift/admin_gift.dart';
import 'package:super_up_admin/src/modules/home_tabs/gifts/gifts_controller.dart';
import 'package:super_up_core/super_up_core.dart';

class GiftsPage extends StatefulWidget {
  const GiftsPage({super.key});

  @override
  State<GiftsPage> createState() => _GiftsPageState();
}

class _GiftsPageState extends State<GiftsPage> {
  late final GiftsController controller;

  @override
  void initState() {
    super.initState();
    controller = GiftsController();
    controller.onInit();
  }

  @override
  void dispose() {
    controller.onClose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: controller,
      builder: (context, child) {
        return Scaffold(
          appBar: AppBar(
            title: const Text('Gifts Management'),
            actions: [
              IconButton(
                onPressed: () => controller.showCreateGiftDialog(context),
                icon: const Icon(Icons.add),
                tooltip: 'Add New Gift',
              ),
              IconButton(
                onPressed: controller.loadGifts,
                icon: const Icon(Icons.refresh),
                tooltip: 'Refresh',
              ),
            ],
          ),
          body: _buildBody(),
        );
      },
    );
  }

  Widget _buildBody() {
    return ValueListenableBuilder(
      valueListenable: controller,
      builder: (context, value, child) {
        if (value.loadingState == VChatLoadingState.loading) {
          return const Center(
            child: CircularProgressIndicator(),
          );
        }

        if (value.loadingState == VChatLoadingState.error) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(Icons.error, size: 64, color: Colors.red),
                const SizedBox(height: 16),
                const Text('Error loading gifts'),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: controller.loadGifts,
                  child: const Text('Retry'),
                ),
              ],
            ),
          );
        }

        final gifts = controller.data;
        if (gifts.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(Icons.card_giftcard, size: 64, color: Colors.grey),
                const SizedBox(height: 16),
                const Text(
                  'No gifts found',
                  style: TextStyle(fontSize: 18, color: Colors.grey),
                ),
                const SizedBox(height: 16),
                ElevatedButton.icon(
                  onPressed: () => controller.showCreateGiftDialog(context),
                  icon: const Icon(Icons.add),
                  label: const Text('Create First Gift'),
                ),
              ],
            ),
          );
        }

        return RefreshIndicator(
          onRefresh: controller.loadGifts,
          child: LayoutBuilder(
            builder: (context, constraints) {
              // Calculate responsive grid
              int crossAxisCount;
              double childAspectRatio;

              if (constraints.maxWidth < 600) {
                // Mobile: 2 columns
                crossAxisCount = 2;
                childAspectRatio = 0.75;
              } else if (constraints.maxWidth < 900) {
                // Tablet: 3 columns
                crossAxisCount = 3;
                childAspectRatio = 0.8;
              } else if (constraints.maxWidth < 1200) {
                // Small desktop: 4 columns
                crossAxisCount = 4;
                childAspectRatio = 0.8;
              } else {
                // Large desktop: 5 columns
                crossAxisCount = 5;
                childAspectRatio = 0.85;
              }

              return GridView.builder(
                padding: const EdgeInsets.all(16),
                gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: crossAxisCount,
                  crossAxisSpacing: 16,
                  mainAxisSpacing: 16,
                  childAspectRatio: childAspectRatio,
                ),
                itemCount: gifts.length,
                itemBuilder: (context, index) {
                  final gift = gifts[index];
                  return GiftCard(
                    gift: gift,
                    onEdit: () => controller.showEditGiftDialog(context, gift),
                    onDelete: () =>
                        controller.showDeleteConfirmation(context, gift),
                  );
                },
              );
            },
          ),
        );
      },
    );
  }
}

class GiftCard extends StatelessWidget {
  final AdminGift gift;
  final VoidCallback onEdit;
  final VoidCallback onDelete;

  const GiftCard({
    super.key,
    required this.gift,
    required this.onEdit,
    required this.onDelete,
  });

  String _getFullImageUrl(String imageKey) {
    if (imageKey.startsWith('http')) {
      return imageKey; // Already a full URL
    }
    // Construct full URL: baseMediaUrl + imageKey
    final baseUrl = SConstants.baseMediaUrl;
    return '$baseUrl$imageKey';
  }

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final isSmall = constraints.maxWidth < 150;

        return Card(
          elevation: 4,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Gift Image
              Expanded(
                flex: 3,
                child: Container(
                  width: double.infinity,
                  decoration: BoxDecoration(
                    borderRadius:
                        const BorderRadius.vertical(top: Radius.circular(12)),
                    color: Colors.grey[200],
                  ),
                  child: ClipRRect(
                    borderRadius:
                        const BorderRadius.vertical(top: Radius.circular(12)),
                    child: gift.imageUrl.isNotEmpty
                        ? Image.network(
                            _getFullImageUrl(gift.imageUrl),
                            fit: BoxFit.cover,
                            errorBuilder: (context, error, stackTrace) {
                              return Center(
                                child: Icon(Icons.broken_image,
                                    size: isSmall ? 24 : 40,
                                    color: Colors.grey),
                              );
                            },
                            loadingBuilder: (context, child, loadingProgress) {
                              if (loadingProgress == null) return child;
                              return const Center(
                                child: CircularProgressIndicator(),
                              );
                            },
                          )
                        : Center(
                            child: Icon(Icons.card_giftcard,
                                size: isSmall ? 24 : 40, color: Colors.grey),
                          ),
                  ),
                ),
              ),

              // Gift Info
              Expanded(
                flex: 2,
                child: Padding(
                  padding: EdgeInsets.all(isSmall ? 6 : 8),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // Name and Status
                      Flexible(
                        child: Row(
                          children: [
                            Expanded(
                              child: Text(
                                gift.name ??
                                    '\$${gift.price.toStringAsFixed(2)} Gift',
                                style: TextStyle(
                                  fontWeight: FontWeight.bold,
                                  fontSize: isSmall ? 10 : 12,
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                            if (!isSmall) ...[
                              const SizedBox(width: 2),
                              Container(
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 3, vertical: 1),
                                decoration: BoxDecoration(
                                  color:
                                      gift.isActive ? Colors.green : Colors.red,
                                  borderRadius: BorderRadius.circular(6),
                                ),
                                child: Text(
                                  gift.isActive ? 'Active' : 'Inactive',
                                  style: const TextStyle(
                                    color: Colors.white,
                                    fontSize: 7,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                            ],
                          ],
                        ),
                      ),
                      SizedBox(height: isSmall ? 1 : 2),

                      // Price
                      Text(
                        '\$${gift.price.toStringAsFixed(2)}',
                        style: TextStyle(
                          fontSize: isSmall ? 12 : 14,
                          fontWeight: FontWeight.bold,
                          color: Colors.green,
                        ),
                      ),

                      SizedBox(height: isSmall ? 2 : 4),

                      // Action Buttons
                      Row(
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          IconButton(
                            onPressed: onEdit,
                            icon: Icon(Icons.edit, size: isSmall ? 12 : 14),
                            tooltip: 'Edit Gift',
                            constraints: const BoxConstraints(
                              minWidth: 24,
                              minHeight: 24,
                            ),
                            padding: EdgeInsets.all(isSmall ? 1 : 2),
                          ),
                          IconButton(
                            onPressed: onDelete,
                            icon: Icon(Icons.delete,
                                size: isSmall ? 12 : 14, color: Colors.red),
                            tooltip: 'Delete Gift',
                            constraints: const BoxConstraints(
                              minWidth: 24,
                              minHeight: 24,
                            ),
                            padding: EdgeInsets.all(isSmall ? 1 : 2),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
