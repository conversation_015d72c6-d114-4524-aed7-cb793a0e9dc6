name: super_up_admin
description: Super up admin

# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter


  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8
  responsive_framework: ^1.5.1
  get_it: ^8.0.2
  flex_color_scheme: ^7.3.1
  http: ^1.2.2
  super_up_core:
    path: ../../packages/super_up_core
  passwordfield: ^0.2.0
  sidebarx: ^0.17.1
  settings_ui: ^2.0.2
  url_strategy: ^0.3.0
  responsive_table: ^0.2.0+2
  number_paginator: ^0.4.1
  percent_indicator: ^4.2.3
  data_table_2: ^2.5.15
  desktop_window: ^0.4.2
  auto_animated: ^3.2.0
  google_fonts: ^6.2.1
  chopper: ^8.0.3
  v_platform: ^2.1.4
  v_chat_sdk_core:
    path: ../../packages/v_chat_sdk_core
  s_translation:
    path: ../../packages/s_translation
  flutter_svg: ^2.0.11
  pluto_grid: ^8.0.0
#  pluto_grid_export: ^1.0.6
  intl: ^0.19.0
  flutter_localizations:
    sdk: flutter
  numeral: ^3.0.0
  adaptive_dialog: ^2.2.1+2
  modal_bottom_sheet: ^3.0.0
  flutter_native_splash: ^2.4.2
  badges: ^3.1.2
dev_dependencies:
  flutter_lints: ^5.0.0
  build_runner: ^2.4.13
  chopper_generator: ^8.0.3
  flutter_launcher_icons: ^0.14.1
flutter:
  assets:
    - assets/
  uses-material-design: true
  generate: true

flutter_icons:
  android: ic_launcher
  image_path: assets/logo.png
  ios: true
  remove_alpha_ios: true
  #  adaptive_icon_foreground: true
  min_sdk_android: 21 # android min sdk min:16, default 21
  web:
    generate: true
    image_path: "assets/logo.png"
    background_color: "#hexcode"
    theme_color: "#hexcode"
  windows:
    generate: true
    image_path: "assets/logo.png"
    icon_size: 48 # min:48, max:256, default: 48
  macos:
    generate: true
    image_path: "assets/logo.png"
