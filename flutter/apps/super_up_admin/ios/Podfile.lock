PODS:
  - audio_service (0.0.1):
    - Flutter
    - FlutterMacOS
  - audio_session (0.0.1):
    - Flutter
  - background_downloader (0.0.1):
    - Flutter
  - camera_avfoundation (0.0.1):
    - Flutter
  - connectivity_plus (0.0.1):
    - Flutter
    - FlutterMacOS
  - CryptoSwift (1.8.4)
  - device_info_plus (0.0.1):
    - Flutter
  - DKImagePickerController/Core (4.3.9):
    - DKImagePickerController/ImageDataManager
    - DKImagePickerController/Resource
  - DKImagePickerController/ImageDataManager (4.3.9)
  - DKImagePickerController/PhotoGallery (4.3.9):
    - DKImagePickerController/Core
    - DKPhotoGallery
  - DKImagePickerController/Resource (4.3.9)
  - DKPhotoGallery (0.0.19):
    - DKPhotoGallery/Core (= 0.0.19)
    - DKPhotoGallery/Model (= 0.0.19)
    - DKPhotoGallery/Preview (= 0.0.19)
    - DKPhotoGallery/Resource (= 0.0.19)
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Core (0.0.19):
    - DKPhotoGallery/Model
    - DKPhotoGallery/Preview
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Model (0.0.19):
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Preview (0.0.19):
    - DKPhotoGallery/Model
    - DKPhotoGallery/Resource
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Resource (0.0.19):
    - SDWebImage
    - SwiftyGif
  - file_picker (0.0.1):
    - DKImagePickerController/PhotoGallery
    - Flutter
  - file_saver (0.0.1):
    - Flutter
  - Flutter (1.0.0)
  - flutter_callkit_incoming (0.0.1):
    - CryptoSwift
    - Flutter
  - flutter_fgbg (0.0.1):
    - Flutter
  - flutter_local_notifications (0.0.1):
    - Flutter
  - flutter_native_splash (0.0.1):
    - Flutter
  - gal (1.0.0):
    - Flutter
    - FlutterMacOS
  - Google-Mobile-Ads-SDK (11.10.0):
    - GoogleUserMessagingPlatform (>= 1.1)
  - google_mobile_ads (5.2.0):
    - Flutter
    - Google-Mobile-Ads-SDK (~> 11.10.0)
    - webview_flutter_wkwebview
  - GoogleUserMessagingPlatform (3.0.0)
  - image_cropper (0.0.4):
    - Flutter
    - TOCropViewController (~> 2.7.4)
  - image_picker_ios (0.0.1):
    - Flutter
  - just_audio (0.0.1):
    - Flutter
    - FlutterMacOS
  - map_launcher (0.0.1):
    - Flutter
  - package_info_plus (0.4.5):
    - Flutter
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - permission_handler_apple (9.3.0):
    - Flutter
  - photo_manager (2.0.0):
    - Flutter
    - FlutterMacOS
  - SDWebImage (5.21.1):
    - SDWebImage/Core (= 5.21.1)
  - SDWebImage/Core (5.21.1)
  - sensors_plus (0.0.1):
    - Flutter
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - sqflite_darwin (0.0.4):
    - Flutter
    - FlutterMacOS
  - sqlite3 (3.47.2):
    - sqlite3/common (= 3.47.2)
  - sqlite3/common (3.47.2)
  - sqlite3/dbstatvtab (3.47.2):
    - sqlite3/common
  - sqlite3/fts5 (3.47.2):
    - sqlite3/common
  - sqlite3/perf-threadsafe (3.47.2):
    - sqlite3/common
  - sqlite3/rtree (3.47.2):
    - sqlite3/common
  - sqlite3_flutter_libs (0.0.1):
    - Flutter
    - sqlite3 (~> 3.47.0)
    - sqlite3/dbstatvtab
    - sqlite3/fts5
    - sqlite3/perf-threadsafe
    - sqlite3/rtree
  - SwiftyGif (5.4.5)
  - TOCropViewController (2.7.4)
  - url_launcher_ios (0.0.1):
    - Flutter
  - video_player_avfoundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - wakelock_plus (0.0.1):
    - Flutter
  - webview_flutter_wkwebview (0.0.1):
    - Flutter
    - FlutterMacOS

DEPENDENCIES:
  - audio_service (from `.symlinks/plugins/audio_service/darwin`)
  - audio_session (from `.symlinks/plugins/audio_session/ios`)
  - background_downloader (from `.symlinks/plugins/background_downloader/ios`)
  - camera_avfoundation (from `.symlinks/plugins/camera_avfoundation/ios`)
  - connectivity_plus (from `.symlinks/plugins/connectivity_plus/darwin`)
  - device_info_plus (from `.symlinks/plugins/device_info_plus/ios`)
  - file_picker (from `.symlinks/plugins/file_picker/ios`)
  - file_saver (from `.symlinks/plugins/file_saver/ios`)
  - Flutter (from `Flutter`)
  - flutter_callkit_incoming (from `.symlinks/plugins/flutter_callkit_incoming/ios`)
  - flutter_fgbg (from `.symlinks/plugins/flutter_fgbg/ios`)
  - flutter_local_notifications (from `.symlinks/plugins/flutter_local_notifications/ios`)
  - flutter_native_splash (from `.symlinks/plugins/flutter_native_splash/ios`)
  - gal (from `.symlinks/plugins/gal/darwin`)
  - google_mobile_ads (from `.symlinks/plugins/google_mobile_ads/ios`)
  - image_cropper (from `.symlinks/plugins/image_cropper/ios`)
  - image_picker_ios (from `.symlinks/plugins/image_picker_ios/ios`)
  - just_audio (from `.symlinks/plugins/just_audio/darwin`)
  - map_launcher (from `.symlinks/plugins/map_launcher/ios`)
  - package_info_plus (from `.symlinks/plugins/package_info_plus/ios`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)
  - permission_handler_apple (from `.symlinks/plugins/permission_handler_apple/ios`)
  - photo_manager (from `.symlinks/plugins/photo_manager/ios`)
  - sensors_plus (from `.symlinks/plugins/sensors_plus/ios`)
  - shared_preferences_foundation (from `.symlinks/plugins/shared_preferences_foundation/darwin`)
  - sqflite_darwin (from `.symlinks/plugins/sqflite_darwin/darwin`)
  - sqlite3_flutter_libs (from `.symlinks/plugins/sqlite3_flutter_libs/ios`)
  - url_launcher_ios (from `.symlinks/plugins/url_launcher_ios/ios`)
  - video_player_avfoundation (from `.symlinks/plugins/video_player_avfoundation/darwin`)
  - wakelock_plus (from `.symlinks/plugins/wakelock_plus/ios`)
  - webview_flutter_wkwebview (from `.symlinks/plugins/webview_flutter_wkwebview/darwin`)

SPEC REPOS:
  trunk:
    - CryptoSwift
    - DKImagePickerController
    - DKPhotoGallery
    - Google-Mobile-Ads-SDK
    - GoogleUserMessagingPlatform
    - SDWebImage
    - sqlite3
    - SwiftyGif
    - TOCropViewController

EXTERNAL SOURCES:
  audio_service:
    :path: ".symlinks/plugins/audio_service/darwin"
  audio_session:
    :path: ".symlinks/plugins/audio_session/ios"
  background_downloader:
    :path: ".symlinks/plugins/background_downloader/ios"
  camera_avfoundation:
    :path: ".symlinks/plugins/camera_avfoundation/ios"
  connectivity_plus:
    :path: ".symlinks/plugins/connectivity_plus/darwin"
  device_info_plus:
    :path: ".symlinks/plugins/device_info_plus/ios"
  file_picker:
    :path: ".symlinks/plugins/file_picker/ios"
  file_saver:
    :path: ".symlinks/plugins/file_saver/ios"
  Flutter:
    :path: Flutter
  flutter_callkit_incoming:
    :path: ".symlinks/plugins/flutter_callkit_incoming/ios"
  flutter_fgbg:
    :path: ".symlinks/plugins/flutter_fgbg/ios"
  flutter_local_notifications:
    :path: ".symlinks/plugins/flutter_local_notifications/ios"
  flutter_native_splash:
    :path: ".symlinks/plugins/flutter_native_splash/ios"
  gal:
    :path: ".symlinks/plugins/gal/darwin"
  google_mobile_ads:
    :path: ".symlinks/plugins/google_mobile_ads/ios"
  image_cropper:
    :path: ".symlinks/plugins/image_cropper/ios"
  image_picker_ios:
    :path: ".symlinks/plugins/image_picker_ios/ios"
  just_audio:
    :path: ".symlinks/plugins/just_audio/darwin"
  map_launcher:
    :path: ".symlinks/plugins/map_launcher/ios"
  package_info_plus:
    :path: ".symlinks/plugins/package_info_plus/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"
  permission_handler_apple:
    :path: ".symlinks/plugins/permission_handler_apple/ios"
  photo_manager:
    :path: ".symlinks/plugins/photo_manager/ios"
  sensors_plus:
    :path: ".symlinks/plugins/sensors_plus/ios"
  shared_preferences_foundation:
    :path: ".symlinks/plugins/shared_preferences_foundation/darwin"
  sqflite_darwin:
    :path: ".symlinks/plugins/sqflite_darwin/darwin"
  sqlite3_flutter_libs:
    :path: ".symlinks/plugins/sqlite3_flutter_libs/ios"
  url_launcher_ios:
    :path: ".symlinks/plugins/url_launcher_ios/ios"
  video_player_avfoundation:
    :path: ".symlinks/plugins/video_player_avfoundation/darwin"
  wakelock_plus:
    :path: ".symlinks/plugins/wakelock_plus/ios"
  webview_flutter_wkwebview:
    :path: ".symlinks/plugins/webview_flutter_wkwebview/darwin"

SPEC CHECKSUMS:
  audio_service: aa99a6ba2ae7565996015322b0bb024e1d25c6fd
  audio_session: 9bb7f6c970f21241b19f5a3658097ae459681ba0
  background_downloader: b0572309a68d929c35941f0484e8ad7c65052228
  camera_avfoundation: 04b44aeb14070126c6529e5ab82cc7c9fca107cf
  connectivity_plus: b21496ab28d1324eb59885d888a4d83b98531f01
  CryptoSwift: e64e11850ede528a02a0f3e768cec8e9d92ecb90
  device_info_plus: 21fcca2080fbcd348be798aa36c3e5ed849eefbe
  DKImagePickerController: 946cec48c7873164274ecc4624d19e3da4c1ef3c
  DKPhotoGallery: b3834fecb755ee09a593d7c9e389d8b5d6deed60
  file_picker: 9b3292d7c8bc68c8a7bf8eb78f730e49c8efc517
  file_saver: 6cdbcddd690cb02b0c1a0c225b37cd805c2bf8b6
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  flutter_callkit_incoming: cb8138af67cda6dd981f7101a5d709003af21502
  flutter_fgbg: d3da78df78454b1808f0829a5da9cd17dfe16444
  flutter_local_notifications: 395056b3175ba4f08480a7c5de30cd36d69827e4
  flutter_native_splash: 35ddbc7228eafcb3969dcc5f1fbbe27c1145a4f0
  gal: 44e5b10dbd347c8247a2851acee6c1fbe282c1d3
  Google-Mobile-Ads-SDK: 13e6e98edfd78ad8d8a791edb927658cc260a56f
  google_mobile_ads: dc2b2a5884bef7ab2b4ff30022a513df5373e208
  GoogleUserMessagingPlatform: f8d0cdad3ca835406755d0a69aa634f00e76d576
  image_cropper: 5f162dcf988100dc1513f9c6b7eb42cd6fbf9156
  image_picker_ios: 7fe1ff8e34c1790d6fff70a32484959f563a928a
  just_audio: 4e391f57b79cad2b0674030a00453ca5ce817eed
  map_launcher: fe43bda6720bb73c12fcc1bdd86123ff49a4d4d6
  package_info_plus: af8e2ca6888548050f16fa2f1938db7b5a5df499
  path_provider_foundation: 080d55be775b7414fd5a5ef3ac137b97b097e564
  permission_handler_apple: 4ed2196e43d0651e8ff7ca3483a069d469701f2d
  photo_manager: d2fbcc0f2d82458700ee6256a15018210a81d413
  SDWebImage: f29024626962457f3470184232766516dee8dfea
  sensors_plus: 3c3bac724a2128c895623e03efd82cf0e94fd8e8
  shared_preferences_foundation: 9e1978ff2562383bd5676f64ec4e9aa8fa06a6f7
  sqflite_darwin: 44bb54cc302bff1fbe5752293aba1820b157cf1c
  sqlite3: 7559e33dae4c78538df563795af3a86fc887ee71
  sqlite3_flutter_libs: 4dd5d78cb764bc095baa5af79dcee907d05a21a1
  SwiftyGif: 706c60cf65fa2bc5ee0313beece843c8eb8194d4
  TOCropViewController: 80b8985ad794298fb69d3341de183f33d1853654
  url_launcher_ios: 694010445543906933d732453a59da0a173ae33d
  video_player_avfoundation: 2cef49524dd1f16c5300b9cd6efd9611ce03639b
  wakelock_plus: fd58c82b1388f4afe3fe8aa2c856503a262a5b03
  webview_flutter_wkwebview: 44d4dee7d7056d5ad185d25b38404436d56c547c

PODFILE CHECKSUM: a57f30d18f102dd3ce366b1d62a55ecbef2158e5

COCOAPODS: 1.16.2
