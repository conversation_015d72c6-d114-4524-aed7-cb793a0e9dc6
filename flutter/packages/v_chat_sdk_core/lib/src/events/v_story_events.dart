// Copyright 2023, the hatemragab project author.
// All rights reserved. Use of this source code is governed by a
// MIT license that can be found in the LICENSE file.

import 'package:v_chat_sdk_core/src/models/v_app_event.dart';

/// Base class for all story-related events.
abstract class VStoryEvents extends VAppEvent {
  final String storyId;

  const VStoryEvents({
    required this.storyId,
  });

  @override
  List<Object?> get props => [storyId];
}

/// Event fired when a story is deleted by admin.
class VStoryDeletedEvent extends VStoryEvents {
  final String userId;
  final DateTime deletedAt;
  final String deletedBy;

  const VStoryDeletedEvent({
    required super.storyId,
    required this.userId,
    required this.deletedAt,
    required this.deletedBy,
  });

  @override
  List<Object?> get props => [storyId, userId, deletedAt, deletedBy];
}
