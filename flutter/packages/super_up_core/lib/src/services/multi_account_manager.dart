// Copyright 2023, the hatem<PERSON>b project author.
// All rights reserved. Use of this source code is governed by a
// MIT license that can be found in the LICENSE file.

import 'dart:convert';
import '../models/multi_account/account_session.dart';
import '../models/user/my_profile.dart';
import '../utils/enums.dart';
import '../v_chat/app_pref.dart';
import '../utils/shared.dart';

/// Manages multiple account sessions
class MultiAccountManager {
  static MultiAccountManager? _instance;
  static MultiAccountManager get instance =>
      _instance ??= MultiAccountManager._();

  MultiAccountManager._();

  List<AccountSession> _accounts = [];
  AccountSession? _currentAccount;

  /// Get all stored accounts
  List<AccountSession> get accounts => List.unmodifiable(_accounts);

  /// Get current active account
  AccountSession? get currentAccount => _currentAccount;

  /// Check if user has multiple accounts
  bool get hasMultipleAccounts {
    print(
        'MultiAccountManager: hasMultipleAccounts check - accounts count: ${_accounts.length}');
    return _accounts.length > 1;
  }

  /// Initialize the manager by loading stored accounts
  Future<void> initialize() async {
    await _loadStoredAccounts();
    await _loadCurrentActiveAccount();
  }

  /// Add a new account session
  Future<void> addAccount({
    required String email,
    required String accessToken,
    required SMyProfile profile,
  }) async {
    final accountId =
        AccountSession.createAccountId(email, profile.baseUser.id);

    // Check if account already exists
    final existingIndex =
        _accounts.indexWhere((acc) => acc.accountId == accountId);

    final newSession = AccountSession(
      accountId: accountId,
      email: email,
      accessToken: accessToken,
      profile: profile,
      lastActiveAt: DateTime.now(),
      isActive: false, // Will be set when switching to this account
    );

    if (existingIndex != -1) {
      // Update existing account
      _accounts[existingIndex] = newSession;
      print(
          'MultiAccountManager: Updated existing account. Total accounts: ${_accounts.length}');
    } else {
      // Add new account
      _accounts.add(newSession);
      print(
          'MultiAccountManager: Added new account. Total accounts: ${_accounts.length}');
    }

    await _saveAccounts();
  }

  /// Switch to a specific account
  Future<void> switchToAccount(String accountId) async {
    try {
      final accountIndex =
          _accounts.indexWhere((acc) => acc.accountId == accountId);
      if (accountIndex == -1) {
        throw Exception('Account not found: $accountId');
      }

      // Deactivate all accounts
      for (int i = 0; i < _accounts.length; i++) {
        _accounts[i] = _accounts[i].copyWith(isActive: false);
      }

      // Activate the selected account and update last active time
      _accounts[accountIndex] = _accounts[accountIndex].copyWith(
        isActive: true,
        lastActiveAt: DateTime.now(),
      );

      _currentAccount = _accounts[accountIndex];

      // Update storage
      await _saveAccounts();
      await _saveCurrentActiveAccount();
      await _updateAppAuthAndStorage();
    } catch (e) {
      // If switching fails, try to restore previous state
      await _loadStoredAccounts();
      await _loadCurrentActiveAccount();
      rethrow;
    }
  }

  /// Remove an account
  Future<void> removeAccount(String accountId) async {
    try {
      final accountIndex =
          _accounts.indexWhere((acc) => acc.accountId == accountId);
      if (accountIndex == -1) {
        return; // Account not found
      }

      final wasCurrentAccount = _accounts[accountIndex].isActive;
      _accounts.removeAt(accountIndex);

      if (wasCurrentAccount) {
        _currentAccount = null;
        // If there are other accounts, switch to the most recently used one
        if (_accounts.isNotEmpty) {
          _accounts.sort((a, b) => b.lastActiveAt.compareTo(a.lastActiveAt));
          await switchToAccount(_accounts.first.accountId);
        } else {
          // No accounts left, clear everything
          await _clearAllData();
        }
      }

      await _saveAccounts();
    } catch (e) {
      // If removal fails, try to restore the account list
      await _loadStoredAccounts();
      await _loadCurrentActiveAccount();
      rethrow;
    }
  }

  /// Get account by ID
  AccountSession? getAccount(String accountId) {
    try {
      return _accounts.firstWhere((acc) => acc.accountId == accountId);
    } catch (e) {
      return null;
    }
  }

  /// Update current account profile
  Future<void> updateCurrentAccountProfile(SMyProfile newProfile) async {
    if (_currentAccount == null) return;

    final updatedAccount = _currentAccount!.copyWith(
      profile: newProfile,
      lastActiveAt: DateTime.now(),
    );

    final index = _accounts
        .indexWhere((acc) => acc.accountId == _currentAccount!.accountId);
    if (index != -1) {
      _accounts[index] = updatedAccount;
      _currentAccount = updatedAccount;
      await _saveAccounts();
      await _updateAppAuthAndStorage();
    }
  }

  /// Load stored accounts from preferences
  Future<void> _loadStoredAccounts() async {
    try {
      final accountsJson =
          VAppPref.getStringOrNullKey(SStorageKeys.multiAccountProfiles.name);
      if (accountsJson != null && accountsJson.isNotEmpty) {
        final List<dynamic> accountsList = jsonDecode(accountsJson);
        _accounts =
            accountsList.map((json) => AccountSession.fromMap(json)).toList();
      } else {
        _accounts = [];
      }
    } catch (e) {
      // If loading fails, reset to empty list and clear corrupted data
      _accounts = [];
      await VAppPref.setStringKey(SStorageKeys.multiAccountProfiles.name, '[]');
    }
  }

  /// Load current active account
  Future<void> _loadCurrentActiveAccount() async {
    try {
      final currentAccountId =
          VAppPref.getStringOrNullKey(SStorageKeys.currentActiveAccountId.name);
      if (currentAccountId != null) {
        _currentAccount = _accounts.firstWhere(
          (acc) => acc.accountId == currentAccountId,
          orElse: () => _accounts.isNotEmpty
              ? _accounts.first
              : throw Exception('No accounts'),
        );
      } else if (_accounts.isNotEmpty) {
        _currentAccount = _accounts.first;
      }
    } catch (e) {
      _currentAccount = null;
    }
  }

  /// Save accounts to storage
  Future<void> _saveAccounts() async {
    final accountsJson =
        jsonEncode(_accounts.map((acc) => acc.toMap()).toList());
    await VAppPref.setStringKey(
        SStorageKeys.multiAccountProfiles.name, accountsJson);
  }

  /// Save current active account ID
  Future<void> _saveCurrentActiveAccount() async {
    if (_currentAccount != null) {
      await VAppPref.setStringKey(
          SStorageKeys.currentActiveAccountId.name, _currentAccount!.accountId);
    }
  }

  /// Update AppAuth and storage with current account data
  Future<void> _updateAppAuthAndStorage() async {
    if (_currentAccount == null) return;

    // Update the legacy storage keys for backward compatibility
    await VAppPref.setMap(
        SStorageKeys.myProfile.name, _currentAccount!.profile.toMap());
    await VAppPref.setHashedString(
        SStorageKeys.vAccessToken.name, _currentAccount!.accessToken);
    await VAppPref.setBool(SStorageKeys.isLogin.name, true);

    // Update AppAuth singleton
    AppAuth.setProfileFromSession(_currentAccount!);
  }

  /// Clear all account data
  Future<void> _clearAllData() async {
    _accounts.clear();
    _currentAccount = null;

    await VAppPref.setStringKey(SStorageKeys.multiAccountProfiles.name, '[]');
    await VAppPref.setStringKey(SStorageKeys.currentActiveAccountId.name, '');
    await VAppPref.setBool(SStorageKeys.isLogin.name, false);

    AppAuth.setProfileNull();
  }

  /// Check if an account exists for the given email
  bool hasAccountForEmail(String email) {
    return _accounts
        .any((acc) => acc.email.toLowerCase() == email.toLowerCase());
  }

  /// Get accounts sorted by last active time
  List<AccountSession> getAccountsSortedByLastActive() {
    final sortedAccounts = List<AccountSession>.from(_accounts);
    sortedAccounts.sort((a, b) => b.lastActiveAt.compareTo(a.lastActiveAt));
    return sortedAccounts;
  }
}
