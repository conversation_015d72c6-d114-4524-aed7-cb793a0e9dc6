// // Copyright 2023, the hatemragab project author.
// // All rights reserved. Use of this source code is governed by a
// // MIT license that can be found in the LICENSE file.
//
// import 'package:flutter/cupertino.dart';
// import 'package:flutter/material.dart';
// import 'package:v_chat_sdk_core/v_chat_sdk_core.dart';
// import '../../../../v_chat_message_page.dart';
//
// class VCallPage extends StatefulWidget {
//   final VCallDto dto;
//
//   const VCallPage({
//     Key? key,
//     required this.dto,
//   }) : super(key: key);
//
//   @override
//   State<VCallPage> createState() => _VCallPageState();
// }
//
// class _VCallPageState extends State<VCallPage> {
//   @override
//   Widget build(BuildContext context) {
//     // TODO: implement build
//     throw UnimplementedError();
//   }
//
// }
