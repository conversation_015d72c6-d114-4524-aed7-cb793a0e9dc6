// Copyright 2023, the hate<PERSON><PERSON>b project author.
// All rights reserved. Use of this source code is governed by a
// MIT license that can be found in the LICENSE file.

import 'package:flutter/cupertino.dart';
import 'package:super_up_core/super_up_core.dart';
import 'package:v_chat_message_page/v_chat_message_page.dart';

/// A beautiful encryption banner that displays end-to-end encryption information
/// Matches the app's green theme and design language
class EncryptionBanner extends StatelessWidget {
  final VMessageLocalization language;

  const EncryptionBanner({
    super.key,
    required this.language,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: context.isDark
              ? [
                  const Color(0xFF075E54).withValues(alpha: 0.15),
                  const Color(0xFF008069).withValues(alpha: 0.1),
                ]
              : [
                  const Color(0xFF075E54).withValues(alpha: 0.08),
                  const Color(0xFF008069).withValues(alpha: 0.05),
                ],
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: context.isDark
              ? const Color(0xFF008069).withValues(alpha: 0.3)
              : const Color(0xFF075E54).withValues(alpha: 0.15),
          width: 1.5,
        ),
        boxShadow: [
          BoxShadow(
            color: context.isDark
                ? const Color(0xFF000000).withValues(alpha: 0.2)
                : const Color(0xFF075E54).withValues(alpha: 0.08),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Center(
        child: Text(
          language.chatsAndCallsAreEndToEndEncrypted,
          textAlign: TextAlign.center,
          style: TextStyle(
            color: context.isDark
                ? const Color(0xFFE8F5E8)
                : const Color(0xFF1A4037),
            fontSize: 15,
            fontWeight: FontWeight.w500,
            height: 1.4,
          ),
        ),
      ),
    );
  }
}
